<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout android:orientation="vertical" android:id="@id/container" android:background="@drawable/selector_item_click_bg" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginTop="@dimen/dp_2" android:layout_marginBottom="@dimen/dp_2" android:minHeight="@dimen/dp_65">
        <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/margin_parent" android:layout_marginRight="@dimen/margin_parent" android:layout_weight="1.0">
            <LinearLayout android:orientation="vertical" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginEnd="@dimen/dp_10">
                <ImageView android:id="@id/fileTypeIv" android:layout_width="@dimen/dp_18" android:layout_height="@dimen/dp_18" android:src="@drawable/video" android:scaleType="fitXY" />
                <View android:layout_width="@dimen/dp_1" android:layout_height="@dimen/dp_12" />
            </LinearLayout>
            <LinearLayout android:orientation="vertical" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_weight="1.0">
                <TextView android:id="@id/nameTv" android:layout_width="wrap_content" android:layout_height="wrap_content" style="@style/middle_grey_text_style" />
                <LinearLayout android:orientation="horizontal" android:layout_width="wrap_content" android:layout_height="wrap_content">
                    <TextView android:id="@id/sizeTv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:lines="1" style="@style/small_light_grey_text_style" />
                    <TextView android:id="@id/durationTv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:lines="1" android:layout_marginStart="@dimen/dp_20" style="@style/small_light_grey_text_style" />
                </LinearLayout>
            </LinearLayout>
            <LinearLayout android:gravity="center" android:orientation="vertical" android:id="@id/downloadLine" android:layout_width="wrap_content" android:layout_height="wrap_content">
                <FrameLayout android:layout_width="wrap_content" android:layout_height="wrap_content">
                    <com.example.myapplication.ui.view.CircleProgressBar android:id="@id/progressBar" android:layout_width="@dimen/download_progress_size" android:layout_height="@dimen/download_progress_size" app:backgroundColor="@color/grey" app:backgroundWidth="@dimen/dp_3" app:maxValue="100.0" app:progressColor="@color/blue" app:strokeWidthDimension="@dimen/dp_3" />
                    <ImageView android:layout_gravity="center" android:id="@id/stateIv" android:layout_width="@dimen/download_image_size" android:layout_height="@dimen/download_image_size" android:src="@mipmap/suspend" android:scaleType="fitXY" />
                </FrameLayout>
                <TextView android:id="@id/stateTv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="下载" style="@style/small_light_grey_text_style" />
            </LinearLayout>
        </LinearLayout>
        <View style="@style/thin_dividing_line_style" />
    </LinearLayout>
    <LinearLayout android:layout_gravity="end" android:orientation="horizontal" android:id="@id/slide_line" android:layout_width="@dimen/dp_200" android:layout_height="fill_parent">
        <TextView android:gravity="center" android:id="@id/cancelTv" android:background="@color/textColorGrayA6" android:layout_width="0.0dip" android:layout_height="fill_parent" android:text="@string/text_cancel" android:layout_weight="1.0" android:paddingStart="@dimen/padding_16" android:paddingEnd="@dimen/padding_16" style="@style/middle_white_text_style" />
        <TextView android:gravity="center" android:id="@id/deleteTv" android:background="@color/red" android:layout_width="0.0dip" android:layout_height="fill_parent" android:text="@string/delete" android:layout_weight="1.0" android:paddingStart="@dimen/padding_16" android:paddingEnd="@dimen/padding_16" style="@style/middle_white_text_style" />
    </LinearLayout>
</LinearLayout>
