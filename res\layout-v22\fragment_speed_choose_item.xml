<?xml version="1.0" encoding="utf-8"?>
<TextView android:textAppearance="@style/TextAppearance.AppCompat.Large" android:textSize="@dimen/sp_14" android:textColor="@color/grey_light" android:gravity="center" android:id="@id/text" android:layout_width="fill_parent" android:layout_height="@dimen/dp_50" android:layout_marginLeft="@dimen/dp_10" android:layout_marginTop="@dimen/dp_10" android:layout_marginRight="@dimen/dp_10" android:layout_marginBottom="@dimen/dp_10" android:minWidth="@dimen/dp_55" android:layout_marginHorizontal="@dimen/dp_10" android:layout_marginVertical="@dimen/dp_10"
  xmlns:android="http://schemas.android.com/apk/res/android" />
