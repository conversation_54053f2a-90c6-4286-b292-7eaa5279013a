<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:background="@color/transparent" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout android:gravity="center" android:orientation="vertical" android:id="@id/show_clause_layout" android:background="@drawable/shap_white_round_10dp_bg" android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <TextView android:textSize="@dimen/sp_16" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_margin="@dimen/dp_10" android:text="@string/text_tips_desc" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/black_text_style" />
        <ScrollView android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_weight="1.0" android:paddingHorizontal="@dimen/dp_10">
            <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <TextView android:textSize="@dimen/sp_13" android:gravity="start|center" android:id="@id/tv_text_content" android:scrollbars="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" style="@style/small_grey_text_style" />
                <TextView android:textSize="@dimen/sp_13" android:gravity="start|center" android:scrollbars="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="@string/wxts_text" style="@style/small_grey_text_style" />
            </LinearLayout>
        </ScrollView>
        <LinearLayout android:gravity="start|center" android:id="@id/ll_checkbox" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingHorizontal="@dimen/dp_10">
            <CheckBox android:theme="@style/myCheckBox" android:id="@id/checkbox" android:layout_width="wrap_content" android:layout_height="wrap_content" android:checked="false" />
            <TextView android:textSize="@dimen/sp_13" android:id="@id/tv_text_desc" android:layout_width="fill_parent" android:layout_height="wrap_content" style="@style/small_grey_text_style" />
        </LinearLayout>
        <View android:layout_height="@dimen/dp_1" style="@style/thin_dividing_line_style" />
        <LinearLayout android:gravity="center" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="@dimen/dp_45">
            <TextView android:textColor="@drawable/button_font_style" android:gravity="center" android:id="@id/tv_exit" android:layout_width="0.0dip" android:layout_height="fill_parent" android:text="@string/no_agree" android:layout_weight="1.0" style="@style/middle_theme_color_text_style" />
            <TextView android:enabled="false" android:gravity="center" android:id="@id/tv_agree" android:background="@drawable/agree_button_style" android:layout_width="0.0dip" android:layout_height="fill_parent" android:text="@string/tv_agree" android:layout_weight="1.0" style="@style/middle_white_text_style" />
        </LinearLayout>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
