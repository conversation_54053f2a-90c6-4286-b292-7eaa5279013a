<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:paddingTop="@dimen/dp_15" android:paddingBottom="@dimen/dp_15" android:layout_width="fill_parent" android:layout_height="fill_parent" android:paddingStart="@dimen/dp_15" android:paddingVertical="@dimen/dp_15"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView android:textSize="@dimen/sp_16" android:textColor="@color/textColorBlack" android:id="@id/textView" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="@string/list_course" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout android:id="@id/courseRefresh" android:paddingTop="@dimen/dp_10" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/textView">
        <androidx.recyclerview.widget.RecyclerView android:id="@id/courseRecycler" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
