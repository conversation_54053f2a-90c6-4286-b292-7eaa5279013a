<?xml version="1.0" encoding="utf-8"?>
<CheckedTextView android:ellipsize="marquee" android:gravity="center|left" android:id="@android:id/text1" android:paddingLeft="@dimen/abc_select_dialog_padding_start_material" android:paddingRight="?dialogPreferredPadding" android:layout_width="fill_parent" android:layout_height="wrap_content" android:minHeight="?listPreferredItemHeightSmall" android:drawablePadding="20.0dip" android:textAlignment="viewStart" android:paddingStart="@dimen/abc_select_dialog_padding_start_material" android:paddingEnd="?dialogPreferredPadding" app:drawableLeftCompat="?android:listChoiceIndicatorSingle" app:drawableStartCompat="?android:listChoiceIndicatorSingle"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" />
