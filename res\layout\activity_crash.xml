<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center_horizontal" android:orientation="vertical" android:background="@color/white" android:layout_width="fill_parent" android:layout_height="fill_parent" android:animateLayoutChanges="true"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <TextView android:textSize="@dimen/sp_22" android:textStyle="bold" android:textColor="#ffffffff" android:gravity="center" android:id="@id/rx_crash_tool" android:background="?colorPrimary" android:layout_width="fill_parent" android:layout_height="@dimen/dp_55" android:text="@string/rxcrashtool" />
    <LinearLayout android:layout_gravity="center_vertical" android:orientation="horizontal" android:paddingTop="@dimen/dp_5" android:paddingBottom="@dimen/dp_5" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <ImageView android:id="@id/crash_error_activity_image" android:layout_width="@dimen/dp_50" android:layout_height="@dimen/dp_50" android:src="@mipmap/jc_error_normal" android:contentDescription="@null" />
        <TextView android:textSize="@dimen/sp_18" android:textStyle="bold" android:textColor="#ff707070" android:gravity="center_vertical" android:layout_width="fill_parent" android:layout_height="fill_parent" android:text="@string/crash_error_occurred_explanation" />
    </LinearLayout>
    <Button android:textColor="?colorPrimary" android:id="@id/crash_error_activity_more_info_button" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/crash_error_details" style="?borderlessButtonStyle" />
    <TextView android:textSize="@dimen/sp_14" android:textColor="?colorPrimary" android:id="@id/crash_error_locate_more_info_button" android:background="#55000000" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="@string/crash_error_file" />
    <ScrollView android:paddingTop="@dimen/dp_5" android:paddingBottom="@dimen/dp_5" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_weight="1.0">
        <TextView android:id="@id/crash_error_message" android:scrollbars="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" />
    </ScrollView>
    <LinearLayout android:gravity="center" android:paddingTop="@dimen/dp_5" android:paddingBottom="@dimen/dp_5" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <Button android:textColor="#ffffffff" android:id="@id/share_button" android:background="?colorPrimary" android:paddingLeft="@dimen/dp_15" android:paddingRight="@dimen/dp_15" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/share_log" />
        <Button android:textColor="#ffffffff" android:id="@id/crash_error_activity_restart_button" android:background="?colorPrimary" android:paddingLeft="@dimen/dp_15" android:paddingRight="@dimen/dp_15" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/dp_20" android:layout_marginRight="@dimen/dp_20" android:text="@string/crash_error_restart_app" />
        <Button android:textColor="#ff4b4b4b" android:id="@id/crash_error_activity_close_button" android:background="?colorPrimary" android:paddingLeft="@dimen/dp_15" android:paddingRight="@dimen/dp_15" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/crash_error_close_app" />
    </LinearLayout>
</LinearLayout>
