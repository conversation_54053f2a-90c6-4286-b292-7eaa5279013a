<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:fitsSystemWindows="true" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout android:id="@id/player_frame" android:layout_width="fill_parent" android:layout_height="@dimen/mediaplayer_height">
        <com.example.myapplication.videoplayer.DemoQSVideoView android:id="@id/player" android:background="@color/black" android:layout_width="fill_parent" android:layout_height="fill_parent" />
        <include android:id="@id/history_layout" layout="@layout/player_history_layout" />
    </FrameLayout>
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/menu_view" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout android:id="@id/swiperefreshlayout" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
            <com.example.myapplication.ui.view.SlideRecyclerView android:id="@id/recyclerView" android:layout_width="fill_parent" android:layout_height="fill_parent" />
        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
        <LinearLayout android:gravity="center" android:orientation="vertical" android:id="@id/empty_view" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
            <LinearLayout android:gravity="center" android:orientation="vertical" android:id="@id/net_error" android:paddingTop="@dimen/dp_5" android:paddingBottom="@dimen/dp_5" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:paddingVertical="@dimen/dp_5">
                <ImageView android:id="@id/img_empty" android:paddingTop="@dimen/dp_5" android:paddingBottom="@dimen/dp_5" android:layout_width="@dimen/dp_60" android:layout_height="@dimen/dp_60" android:src="@drawable/icon_net_loss" android:paddingVertical="@dimen/dp_5" />
                <TextView android:textSize="@dimen/sp_16" android:textColor="@color/textColorBlack" android:id="@id/tv_helplees" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/network_helpless" />
            </LinearLayout>
            <TextView android:textSize="@dimen/sp_14" android:textColor="@color/textColorGray" android:id="@id/tv_error" android:layout_width="wrap_content" android:layout_height="wrap_content" />
            <TextView android:textSize="@dimen/sp_14" android:textColor="@color/textColorGray" android:id="@id/tv_refresh" android:paddingTop="@dimen/dp_5" android:paddingBottom="@dimen/dp_5" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/refresh_pull" android:paddingVertical="@dimen/dp_5" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>
