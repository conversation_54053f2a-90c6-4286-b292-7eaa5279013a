<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center_vertical" android:layout_gravity="bottom|center" android:orientation="horizontal" android:id="@id/history_line" android:background="@drawable/history_line_bg_color" android:padding="@dimen/padding_6" android:visibility="invisible" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="@dimen/padding_10"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <ImageView android:id="@id/deleteIv" android:layout_width="@dimen/close_img_size" android:layout_height="@dimen/close_img_size" android:src="@mipmap/close" android:scaleType="fitXY" />
    <TextView android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/last_video" style="@style/middle_light_grey_text_style" />
    <TextView android:ellipsize="end" android:id="@id/filenameTv" android:layout_width="@dimen/filename_width" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/padding_2" android:lines="1" style="@style/middle_white_text_style" />
    <TextView android:id="@id/continueTv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/tips_not_wifi_confirm" style="@style/middle_theme_color_text_style" />
</LinearLayout>
