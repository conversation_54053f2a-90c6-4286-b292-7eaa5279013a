<?xml version="1.0" encoding="utf-8"?>
<resources>
    <public type="anim" name="abc_fade_in" id="0x7f010000" />
    <public type="anim" name="abc_fade_out" id="0x7f010001" />
    <public type="anim" name="abc_grow_fade_in_from_bottom" id="0x7f010002" />
    <public type="anim" name="abc_popup_enter" id="0x7f010003" />
    <public type="anim" name="abc_popup_exit" id="0x7f010004" />
    <public type="anim" name="abc_shrink_fade_out_from_bottom" id="0x7f010005" />
    <public type="anim" name="abc_slide_in_bottom" id="0x7f010006" />
    <public type="anim" name="abc_slide_in_top" id="0x7f010007" />
    <public type="anim" name="abc_slide_out_bottom" id="0x7f010008" />
    <public type="anim" name="abc_slide_out_top" id="0x7f010009" />
    <public type="anim" name="abc_tooltip_enter" id="0x7f01000a" />
    <public type="anim" name="abc_tooltip_exit" id="0x7f01000b" />
    <public type="anim" name="anim_rotate" id="0x7f01000c" />
    <public type="anim" name="btn_checkbox_to_checked_box_inner_merged_animation" id="0x7f01000d" />
    <public type="anim" name="btn_checkbox_to_checked_box_outer_merged_animation" id="0x7f01000e" />
    <public type="anim" name="btn_checkbox_to_checked_icon_null_animation" id="0x7f01000f" />
    <public type="anim" name="btn_checkbox_to_unchecked_box_inner_merged_animation" id="0x7f010010" />
    <public type="anim" name="btn_checkbox_to_unchecked_check_path_merged_animation" id="0x7f010011" />
    <public type="anim" name="btn_checkbox_to_unchecked_icon_null_animation" id="0x7f010012" />
    <public type="anim" name="btn_radio_to_off_mtrl_dot_group_animation" id="0x7f010013" />
    <public type="anim" name="btn_radio_to_off_mtrl_ring_outer_animation" id="0x7f010014" />
    <public type="anim" name="btn_radio_to_off_mtrl_ring_outer_path_animation" id="0x7f010015" />
    <public type="anim" name="btn_radio_to_on_mtrl_dot_group_animation" id="0x7f010016" />
    <public type="anim" name="btn_radio_to_on_mtrl_ring_outer_animation" id="0x7f010017" />
    <public type="anim" name="btn_radio_to_on_mtrl_ring_outer_path_animation" id="0x7f010018" />
    <public type="anim" name="design_bottom_sheet_slide_in" id="0x7f010019" />
    <public type="anim" name="design_bottom_sheet_slide_out" id="0x7f01001a" />
    <public type="anim" name="design_snackbar_in" id="0x7f01001b" />
    <public type="anim" name="design_snackbar_out" id="0x7f01001c" />
    <public type="anim" name="fragment_fast_out_extra_slow_in" id="0x7f01001d" />
    <public type="anim" name="mtrl_bottom_sheet_slide_in" id="0x7f01001e" />
    <public type="anim" name="mtrl_bottom_sheet_slide_out" id="0x7f01001f" />
    <public type="anim" name="mtrl_card_lowers_interpolator" id="0x7f010020" />
    <public type="animator" name="design_appbar_state_list_animator" id="0x7f020000" />
    <public type="animator" name="design_fab_hide_motion_spec" id="0x7f020001" />
    <public type="animator" name="design_fab_show_motion_spec" id="0x7f020002" />
    <public type="animator" name="fragment_close_enter" id="0x7f020003" />
    <public type="animator" name="fragment_close_exit" id="0x7f020004" />
    <public type="animator" name="fragment_fade_enter" id="0x7f020005" />
    <public type="animator" name="fragment_fade_exit" id="0x7f020006" />
    <public type="animator" name="fragment_open_enter" id="0x7f020007" />
    <public type="animator" name="fragment_open_exit" id="0x7f020008" />
    <public type="animator" name="mtrl_btn_state_list_anim" id="0x7f020009" />
    <public type="animator" name="mtrl_btn_unelevated_state_list_anim" id="0x7f02000a" />
    <public type="animator" name="mtrl_card_state_list_anim" id="0x7f02000b" />
    <public type="animator" name="mtrl_chip_state_list_anim" id="0x7f02000c" />
    <public type="animator" name="mtrl_extended_fab_change_size_motion_spec" id="0x7f02000d" />
    <public type="animator" name="mtrl_extended_fab_hide_motion_spec" id="0x7f02000e" />
    <public type="animator" name="mtrl_extended_fab_show_motion_spec" id="0x7f02000f" />
    <public type="animator" name="mtrl_extended_fab_state_list_animator" id="0x7f020010" />
    <public type="animator" name="mtrl_fab_hide_motion_spec" id="0x7f020011" />
    <public type="animator" name="mtrl_fab_show_motion_spec" id="0x7f020012" />
    <public type="animator" name="mtrl_fab_transformation_sheet_collapse_spec" id="0x7f020013" />
    <public type="animator" name="mtrl_fab_transformation_sheet_expand_spec" id="0x7f020014" />
    <public type="array" name="cache_clear_key" id="0x7f030000" />
    <public type="array" name="cache_clear_value" id="0x7f030001" />
    <public type="array" name="cache_size_key" id="0x7f030002" />
    <public type="array" name="cache_size_value" id="0x7f030003" />
    <public type="array" name="long_touch_speed_value" id="0x7f030004" />
    <public type="array" name="multi_language_array" id="0x7f030005" />
    <public type="array" name="pref_entries_pixel_format" id="0x7f030006" />
    <public type="array" name="pref_entries_player" id="0x7f030007" />
    <public type="array" name="pref_entry_summaries_pixel_format" id="0x7f030008" />
    <public type="array" name="pref_entry_summaries_player" id="0x7f030009" />
    <public type="array" name="pref_entry_values_pixel_format" id="0x7f03000a" />
    <public type="array" name="pref_entry_values_player" id="0x7f03000b" />
    <public type="array" name="speed_model" id="0x7f03000c" />
    <public type="array" name="speed_model_index" id="0x7f03000d" />
    <public type="array" name="speed_model_value_mu" id="0x7f03000e" />
    <public type="array" name="speed_model_value_normal" id="0x7f03000f" />
    <public type="attr" name="SharedValue" id="0x7f040000" />
    <public type="attr" name="SharedValueId" id="0x7f040001" />
    <public type="attr" name="actionBarDivider" id="0x7f040002" />
    <public type="attr" name="actionBarItemBackground" id="0x7f040003" />
    <public type="attr" name="actionBarPopupTheme" id="0x7f040004" />
    <public type="attr" name="actionBarSize" id="0x7f040005" />
    <public type="attr" name="actionBarSplitStyle" id="0x7f040006" />
    <public type="attr" name="actionBarStyle" id="0x7f040007" />
    <public type="attr" name="actionBarTabBarStyle" id="0x7f040008" />
    <public type="attr" name="actionBarTabStyle" id="0x7f040009" />
    <public type="attr" name="actionBarTabTextStyle" id="0x7f04000a" />
    <public type="attr" name="actionBarTheme" id="0x7f04000b" />
    <public type="attr" name="actionBarWidgetTheme" id="0x7f04000c" />
    <public type="attr" name="actionButtonStyle" id="0x7f04000d" />
    <public type="attr" name="actionDropDownStyle" id="0x7f04000e" />
    <public type="attr" name="actionLayout" id="0x7f04000f" />
    <public type="attr" name="actionMenuTextAppearance" id="0x7f040010" />
    <public type="attr" name="actionMenuTextColor" id="0x7f040011" />
    <public type="attr" name="actionModeBackground" id="0x7f040012" />
    <public type="attr" name="actionModeCloseButtonStyle" id="0x7f040013" />
    <public type="attr" name="actionModeCloseDrawable" id="0x7f040014" />
    <public type="attr" name="actionModeCopyDrawable" id="0x7f040015" />
    <public type="attr" name="actionModeCutDrawable" id="0x7f040016" />
    <public type="attr" name="actionModeFindDrawable" id="0x7f040017" />
    <public type="attr" name="actionModePasteDrawable" id="0x7f040018" />
    <public type="attr" name="actionModePopupWindowStyle" id="0x7f040019" />
    <public type="attr" name="actionModeSelectAllDrawable" id="0x7f04001a" />
    <public type="attr" name="actionModeShareDrawable" id="0x7f04001b" />
    <public type="attr" name="actionModeSplitBackground" id="0x7f04001c" />
    <public type="attr" name="actionModeStyle" id="0x7f04001d" />
    <public type="attr" name="actionModeWebSearchDrawable" id="0x7f04001e" />
    <public type="attr" name="actionOverflowButtonStyle" id="0x7f04001f" />
    <public type="attr" name="actionOverflowMenuStyle" id="0x7f040020" />
    <public type="attr" name="actionProviderClass" id="0x7f040021" />
    <public type="attr" name="actionTextColorAlpha" id="0x7f040022" />
    <public type="attr" name="actionViewClass" id="0x7f040023" />
    <public type="attr" name="activityChooserViewStyle" id="0x7f040024" />
    <public type="attr" name="adjustable" id="0x7f040025" />
    <public type="attr" name="alertDialogButtonGroupStyle" id="0x7f040026" />
    <public type="attr" name="alertDialogCenterButtons" id="0x7f040027" />
    <public type="attr" name="alertDialogStyle" id="0x7f040028" />
    <public type="attr" name="alertDialogTheme" id="0x7f040029" />
    <public type="attr" name="alignContent" id="0x7f04002a" />
    <public type="attr" name="alignItems" id="0x7f04002b" />
    <public type="attr" name="allowDividerAbove" id="0x7f04002c" />
    <public type="attr" name="allowDividerAfterLastItem" id="0x7f04002d" />
    <public type="attr" name="allowDividerBelow" id="0x7f04002e" />
    <public type="attr" name="allowStacking" id="0x7f04002f" />
    <public type="attr" name="alpha" id="0x7f040030" />
    <public type="attr" name="alphabeticModifiers" id="0x7f040031" />
    <public type="attr" name="altSrc" id="0x7f040032" />
    <public type="attr" name="animateCircleAngleTo" id="0x7f040033" />
    <public type="attr" name="animateRelativeTo" id="0x7f040034" />
    <public type="attr" name="animationMode" id="0x7f040035" />
    <public type="attr" name="appBarLayoutStyle" id="0x7f040036" />
    <public type="attr" name="applyMotionScene" id="0x7f040037" />
    <public type="attr" name="arcMode" id="0x7f040038" />
    <public type="attr" name="arrowHeadLength" id="0x7f040039" />
    <public type="attr" name="arrowShaftLength" id="0x7f04003a" />
    <public type="attr" name="attributeName" id="0x7f04003b" />
    <public type="attr" name="autoCompleteMode" id="0x7f04003c" />
    <public type="attr" name="autoCompleteTextViewStyle" id="0x7f04003d" />
    <public type="attr" name="autoSizeMaxTextSize" id="0x7f04003e" />
    <public type="attr" name="autoSizeMinTextSize" id="0x7f04003f" />
    <public type="attr" name="autoSizePresetSizes" id="0x7f040040" />
    <public type="attr" name="autoSizeStepGranularity" id="0x7f040041" />
    <public type="attr" name="autoSizeTextType" id="0x7f040042" />
    <public type="attr" name="autoTransition" id="0x7f040043" />
    <public type="attr" name="background" id="0x7f040044" />
    <public type="attr" name="backgroundColor" id="0x7f040045" />
    <public type="attr" name="backgroundInsetBottom" id="0x7f040046" />
    <public type="attr" name="backgroundInsetEnd" id="0x7f040047" />
    <public type="attr" name="backgroundInsetStart" id="0x7f040048" />
    <public type="attr" name="backgroundInsetTop" id="0x7f040049" />
    <public type="attr" name="backgroundOverlayColorAlpha" id="0x7f04004a" />
    <public type="attr" name="backgroundSplit" id="0x7f04004b" />
    <public type="attr" name="backgroundStacked" id="0x7f04004c" />
    <public type="attr" name="backgroundTint" id="0x7f04004d" />
    <public type="attr" name="backgroundTintMode" id="0x7f04004e" />
    <public type="attr" name="backgroundWidth" id="0x7f04004f" />
    <public type="attr" name="badgeGravity" id="0x7f040050" />
    <public type="attr" name="badgeStyle" id="0x7f040051" />
    <public type="attr" name="badgeTextColor" id="0x7f040052" />
    <public type="attr" name="banner_auto_loop" id="0x7f040053" />
    <public type="attr" name="banner_indicator_gravity" id="0x7f040054" />
    <public type="attr" name="banner_indicator_height" id="0x7f040055" />
    <public type="attr" name="banner_indicator_margin" id="0x7f040056" />
    <public type="attr" name="banner_indicator_marginBottom" id="0x7f040057" />
    <public type="attr" name="banner_indicator_marginLeft" id="0x7f040058" />
    <public type="attr" name="banner_indicator_marginRight" id="0x7f040059" />
    <public type="attr" name="banner_indicator_marginTop" id="0x7f04005a" />
    <public type="attr" name="banner_indicator_normal_color" id="0x7f04005b" />
    <public type="attr" name="banner_indicator_normal_width" id="0x7f04005c" />
    <public type="attr" name="banner_indicator_radius" id="0x7f04005d" />
    <public type="attr" name="banner_indicator_selected_color" id="0x7f04005e" />
    <public type="attr" name="banner_indicator_selected_width" id="0x7f04005f" />
    <public type="attr" name="banner_indicator_space" id="0x7f040060" />
    <public type="attr" name="banner_infinite_loop" id="0x7f040061" />
    <public type="attr" name="banner_loop_time" id="0x7f040062" />
    <public type="attr" name="banner_orientation" id="0x7f040063" />
    <public type="attr" name="banner_radius" id="0x7f040064" />
    <public type="attr" name="banner_round_bottom_left" id="0x7f040065" />
    <public type="attr" name="banner_round_bottom_right" id="0x7f040066" />
    <public type="attr" name="banner_round_top_left" id="0x7f040067" />
    <public type="attr" name="banner_round_top_right" id="0x7f040068" />
    <public type="attr" name="barLength" id="0x7f040069" />
    <public type="attr" name="barrierAllowsGoneWidgets" id="0x7f04006a" />
    <public type="attr" name="barrierDirection" id="0x7f04006b" />
    <public type="attr" name="barrierMargin" id="0x7f04006c" />
    <public type="attr" name="behavior_autoHide" id="0x7f04006d" />
    <public type="attr" name="behavior_autoShrink" id="0x7f04006e" />
    <public type="attr" name="behavior_expandedOffset" id="0x7f04006f" />
    <public type="attr" name="behavior_fitToContents" id="0x7f040070" />
    <public type="attr" name="behavior_halfExpandedRatio" id="0x7f040071" />
    <public type="attr" name="behavior_hideable" id="0x7f040072" />
    <public type="attr" name="behavior_overlapTop" id="0x7f040073" />
    <public type="attr" name="behavior_peekHeight" id="0x7f040074" />
    <public type="attr" name="behavior_saveFlags" id="0x7f040075" />
    <public type="attr" name="behavior_skipCollapsed" id="0x7f040076" />
    <public type="attr" name="blendSrc" id="0x7f040077" />
    <public type="attr" name="borderRound" id="0x7f040078" />
    <public type="attr" name="borderRoundPercent" id="0x7f040079" />
    <public type="attr" name="borderWidth" id="0x7f04007a" />
    <public type="attr" name="borderlessButtonStyle" id="0x7f04007b" />
    <public type="attr" name="bottomAppBarStyle" id="0x7f04007c" />
    <public type="attr" name="bottomNavigationStyle" id="0x7f04007d" />
    <public type="attr" name="bottomSheetDialogTheme" id="0x7f04007e" />
    <public type="attr" name="bottomSheetStyle" id="0x7f04007f" />
    <public type="attr" name="boxBackgroundColor" id="0x7f040080" />
    <public type="attr" name="boxBackgroundMode" id="0x7f040081" />
    <public type="attr" name="boxCollapsedPaddingTop" id="0x7f040082" />
    <public type="attr" name="boxCornerRadiusBottomEnd" id="0x7f040083" />
    <public type="attr" name="boxCornerRadiusBottomStart" id="0x7f040084" />
    <public type="attr" name="boxCornerRadiusTopEnd" id="0x7f040085" />
    <public type="attr" name="boxCornerRadiusTopStart" id="0x7f040086" />
    <public type="attr" name="boxStrokeColor" id="0x7f040087" />
    <public type="attr" name="boxStrokeWidth" id="0x7f040088" />
    <public type="attr" name="boxStrokeWidthFocused" id="0x7f040089" />
    <public type="attr" name="brightness" id="0x7f04008a" />
    <public type="attr" name="buttonBarButtonStyle" id="0x7f04008b" />
    <public type="attr" name="buttonBarNegativeButtonStyle" id="0x7f04008c" />
    <public type="attr" name="buttonBarNeutralButtonStyle" id="0x7f04008d" />
    <public type="attr" name="buttonBarPositiveButtonStyle" id="0x7f04008e" />
    <public type="attr" name="buttonBarStyle" id="0x7f04008f" />
    <public type="attr" name="buttonCompat" id="0x7f040090" />
    <public type="attr" name="buttonGravity" id="0x7f040091" />
    <public type="attr" name="buttonIconDimen" id="0x7f040092" />
    <public type="attr" name="buttonPanelSideLayout" id="0x7f040093" />
    <public type="attr" name="buttonStyle" id="0x7f040094" />
    <public type="attr" name="buttonStyleSmall" id="0x7f040095" />
    <public type="attr" name="buttonTint" id="0x7f040096" />
    <public type="attr" name="buttonTintMode" id="0x7f040097" />
    <public type="attr" name="cardBackgroundColor" id="0x7f040098" />
    <public type="attr" name="cardCornerRadius" id="0x7f040099" />
    <public type="attr" name="cardElevation" id="0x7f04009a" />
    <public type="attr" name="cardForegroundColor" id="0x7f04009b" />
    <public type="attr" name="cardMaxElevation" id="0x7f04009c" />
    <public type="attr" name="cardPreventCornerOverlap" id="0x7f04009d" />
    <public type="attr" name="cardUseCompatPadding" id="0x7f04009e" />
    <public type="attr" name="cardViewStyle" id="0x7f04009f" />
    <public type="attr" name="carousel_backwardTransition" id="0x7f0400a0" />
    <public type="attr" name="carousel_emptyViewsBehavior" id="0x7f0400a1" />
    <public type="attr" name="carousel_firstView" id="0x7f0400a2" />
    <public type="attr" name="carousel_forwardTransition" id="0x7f0400a3" />
    <public type="attr" name="carousel_infinite" id="0x7f0400a4" />
    <public type="attr" name="carousel_nextState" id="0x7f0400a5" />
    <public type="attr" name="carousel_previousState" id="0x7f0400a6" />
    <public type="attr" name="carousel_touchUpMode" id="0x7f0400a7" />
    <public type="attr" name="carousel_touchUp_dampeningFactor" id="0x7f0400a8" />
    <public type="attr" name="carousel_touchUp_velocityThreshold" id="0x7f0400a9" />
    <public type="attr" name="chainUseRtl" id="0x7f0400aa" />
    <public type="attr" name="checkBoxPreferenceStyle" id="0x7f0400ab" />
    <public type="attr" name="checkboxStyle" id="0x7f0400ac" />
    <public type="attr" name="checkedButton" id="0x7f0400ad" />
    <public type="attr" name="checkedChip" id="0x7f0400ae" />
    <public type="attr" name="checkedIcon" id="0x7f0400af" />
    <public type="attr" name="checkedIconEnabled" id="0x7f0400b0" />
    <public type="attr" name="checkedIconTint" id="0x7f0400b1" />
    <public type="attr" name="checkedIconVisible" id="0x7f0400b2" />
    <public type="attr" name="checkedTextViewStyle" id="0x7f0400b3" />
    <public type="attr" name="chipBackgroundColor" id="0x7f0400b4" />
    <public type="attr" name="chipCornerRadius" id="0x7f0400b5" />
    <public type="attr" name="chipEndPadding" id="0x7f0400b6" />
    <public type="attr" name="chipGroupStyle" id="0x7f0400b7" />
    <public type="attr" name="chipIcon" id="0x7f0400b8" />
    <public type="attr" name="chipIconEnabled" id="0x7f0400b9" />
    <public type="attr" name="chipIconSize" id="0x7f0400ba" />
    <public type="attr" name="chipIconTint" id="0x7f0400bb" />
    <public type="attr" name="chipIconVisible" id="0x7f0400bc" />
    <public type="attr" name="chipMinHeight" id="0x7f0400bd" />
    <public type="attr" name="chipMinTouchTargetSize" id="0x7f0400be" />
    <public type="attr" name="chipSpacing" id="0x7f0400bf" />
    <public type="attr" name="chipSpacingHorizontal" id="0x7f0400c0" />
    <public type="attr" name="chipSpacingVertical" id="0x7f0400c1" />
    <public type="attr" name="chipStandaloneStyle" id="0x7f0400c2" />
    <public type="attr" name="chipStartPadding" id="0x7f0400c3" />
    <public type="attr" name="chipStrokeColor" id="0x7f0400c4" />
    <public type="attr" name="chipStrokeWidth" id="0x7f0400c5" />
    <public type="attr" name="chipStyle" id="0x7f0400c6" />
    <public type="attr" name="chipSurfaceColor" id="0x7f0400c7" />
    <public type="attr" name="circleRadius" id="0x7f0400c8" />
    <public type="attr" name="circularflow_angles" id="0x7f0400c9" />
    <public type="attr" name="circularflow_defaultAngle" id="0x7f0400ca" />
    <public type="attr" name="circularflow_defaultRadius" id="0x7f0400cb" />
    <public type="attr" name="circularflow_radiusInDP" id="0x7f0400cc" />
    <public type="attr" name="circularflow_viewCenter" id="0x7f0400cd" />
    <public type="attr" name="clearsTag" id="0x7f0400ce" />
    <public type="attr" name="clickAction" id="0x7f0400cf" />
    <public type="attr" name="closeIcon" id="0x7f0400d0" />
    <public type="attr" name="closeIconEnabled" id="0x7f0400d1" />
    <public type="attr" name="closeIconEndPadding" id="0x7f0400d2" />
    <public type="attr" name="closeIconSize" id="0x7f0400d3" />
    <public type="attr" name="closeIconStartPadding" id="0x7f0400d4" />
    <public type="attr" name="closeIconTint" id="0x7f0400d5" />
    <public type="attr" name="closeIconVisible" id="0x7f0400d6" />
    <public type="attr" name="closeItemLayout" id="0x7f0400d7" />
    <public type="attr" name="collapseContentDescription" id="0x7f0400d8" />
    <public type="attr" name="collapseIcon" id="0x7f0400d9" />
    <public type="attr" name="collapsedTitleGravity" id="0x7f0400da" />
    <public type="attr" name="collapsedTitleTextAppearance" id="0x7f0400db" />
    <public type="attr" name="color" id="0x7f0400dc" />
    <public type="attr" name="colorAccent" id="0x7f0400dd" />
    <public type="attr" name="colorBackgroundFloating" id="0x7f0400de" />
    <public type="attr" name="colorButtonNormal" id="0x7f0400df" />
    <public type="attr" name="colorControlActivated" id="0x7f0400e0" />
    <public type="attr" name="colorControlHighlight" id="0x7f0400e1" />
    <public type="attr" name="colorControlNormal" id="0x7f0400e2" />
    <public type="attr" name="colorError" id="0x7f0400e3" />
    <public type="attr" name="colorOnBackground" id="0x7f0400e4" />
    <public type="attr" name="colorOnError" id="0x7f0400e5" />
    <public type="attr" name="colorOnPrimary" id="0x7f0400e6" />
    <public type="attr" name="colorOnPrimarySurface" id="0x7f0400e7" />
    <public type="attr" name="colorOnSecondary" id="0x7f0400e8" />
    <public type="attr" name="colorOnSurface" id="0x7f0400e9" />
    <public type="attr" name="colorPrimary" id="0x7f0400ea" />
    <public type="attr" name="colorPrimaryDark" id="0x7f0400eb" />
    <public type="attr" name="colorPrimarySurface" id="0x7f0400ec" />
    <public type="attr" name="colorPrimaryVariant" id="0x7f0400ed" />
    <public type="attr" name="colorSecondary" id="0x7f0400ee" />
    <public type="attr" name="colorSecondaryVariant" id="0x7f0400ef" />
    <public type="attr" name="colorSurface" id="0x7f0400f0" />
    <public type="attr" name="colorSwitchThumbNormal" id="0x7f0400f1" />
    <public type="attr" name="commitIcon" id="0x7f0400f2" />
    <public type="attr" name="constraintRotate" id="0x7f0400f3" />
    <public type="attr" name="constraintSet" id="0x7f0400f4" />
    <public type="attr" name="constraintSetEnd" id="0x7f0400f5" />
    <public type="attr" name="constraintSetStart" id="0x7f0400f6" />
    <public type="attr" name="constraint_referenced_ids" id="0x7f0400f7" />
    <public type="attr" name="constraint_referenced_tags" id="0x7f0400f8" />
    <public type="attr" name="constraints" id="0x7f0400f9" />
    <public type="attr" name="content" id="0x7f0400fa" />
    <public type="attr" name="contentDescription" id="0x7f0400fb" />
    <public type="attr" name="contentInsetEnd" id="0x7f0400fc" />
    <public type="attr" name="contentInsetEndWithActions" id="0x7f0400fd" />
    <public type="attr" name="contentInsetLeft" id="0x7f0400fe" />
    <public type="attr" name="contentInsetRight" id="0x7f0400ff" />
    <public type="attr" name="contentInsetStart" id="0x7f040100" />
    <public type="attr" name="contentInsetStartWithNavigation" id="0x7f040101" />
    <public type="attr" name="contentPadding" id="0x7f040102" />
    <public type="attr" name="contentPaddingBottom" id="0x7f040103" />
    <public type="attr" name="contentPaddingLeft" id="0x7f040104" />
    <public type="attr" name="contentPaddingRight" id="0x7f040105" />
    <public type="attr" name="contentPaddingTop" id="0x7f040106" />
    <public type="attr" name="contentScrim" id="0x7f040107" />
    <public type="attr" name="contrast" id="0x7f040108" />
    <public type="attr" name="controlBackground" id="0x7f040109" />
    <public type="attr" name="coordinatorLayoutStyle" id="0x7f04010a" />
    <public type="attr" name="cornerFamily" id="0x7f04010b" />
    <public type="attr" name="cornerFamilyBottomLeft" id="0x7f04010c" />
    <public type="attr" name="cornerFamilyBottomRight" id="0x7f04010d" />
    <public type="attr" name="cornerFamilyTopLeft" id="0x7f04010e" />
    <public type="attr" name="cornerFamilyTopRight" id="0x7f04010f" />
    <public type="attr" name="cornerRadius" id="0x7f040110" />
    <public type="attr" name="cornerSize" id="0x7f040111" />
    <public type="attr" name="cornerSizeBottomLeft" id="0x7f040112" />
    <public type="attr" name="cornerSizeBottomRight" id="0x7f040113" />
    <public type="attr" name="cornerSizeTopLeft" id="0x7f040114" />
    <public type="attr" name="cornerSizeTopRight" id="0x7f040115" />
    <public type="attr" name="counterEnabled" id="0x7f040116" />
    <public type="attr" name="counterMaxLength" id="0x7f040117" />
    <public type="attr" name="counterOverflowTextAppearance" id="0x7f040118" />
    <public type="attr" name="counterOverflowTextColor" id="0x7f040119" />
    <public type="attr" name="counterTextAppearance" id="0x7f04011a" />
    <public type="attr" name="counterTextColor" id="0x7f04011b" />
    <public type="attr" name="crossfade" id="0x7f04011c" />
    <public type="attr" name="currentState" id="0x7f04011d" />
    <public type="attr" name="curveFit" id="0x7f04011e" />
    <public type="attr" name="customBoolean" id="0x7f04011f" />
    <public type="attr" name="customColorDrawableValue" id="0x7f040120" />
    <public type="attr" name="customColorValue" id="0x7f040121" />
    <public type="attr" name="customDimension" id="0x7f040122" />
    <public type="attr" name="customFloatValue" id="0x7f040123" />
    <public type="attr" name="customIntegerValue" id="0x7f040124" />
    <public type="attr" name="customNavigationLayout" id="0x7f040125" />
    <public type="attr" name="customPixelDimension" id="0x7f040126" />
    <public type="attr" name="customReference" id="0x7f040127" />
    <public type="attr" name="customStringValue" id="0x7f040128" />
    <public type="attr" name="dayInvalidStyle" id="0x7f040129" />
    <public type="attr" name="daySelectedStyle" id="0x7f04012a" />
    <public type="attr" name="dayStyle" id="0x7f04012b" />
    <public type="attr" name="dayTodayStyle" id="0x7f04012c" />
    <public type="attr" name="defaultDuration" id="0x7f04012d" />
    <public type="attr" name="defaultQueryHint" id="0x7f04012e" />
    <public type="attr" name="defaultState" id="0x7f04012f" />
    <public type="attr" name="defaultValue" id="0x7f040130" />
    <public type="attr" name="deltaPolarAngle" id="0x7f040131" />
    <public type="attr" name="deltaPolarRadius" id="0x7f040132" />
    <public type="attr" name="dependency" id="0x7f040133" />
    <public type="attr" name="deriveConstraintsFrom" id="0x7f040134" />
    <public type="attr" name="dialogCornerRadius" id="0x7f040135" />
    <public type="attr" name="dialogIcon" id="0x7f040136" />
    <public type="attr" name="dialogLayout" id="0x7f040137" />
    <public type="attr" name="dialogMessage" id="0x7f040138" />
    <public type="attr" name="dialogPreferenceStyle" id="0x7f040139" />
    <public type="attr" name="dialogPreferredPadding" id="0x7f04013a" />
    <public type="attr" name="dialogTheme" id="0x7f04013b" />
    <public type="attr" name="dialogTitle" id="0x7f04013c" />
    <public type="attr" name="disableDependentsState" id="0x7f04013d" />
    <public type="attr" name="displayOptions" id="0x7f04013e" />
    <public type="attr" name="divider" id="0x7f04013f" />
    <public type="attr" name="dividerDrawable" id="0x7f040140" />
    <public type="attr" name="dividerDrawableHorizontal" id="0x7f040141" />
    <public type="attr" name="dividerDrawableVertical" id="0x7f040142" />
    <public type="attr" name="dividerHorizontal" id="0x7f040143" />
    <public type="attr" name="dividerPadding" id="0x7f040144" />
    <public type="attr" name="dividerVertical" id="0x7f040145" />
    <public type="attr" name="dragDirection" id="0x7f040146" />
    <public type="attr" name="dragScale" id="0x7f040147" />
    <public type="attr" name="dragThreshold" id="0x7f040148" />
    <public type="attr" name="drawPath" id="0x7f040149" />
    <public type="attr" name="drawableBottomCompat" id="0x7f04014a" />
    <public type="attr" name="drawableEndCompat" id="0x7f04014b" />
    <public type="attr" name="drawableLeftCompat" id="0x7f04014c" />
    <public type="attr" name="drawableRightCompat" id="0x7f04014d" />
    <public type="attr" name="drawableSize" id="0x7f04014e" />
    <public type="attr" name="drawableStartCompat" id="0x7f04014f" />
    <public type="attr" name="drawableTint" id="0x7f040150" />
    <public type="attr" name="drawableTintMode" id="0x7f040151" />
    <public type="attr" name="drawableTopCompat" id="0x7f040152" />
    <public type="attr" name="drawerArrowStyle" id="0x7f040153" />
    <public type="attr" name="dropDownListViewStyle" id="0x7f040154" />
    <public type="attr" name="dropdownListPreferredItemHeight" id="0x7f040155" />
    <public type="attr" name="dropdownPreferenceStyle" id="0x7f040156" />
    <public type="attr" name="duration" id="0x7f040157" />
    <public type="attr" name="editTextBackground" id="0x7f040158" />
    <public type="attr" name="editTextColor" id="0x7f040159" />
    <public type="attr" name="editTextPreferenceStyle" id="0x7f04015a" />
    <public type="attr" name="editTextStyle" id="0x7f04015b" />
    <public type="attr" name="elevation" id="0x7f04015c" />
    <public type="attr" name="elevationOverlayColor" id="0x7f04015d" />
    <public type="attr" name="elevationOverlayEnabled" id="0x7f04015e" />
    <public type="attr" name="enableCopying" id="0x7f04015f" />
    <public type="attr" name="enabled" id="0x7f040160" />
    <public type="attr" name="endIconCheckable" id="0x7f040161" />
    <public type="attr" name="endIconContentDescription" id="0x7f040162" />
    <public type="attr" name="endIconDrawable" id="0x7f040163" />
    <public type="attr" name="endIconMode" id="0x7f040164" />
    <public type="attr" name="endIconTint" id="0x7f040165" />
    <public type="attr" name="endIconTintMode" id="0x7f040166" />
    <public type="attr" name="enforceMaterialTheme" id="0x7f040167" />
    <public type="attr" name="enforceTextAppearance" id="0x7f040168" />
    <public type="attr" name="ensureMinTouchTargetSize" id="0x7f040169" />
    <public type="attr" name="entries" id="0x7f04016a" />
    <public type="attr" name="entrySummaries" id="0x7f04016b" />
    <public type="attr" name="entryValues" id="0x7f04016c" />
    <public type="attr" name="errorEnabled" id="0x7f04016d" />
    <public type="attr" name="errorIconDrawable" id="0x7f04016e" />
    <public type="attr" name="errorIconTint" id="0x7f04016f" />
    <public type="attr" name="errorIconTintMode" id="0x7f040170" />
    <public type="attr" name="errorTextAppearance" id="0x7f040171" />
    <public type="attr" name="errorTextColor" id="0x7f040172" />
    <public type="attr" name="expandActivityOverflowButtonDrawable" id="0x7f040173" />
    <public type="attr" name="expanded" id="0x7f040174" />
    <public type="attr" name="expandedTitleGravity" id="0x7f040175" />
    <public type="attr" name="expandedTitleMargin" id="0x7f040176" />
    <public type="attr" name="expandedTitleMarginBottom" id="0x7f040177" />
    <public type="attr" name="expandedTitleMarginEnd" id="0x7f040178" />
    <public type="attr" name="expandedTitleMarginStart" id="0x7f040179" />
    <public type="attr" name="expandedTitleMarginTop" id="0x7f04017a" />
    <public type="attr" name="expandedTitleTextAppearance" id="0x7f04017b" />
    <public type="attr" name="extendMotionSpec" id="0x7f04017c" />
    <public type="attr" name="extendedFloatingActionButtonStyle" id="0x7f04017d" />
    <public type="attr" name="fabAlignmentMode" id="0x7f04017e" />
    <public type="attr" name="fabAnimationMode" id="0x7f04017f" />
    <public type="attr" name="fabCradleMargin" id="0x7f040180" />
    <public type="attr" name="fabCradleRoundedCornerRadius" id="0x7f040181" />
    <public type="attr" name="fabCradleVerticalOffset" id="0x7f040182" />
    <public type="attr" name="fabCustomSize" id="0x7f040183" />
    <public type="attr" name="fabSize" id="0x7f040184" />
    <public type="attr" name="fastScrollEnabled" id="0x7f040185" />
    <public type="attr" name="fastScrollHorizontalThumbDrawable" id="0x7f040186" />
    <public type="attr" name="fastScrollHorizontalTrackDrawable" id="0x7f040187" />
    <public type="attr" name="fastScrollVerticalThumbDrawable" id="0x7f040188" />
    <public type="attr" name="fastScrollVerticalTrackDrawable" id="0x7f040189" />
    <public type="attr" name="firstBaselineToTopHeight" id="0x7f04018a" />
    <public type="attr" name="flexDirection" id="0x7f04018b" />
    <public type="attr" name="flexWrap" id="0x7f04018c" />
    <public type="attr" name="floatingActionButtonStyle" id="0x7f04018d" />
    <public type="attr" name="flow_firstHorizontalBias" id="0x7f04018e" />
    <public type="attr" name="flow_firstHorizontalStyle" id="0x7f04018f" />
    <public type="attr" name="flow_firstVerticalBias" id="0x7f040190" />
    <public type="attr" name="flow_firstVerticalStyle" id="0x7f040191" />
    <public type="attr" name="flow_horizontalAlign" id="0x7f040192" />
    <public type="attr" name="flow_horizontalBias" id="0x7f040193" />
    <public type="attr" name="flow_horizontalGap" id="0x7f040194" />
    <public type="attr" name="flow_horizontalStyle" id="0x7f040195" />
    <public type="attr" name="flow_lastHorizontalBias" id="0x7f040196" />
    <public type="attr" name="flow_lastHorizontalStyle" id="0x7f040197" />
    <public type="attr" name="flow_lastVerticalBias" id="0x7f040198" />
    <public type="attr" name="flow_lastVerticalStyle" id="0x7f040199" />
    <public type="attr" name="flow_maxElementsWrap" id="0x7f04019a" />
    <public type="attr" name="flow_padding" id="0x7f04019b" />
    <public type="attr" name="flow_verticalAlign" id="0x7f04019c" />
    <public type="attr" name="flow_verticalBias" id="0x7f04019d" />
    <public type="attr" name="flow_verticalGap" id="0x7f04019e" />
    <public type="attr" name="flow_verticalStyle" id="0x7f04019f" />
    <public type="attr" name="flow_wrapMode" id="0x7f0401a0" />
    <public type="attr" name="font" id="0x7f0401a1" />
    <public type="attr" name="fontFamily" id="0x7f0401a2" />
    <public type="attr" name="fontProviderAuthority" id="0x7f0401a3" />
    <public type="attr" name="fontProviderCerts" id="0x7f0401a4" />
    <public type="attr" name="fontProviderFetchStrategy" id="0x7f0401a5" />
    <public type="attr" name="fontProviderFetchTimeout" id="0x7f0401a6" />
    <public type="attr" name="fontProviderPackage" id="0x7f0401a7" />
    <public type="attr" name="fontProviderQuery" id="0x7f0401a8" />
    <public type="attr" name="fontProviderSystemFontFamily" id="0x7f0401a9" />
    <public type="attr" name="fontStyle" id="0x7f0401aa" />
    <public type="attr" name="fontVariationSettings" id="0x7f0401ab" />
    <public type="attr" name="fontWeight" id="0x7f0401ac" />
    <public type="attr" name="foregroundInsidePadding" id="0x7f0401ad" />
    <public type="attr" name="fragment" id="0x7f0401ae" />
    <public type="attr" name="framePosition" id="0x7f0401af" />
    <public type="attr" name="gapBetweenBars" id="0x7f0401b0" />
    <public type="attr" name="goIcon" id="0x7f0401b1" />
    <public type="attr" name="guidelineUseRtl" id="0x7f0401b2" />
    <public type="attr" name="headerLayout" id="0x7f0401b3" />
    <public type="attr" name="height" id="0x7f0401b4" />
    <public type="attr" name="helperText" id="0x7f0401b5" />
    <public type="attr" name="helperTextEnabled" id="0x7f0401b6" />
    <public type="attr" name="helperTextTextAppearance" id="0x7f0401b7" />
    <public type="attr" name="helperTextTextColor" id="0x7f0401b8" />
    <public type="attr" name="hideMotionSpec" id="0x7f0401b9" />
    <public type="attr" name="hideOnContentScroll" id="0x7f0401ba" />
    <public type="attr" name="hideOnScroll" id="0x7f0401bb" />
    <public type="attr" name="hintAnimationEnabled" id="0x7f0401bc" />
    <public type="attr" name="hintEnabled" id="0x7f0401bd" />
    <public type="attr" name="hintTextAppearance" id="0x7f0401be" />
    <public type="attr" name="hintTextColor" id="0x7f0401bf" />
    <public type="attr" name="homeAsUpIndicator" id="0x7f0401c0" />
    <public type="attr" name="homeLayout" id="0x7f0401c1" />
    <public type="attr" name="hoveredFocusedTranslationZ" id="0x7f0401c2" />
    <public type="attr" name="icon" id="0x7f0401c3" />
    <public type="attr" name="iconEndPadding" id="0x7f0401c4" />
    <public type="attr" name="iconGravity" id="0x7f0401c5" />
    <public type="attr" name="iconPadding" id="0x7f0401c6" />
    <public type="attr" name="iconSize" id="0x7f0401c7" />
    <public type="attr" name="iconSpaceReserved" id="0x7f0401c8" />
    <public type="attr" name="iconStartPadding" id="0x7f0401c9" />
    <public type="attr" name="iconTint" id="0x7f0401ca" />
    <public type="attr" name="iconTintMode" id="0x7f0401cb" />
    <public type="attr" name="iconifiedByDefault" id="0x7f0401cc" />
    <public type="attr" name="ifTagNotSet" id="0x7f0401cd" />
    <public type="attr" name="ifTagSet" id="0x7f0401ce" />
    <public type="attr" name="imageButtonStyle" id="0x7f0401cf" />
    <public type="attr" name="imagePanX" id="0x7f0401d0" />
    <public type="attr" name="imagePanY" id="0x7f0401d1" />
    <public type="attr" name="imageRotate" id="0x7f0401d2" />
    <public type="attr" name="imageZoom" id="0x7f0401d3" />
    <public type="attr" name="indeterminateProgressStyle" id="0x7f0401d4" />
    <public type="attr" name="initialActivityCount" id="0x7f0401d5" />
    <public type="attr" name="initialExpandedChildrenCount" id="0x7f0401d6" />
    <public type="attr" name="insetForeground" id="0x7f0401d7" />
    <public type="attr" name="isLightTheme" id="0x7f0401d8" />
    <public type="attr" name="isMaterialTheme" id="0x7f0401d9" />
    <public type="attr" name="isPreferenceVisible" id="0x7f0401da" />
    <public type="attr" name="itemBackground" id="0x7f0401db" />
    <public type="attr" name="itemFillColor" id="0x7f0401dc" />
    <public type="attr" name="itemHorizontalPadding" id="0x7f0401dd" />
    <public type="attr" name="itemHorizontalTranslationEnabled" id="0x7f0401de" />
    <public type="attr" name="itemIconPadding" id="0x7f0401df" />
    <public type="attr" name="itemIconSize" id="0x7f0401e0" />
    <public type="attr" name="itemIconTint" id="0x7f0401e1" />
    <public type="attr" name="itemMaxLines" id="0x7f0401e2" />
    <public type="attr" name="itemPadding" id="0x7f0401e3" />
    <public type="attr" name="itemRippleColor" id="0x7f0401e4" />
    <public type="attr" name="itemShapeAppearance" id="0x7f0401e5" />
    <public type="attr" name="itemShapeAppearanceOverlay" id="0x7f0401e6" />
    <public type="attr" name="itemShapeFillColor" id="0x7f0401e7" />
    <public type="attr" name="itemShapeInsetBottom" id="0x7f0401e8" />
    <public type="attr" name="itemShapeInsetEnd" id="0x7f0401e9" />
    <public type="attr" name="itemShapeInsetStart" id="0x7f0401ea" />
    <public type="attr" name="itemShapeInsetTop" id="0x7f0401eb" />
    <public type="attr" name="itemSpacing" id="0x7f0401ec" />
    <public type="attr" name="itemStrokeColor" id="0x7f0401ed" />
    <public type="attr" name="itemStrokeWidth" id="0x7f0401ee" />
    <public type="attr" name="itemTextAppearance" id="0x7f0401ef" />
    <public type="attr" name="itemTextAppearanceActive" id="0x7f0401f0" />
    <public type="attr" name="itemTextAppearanceInactive" id="0x7f0401f1" />
    <public type="attr" name="itemTextColor" id="0x7f0401f2" />
    <public type="attr" name="justifyContent" id="0x7f0401f3" />
    <public type="attr" name="key" id="0x7f0401f4" />
    <public type="attr" name="keyPositionType" id="0x7f0401f5" />
    <public type="attr" name="keylines" id="0x7f0401f6" />
    <public type="attr" name="lStar" id="0x7f0401f7" />
    <public type="attr" name="labelVisibilityMode" id="0x7f0401f8" />
    <public type="attr" name="lastBaselineToBottomHeight" id="0x7f0401f9" />
    <public type="attr" name="layout" id="0x7f0401fa" />
    <public type="attr" name="layoutDescription" id="0x7f0401fb" />
    <public type="attr" name="layoutDuringTransition" id="0x7f0401fc" />
    <public type="attr" name="layoutManager" id="0x7f0401fd" />
    <public type="attr" name="layout_alignSelf" id="0x7f0401fe" />
    <public type="attr" name="layout_anchor" id="0x7f0401ff" />
    <public type="attr" name="layout_anchorGravity" id="0x7f040200" />
    <public type="attr" name="layout_behavior" id="0x7f040201" />
    <public type="attr" name="layout_collapseMode" id="0x7f040202" />
    <public type="attr" name="layout_collapseParallaxMultiplier" id="0x7f040203" />
    <public type="attr" name="layout_constrainedHeight" id="0x7f040204" />
    <public type="attr" name="layout_constrainedWidth" id="0x7f040205" />
    <public type="attr" name="layout_constraintBaseline_creator" id="0x7f040206" />
    <public type="attr" name="layout_constraintBaseline_toBaselineOf" id="0x7f040207" />
    <public type="attr" name="layout_constraintBaseline_toBottomOf" id="0x7f040208" />
    <public type="attr" name="layout_constraintBaseline_toTopOf" id="0x7f040209" />
    <public type="attr" name="layout_constraintBottom_creator" id="0x7f04020a" />
    <public type="attr" name="layout_constraintBottom_toBottomOf" id="0x7f04020b" />
    <public type="attr" name="layout_constraintBottom_toTopOf" id="0x7f04020c" />
    <public type="attr" name="layout_constraintCircle" id="0x7f04020d" />
    <public type="attr" name="layout_constraintCircleAngle" id="0x7f04020e" />
    <public type="attr" name="layout_constraintCircleRadius" id="0x7f04020f" />
    <public type="attr" name="layout_constraintDimensionRatio" id="0x7f040210" />
    <public type="attr" name="layout_constraintEnd_toEndOf" id="0x7f040211" />
    <public type="attr" name="layout_constraintEnd_toStartOf" id="0x7f040212" />
    <public type="attr" name="layout_constraintGuide_begin" id="0x7f040213" />
    <public type="attr" name="layout_constraintGuide_end" id="0x7f040214" />
    <public type="attr" name="layout_constraintGuide_percent" id="0x7f040215" />
    <public type="attr" name="layout_constraintHeight" id="0x7f040216" />
    <public type="attr" name="layout_constraintHeight_default" id="0x7f040217" />
    <public type="attr" name="layout_constraintHeight_max" id="0x7f040218" />
    <public type="attr" name="layout_constraintHeight_min" id="0x7f040219" />
    <public type="attr" name="layout_constraintHeight_percent" id="0x7f04021a" />
    <public type="attr" name="layout_constraintHorizontal_bias" id="0x7f04021b" />
    <public type="attr" name="layout_constraintHorizontal_chainStyle" id="0x7f04021c" />
    <public type="attr" name="layout_constraintHorizontal_weight" id="0x7f04021d" />
    <public type="attr" name="layout_constraintLeft_creator" id="0x7f04021e" />
    <public type="attr" name="layout_constraintLeft_toLeftOf" id="0x7f04021f" />
    <public type="attr" name="layout_constraintLeft_toRightOf" id="0x7f040220" />
    <public type="attr" name="layout_constraintRight_creator" id="0x7f040221" />
    <public type="attr" name="layout_constraintRight_toLeftOf" id="0x7f040222" />
    <public type="attr" name="layout_constraintRight_toRightOf" id="0x7f040223" />
    <public type="attr" name="layout_constraintStart_toEndOf" id="0x7f040224" />
    <public type="attr" name="layout_constraintStart_toStartOf" id="0x7f040225" />
    <public type="attr" name="layout_constraintTag" id="0x7f040226" />
    <public type="attr" name="layout_constraintTop_creator" id="0x7f040227" />
    <public type="attr" name="layout_constraintTop_toBottomOf" id="0x7f040228" />
    <public type="attr" name="layout_constraintTop_toTopOf" id="0x7f040229" />
    <public type="attr" name="layout_constraintVertical_bias" id="0x7f04022a" />
    <public type="attr" name="layout_constraintVertical_chainStyle" id="0x7f04022b" />
    <public type="attr" name="layout_constraintVertical_weight" id="0x7f04022c" />
    <public type="attr" name="layout_constraintWidth" id="0x7f04022d" />
    <public type="attr" name="layout_constraintWidth_default" id="0x7f04022e" />
    <public type="attr" name="layout_constraintWidth_max" id="0x7f04022f" />
    <public type="attr" name="layout_constraintWidth_min" id="0x7f040230" />
    <public type="attr" name="layout_constraintWidth_percent" id="0x7f040231" />
    <public type="attr" name="layout_dodgeInsetEdges" id="0x7f040232" />
    <public type="attr" name="layout_editor_absoluteX" id="0x7f040233" />
    <public type="attr" name="layout_editor_absoluteY" id="0x7f040234" />
    <public type="attr" name="layout_flexBasisPercent" id="0x7f040235" />
    <public type="attr" name="layout_flexGrow" id="0x7f040236" />
    <public type="attr" name="layout_flexShrink" id="0x7f040237" />
    <public type="attr" name="layout_goneMarginBaseline" id="0x7f040238" />
    <public type="attr" name="layout_goneMarginBottom" id="0x7f040239" />
    <public type="attr" name="layout_goneMarginEnd" id="0x7f04023a" />
    <public type="attr" name="layout_goneMarginLeft" id="0x7f04023b" />
    <public type="attr" name="layout_goneMarginRight" id="0x7f04023c" />
    <public type="attr" name="layout_goneMarginStart" id="0x7f04023d" />
    <public type="attr" name="layout_goneMarginTop" id="0x7f04023e" />
    <public type="attr" name="layout_insetEdge" id="0x7f04023f" />
    <public type="attr" name="layout_keyline" id="0x7f040240" />
    <public type="attr" name="layout_marginBaseline" id="0x7f040241" />
    <public type="attr" name="layout_maxHeight" id="0x7f040242" />
    <public type="attr" name="layout_maxWidth" id="0x7f040243" />
    <public type="attr" name="layout_minHeight" id="0x7f040244" />
    <public type="attr" name="layout_minWidth" id="0x7f040245" />
    <public type="attr" name="layout_optimizationLevel" id="0x7f040246" />
    <public type="attr" name="layout_order" id="0x7f040247" />
    <public type="attr" name="layout_scrollFlags" id="0x7f040248" />
    <public type="attr" name="layout_scrollInterpolator" id="0x7f040249" />
    <public type="attr" name="layout_wrapBefore" id="0x7f04024a" />
    <public type="attr" name="layout_wrapBehaviorInParent" id="0x7f04024b" />
    <public type="attr" name="liftOnScroll" id="0x7f04024c" />
    <public type="attr" name="liftOnScrollTargetViewId" id="0x7f04024d" />
    <public type="attr" name="limitBoundsTo" id="0x7f04024e" />
    <public type="attr" name="lineHeight" id="0x7f04024f" />
    <public type="attr" name="lineSpacing" id="0x7f040250" />
    <public type="attr" name="listChoiceBackgroundIndicator" id="0x7f040251" />
    <public type="attr" name="listChoiceIndicatorMultipleAnimated" id="0x7f040252" />
    <public type="attr" name="listChoiceIndicatorSingleAnimated" id="0x7f040253" />
    <public type="attr" name="listDividerAlertDialog" id="0x7f040254" />
    <public type="attr" name="listItemLayout" id="0x7f040255" />
    <public type="attr" name="listLayout" id="0x7f040256" />
    <public type="attr" name="listMenuViewStyle" id="0x7f040257" />
    <public type="attr" name="listPopupWindowStyle" id="0x7f040258" />
    <public type="attr" name="listPreferredItemHeight" id="0x7f040259" />
    <public type="attr" name="listPreferredItemHeightLarge" id="0x7f04025a" />
    <public type="attr" name="listPreferredItemHeightSmall" id="0x7f04025b" />
    <public type="attr" name="listPreferredItemPaddingEnd" id="0x7f04025c" />
    <public type="attr" name="listPreferredItemPaddingLeft" id="0x7f04025d" />
    <public type="attr" name="listPreferredItemPaddingRight" id="0x7f04025e" />
    <public type="attr" name="listPreferredItemPaddingStart" id="0x7f04025f" />
    <public type="attr" name="logo" id="0x7f040260" />
    <public type="attr" name="logoDescription" id="0x7f040261" />
    <public type="attr" name="materialAlertDialogBodyTextStyle" id="0x7f040262" />
    <public type="attr" name="materialAlertDialogTheme" id="0x7f040263" />
    <public type="attr" name="materialAlertDialogTitleIconStyle" id="0x7f040264" />
    <public type="attr" name="materialAlertDialogTitlePanelStyle" id="0x7f040265" />
    <public type="attr" name="materialAlertDialogTitleTextStyle" id="0x7f040266" />
    <public type="attr" name="materialButtonOutlinedStyle" id="0x7f040267" />
    <public type="attr" name="materialButtonStyle" id="0x7f040268" />
    <public type="attr" name="materialButtonToggleGroupStyle" id="0x7f040269" />
    <public type="attr" name="materialCalendarDay" id="0x7f04026a" />
    <public type="attr" name="materialCalendarFullscreenTheme" id="0x7f04026b" />
    <public type="attr" name="materialCalendarHeaderConfirmButton" id="0x7f04026c" />
    <public type="attr" name="materialCalendarHeaderDivider" id="0x7f04026d" />
    <public type="attr" name="materialCalendarHeaderLayout" id="0x7f04026e" />
    <public type="attr" name="materialCalendarHeaderSelection" id="0x7f04026f" />
    <public type="attr" name="materialCalendarHeaderTitle" id="0x7f040270" />
    <public type="attr" name="materialCalendarHeaderToggleButton" id="0x7f040271" />
    <public type="attr" name="materialCalendarStyle" id="0x7f040272" />
    <public type="attr" name="materialCalendarTheme" id="0x7f040273" />
    <public type="attr" name="materialCardViewStyle" id="0x7f040274" />
    <public type="attr" name="materialThemeOverlay" id="0x7f040275" />
    <public type="attr" name="maxAcceleration" id="0x7f040276" />
    <public type="attr" name="maxActionInlineWidth" id="0x7f040277" />
    <public type="attr" name="maxButtonHeight" id="0x7f040278" />
    <public type="attr" name="maxCharacterCount" id="0x7f040279" />
    <public type="attr" name="maxHeight" id="0x7f04027a" />
    <public type="attr" name="maxImageSize" id="0x7f04027b" />
    <public type="attr" name="maxLine" id="0x7f04027c" />
    <public type="attr" name="maxValue" id="0x7f04027d" />
    <public type="attr" name="maxVelocity" id="0x7f04027e" />
    <public type="attr" name="maxWidth" id="0x7f04027f" />
    <public type="attr" name="measureHeight" id="0x7f040280" />
    <public type="attr" name="measureWithLargestChild" id="0x7f040281" />
    <public type="attr" name="menu" id="0x7f040282" />
    <public type="attr" name="methodName" id="0x7f040283" />
    <public type="attr" name="min" id="0x7f040284" />
    <public type="attr" name="minHeight" id="0x7f040285" />
    <public type="attr" name="minTouchTargetSize" id="0x7f040286" />
    <public type="attr" name="minWidth" id="0x7f040287" />
    <public type="attr" name="mock_diagonalsColor" id="0x7f040288" />
    <public type="attr" name="mock_label" id="0x7f040289" />
    <public type="attr" name="mock_labelBackgroundColor" id="0x7f04028a" />
    <public type="attr" name="mock_labelColor" id="0x7f04028b" />
    <public type="attr" name="mock_showDiagonals" id="0x7f04028c" />
    <public type="attr" name="mock_showLabel" id="0x7f04028d" />
    <public type="attr" name="motionDebug" id="0x7f04028e" />
    <public type="attr" name="motionEffect_alpha" id="0x7f04028f" />
    <public type="attr" name="motionEffect_end" id="0x7f040290" />
    <public type="attr" name="motionEffect_move" id="0x7f040291" />
    <public type="attr" name="motionEffect_start" id="0x7f040292" />
    <public type="attr" name="motionEffect_strict" id="0x7f040293" />
    <public type="attr" name="motionEffect_translationX" id="0x7f040294" />
    <public type="attr" name="motionEffect_translationY" id="0x7f040295" />
    <public type="attr" name="motionEffect_viewTransition" id="0x7f040296" />
    <public type="attr" name="motionInterpolator" id="0x7f040297" />
    <public type="attr" name="motionPathRotate" id="0x7f040298" />
    <public type="attr" name="motionProgress" id="0x7f040299" />
    <public type="attr" name="motionStagger" id="0x7f04029a" />
    <public type="attr" name="motionTarget" id="0x7f04029b" />
    <public type="attr" name="motion_postLayoutCollision" id="0x7f04029c" />
    <public type="attr" name="motion_triggerOnCollision" id="0x7f04029d" />
    <public type="attr" name="moveWhenScrollAtTop" id="0x7f04029e" />
    <public type="attr" name="multiChoiceItemLayout" id="0x7f04029f" />
    <public type="attr" name="navigationContentDescription" id="0x7f0402a0" />
    <public type="attr" name="navigationIcon" id="0x7f0402a1" />
    <public type="attr" name="navigationMode" id="0x7f0402a2" />
    <public type="attr" name="navigationViewStyle" id="0x7f0402a3" />
    <public type="attr" name="negativeButtonText" id="0x7f0402a4" />
    <public type="attr" name="nestedScrollFlags" id="0x7f0402a5" />
    <public type="attr" name="nestedScrollViewStyle" id="0x7f0402a6" />
    <public type="attr" name="normal_drawable" id="0x7f0402a7" />
    <public type="attr" name="number" id="0x7f0402a8" />
    <public type="attr" name="numericModifiers" id="0x7f0402a9" />
    <public type="attr" name="onCross" id="0x7f0402aa" />
    <public type="attr" name="onHide" id="0x7f0402ab" />
    <public type="attr" name="onNegativeCross" id="0x7f0402ac" />
    <public type="attr" name="onPositiveCross" id="0x7f0402ad" />
    <public type="attr" name="onShow" id="0x7f0402ae" />
    <public type="attr" name="onStateTransition" id="0x7f0402af" />
    <public type="attr" name="onTouchUp" id="0x7f0402b0" />
    <public type="attr" name="order" id="0x7f0402b1" />
    <public type="attr" name="orderingFromXml" id="0x7f0402b2" />
    <public type="attr" name="outlineColor" id="0x7f0402b3" />
    <public type="attr" name="outlineWidth" id="0x7f0402b4" />
    <public type="attr" name="overlapAnchor" id="0x7f0402b5" />
    <public type="attr" name="overlay" id="0x7f0402b6" />
    <public type="attr" name="paddingBottomNoButtons" id="0x7f0402b7" />
    <public type="attr" name="paddingEnd" id="0x7f0402b8" />
    <public type="attr" name="paddingStart" id="0x7f0402b9" />
    <public type="attr" name="paddingTopNoTitle" id="0x7f0402ba" />
    <public type="attr" name="panelBackground" id="0x7f0402bb" />
    <public type="attr" name="panelMenuListTheme" id="0x7f0402bc" />
    <public type="attr" name="panelMenuListWidth" id="0x7f0402bd" />
    <public type="attr" name="passwordToggleContentDescription" id="0x7f0402be" />
    <public type="attr" name="passwordToggleDrawable" id="0x7f0402bf" />
    <public type="attr" name="passwordToggleEnabled" id="0x7f0402c0" />
    <public type="attr" name="passwordToggleTint" id="0x7f0402c1" />
    <public type="attr" name="passwordToggleTintMode" id="0x7f0402c2" />
    <public type="attr" name="pathMotionArc" id="0x7f0402c3" />
    <public type="attr" name="path_percent" id="0x7f0402c4" />
    <public type="attr" name="percentHeight" id="0x7f0402c5" />
    <public type="attr" name="percentWidth" id="0x7f0402c6" />
    <public type="attr" name="percentX" id="0x7f0402c7" />
    <public type="attr" name="percentY" id="0x7f0402c8" />
    <public type="attr" name="perpendicularPath_percent" id="0x7f0402c9" />
    <public type="attr" name="persistent" id="0x7f0402ca" />
    <public type="attr" name="pivotAnchor" id="0x7f0402cb" />
    <public type="attr" name="placeholder_emptyVisibility" id="0x7f0402cc" />
    <public type="attr" name="polarRelativeTo" id="0x7f0402cd" />
    <public type="attr" name="popupMenuBackground" id="0x7f0402ce" />
    <public type="attr" name="popupMenuStyle" id="0x7f0402cf" />
    <public type="attr" name="popupTheme" id="0x7f0402d0" />
    <public type="attr" name="popupWindowStyle" id="0x7f0402d1" />
    <public type="attr" name="positiveButtonText" id="0x7f0402d2" />
    <public type="attr" name="preferenceCategoryStyle" id="0x7f0402d3" />
    <public type="attr" name="preferenceCategoryTitleTextAppearance" id="0x7f0402d4" />
    <public type="attr" name="preferenceFragmentCompatStyle" id="0x7f0402d5" />
    <public type="attr" name="preferenceFragmentListStyle" id="0x7f0402d6" />
    <public type="attr" name="preferenceFragmentStyle" id="0x7f0402d7" />
    <public type="attr" name="preferenceInformationStyle" id="0x7f0402d8" />
    <public type="attr" name="preferenceScreenStyle" id="0x7f0402d9" />
    <public type="attr" name="preferenceStyle" id="0x7f0402da" />
    <public type="attr" name="preferenceTheme" id="0x7f0402db" />
    <public type="attr" name="prefix" id="0x7f0402dc" />
    <public type="attr" name="preserveIconSpacing" id="0x7f0402dd" />
    <public type="attr" name="pressedTranslationZ" id="0x7f0402de" />
    <public type="attr" name="progressBarPadding" id="0x7f0402df" />
    <public type="attr" name="progressBarStyle" id="0x7f0402e0" />
    <public type="attr" name="progressColor" id="0x7f0402e1" />
    <public type="attr" name="progressText" id="0x7f0402e2" />
    <public type="attr" name="progressTextColor" id="0x7f0402e3" />
    <public type="attr" name="quantizeMotionInterpolator" id="0x7f0402e4" />
    <public type="attr" name="quantizeMotionPhase" id="0x7f0402e5" />
    <public type="attr" name="quantizeMotionSteps" id="0x7f0402e6" />
    <public type="attr" name="queryBackground" id="0x7f0402e7" />
    <public type="attr" name="queryHint" id="0x7f0402e8" />
    <public type="attr" name="queryPatterns" id="0x7f0402e9" />
    <public type="attr" name="radioButtonStyle" id="0x7f0402ea" />
    <public type="attr" name="rangeFillColor" id="0x7f0402eb" />
    <public type="attr" name="ratingBarStyle" id="0x7f0402ec" />
    <public type="attr" name="ratingBarStyleIndicator" id="0x7f0402ed" />
    <public type="attr" name="ratingBarStyleSmall" id="0x7f0402ee" />
    <public type="attr" name="reactiveGuide_animateChange" id="0x7f0402ef" />
    <public type="attr" name="reactiveGuide_applyToAllConstraintSets" id="0x7f0402f0" />
    <public type="attr" name="reactiveGuide_applyToConstraintSet" id="0x7f0402f1" />
    <public type="attr" name="reactiveGuide_valueId" id="0x7f0402f2" />
    <public type="attr" name="recyclerViewStyle" id="0x7f0402f3" />
    <public type="attr" name="region_heightLessThan" id="0x7f0402f4" />
    <public type="attr" name="region_heightMoreThan" id="0x7f0402f5" />
    <public type="attr" name="region_widthLessThan" id="0x7f0402f6" />
    <public type="attr" name="region_widthMoreThan" id="0x7f0402f7" />
    <public type="attr" name="reverseLayout" id="0x7f0402f8" />
    <public type="attr" name="rippleColor" id="0x7f0402f9" />
    <public type="attr" name="rotationCenterId" id="0x7f0402fa" />
    <public type="attr" name="round" id="0x7f0402fb" />
    <public type="attr" name="roundPercent" id="0x7f0402fc" />
    <public type="attr" name="roundedCorners" id="0x7f0402fd" />
    <public type="attr" name="saturation" id="0x7f0402fe" />
    <public type="attr" name="scaleFromTextSize" id="0x7f0402ff" />
    <public type="attr" name="scrimAnimationDuration" id="0x7f040300" />
    <public type="attr" name="scrimBackground" id="0x7f040301" />
    <public type="attr" name="scrimVisibleHeightTrigger" id="0x7f040302" />
    <public type="attr" name="searchHintIcon" id="0x7f040303" />
    <public type="attr" name="searchIcon" id="0x7f040304" />
    <public type="attr" name="searchViewStyle" id="0x7f040305" />
    <public type="attr" name="seekBarIncrement" id="0x7f040306" />
    <public type="attr" name="seekBarPreferenceStyle" id="0x7f040307" />
    <public type="attr" name="seekBarStyle" id="0x7f040308" />
    <public type="attr" name="selectable" id="0x7f040309" />
    <public type="attr" name="selectableItemBackground" id="0x7f04030a" />
    <public type="attr" name="selectableItemBackgroundBorderless" id="0x7f04030b" />
    <public type="attr" name="selected_drawable" id="0x7f04030c" />
    <public type="attr" name="setsTag" id="0x7f04030d" />
    <public type="attr" name="shapeAppearance" id="0x7f04030e" />
    <public type="attr" name="shapeAppearanceLargeComponent" id="0x7f04030f" />
    <public type="attr" name="shapeAppearanceMediumComponent" id="0x7f040310" />
    <public type="attr" name="shapeAppearanceOverlay" id="0x7f040311" />
    <public type="attr" name="shapeAppearanceSmallComponent" id="0x7f040312" />
    <public type="attr" name="shortcutMatchRequired" id="0x7f040313" />
    <public type="attr" name="shouldDisableView" id="0x7f040314" />
    <public type="attr" name="showAsAction" id="0x7f040315" />
    <public type="attr" name="showDivider" id="0x7f040316" />
    <public type="attr" name="showDividerHorizontal" id="0x7f040317" />
    <public type="attr" name="showDividerVertical" id="0x7f040318" />
    <public type="attr" name="showDividers" id="0x7f040319" />
    <public type="attr" name="showMotionSpec" id="0x7f04031a" />
    <public type="attr" name="showPaths" id="0x7f04031b" />
    <public type="attr" name="showSeekBarValue" id="0x7f04031c" />
    <public type="attr" name="showText" id="0x7f04031d" />
    <public type="attr" name="showTitle" id="0x7f04031e" />
    <public type="attr" name="shrinkMotionSpec" id="0x7f04031f" />
    <public type="attr" name="singleChoiceItemLayout" id="0x7f040320" />
    <public type="attr" name="singleLine" id="0x7f040321" />
    <public type="attr" name="singleLineTitle" id="0x7f040322" />
    <public type="attr" name="singleSelection" id="0x7f040323" />
    <public type="attr" name="sizePercent" id="0x7f040324" />
    <public type="attr" name="snackbarButtonStyle" id="0x7f040325" />
    <public type="attr" name="snackbarStyle" id="0x7f040326" />
    <public type="attr" name="spanCount" id="0x7f040327" />
    <public type="attr" name="spinBars" id="0x7f040328" />
    <public type="attr" name="spinnerDropDownItemStyle" id="0x7f040329" />
    <public type="attr" name="spinnerStyle" id="0x7f04032a" />
    <public type="attr" name="splitTrack" id="0x7f04032b" />
    <public type="attr" name="springBoundary" id="0x7f04032c" />
    <public type="attr" name="springDamping" id="0x7f04032d" />
    <public type="attr" name="springMass" id="0x7f04032e" />
    <public type="attr" name="springStiffness" id="0x7f04032f" />
    <public type="attr" name="springStopThreshold" id="0x7f040330" />
    <public type="attr" name="srcCompat" id="0x7f040331" />
    <public type="attr" name="stackFromEnd" id="0x7f040332" />
    <public type="attr" name="staggered" id="0x7f040333" />
    <public type="attr" name="startIconCheckable" id="0x7f040334" />
    <public type="attr" name="startIconContentDescription" id="0x7f040335" />
    <public type="attr" name="startIconDrawable" id="0x7f040336" />
    <public type="attr" name="startIconTint" id="0x7f040337" />
    <public type="attr" name="startIconTintMode" id="0x7f040338" />
    <public type="attr" name="state_above_anchor" id="0x7f040339" />
    <public type="attr" name="state_collapsed" id="0x7f04033a" />
    <public type="attr" name="state_collapsible" id="0x7f04033b" />
    <public type="attr" name="state_dragged" id="0x7f04033c" />
    <public type="attr" name="state_liftable" id="0x7f04033d" />
    <public type="attr" name="state_lifted" id="0x7f04033e" />
    <public type="attr" name="statusBarBackground" id="0x7f04033f" />
    <public type="attr" name="statusBarForeground" id="0x7f040340" />
    <public type="attr" name="statusBarScrim" id="0x7f040341" />
    <public type="attr" name="strokeColor" id="0x7f040342" />
    <public type="attr" name="strokeWidth" id="0x7f040343" />
    <public type="attr" name="strokeWidthDimension" id="0x7f040344" />
    <public type="attr" name="subMenuArrow" id="0x7f040345" />
    <public type="attr" name="submitBackground" id="0x7f040346" />
    <public type="attr" name="subtitle" id="0x7f040347" />
    <public type="attr" name="subtitleTextAppearance" id="0x7f040348" />
    <public type="attr" name="subtitleTextColor" id="0x7f040349" />
    <public type="attr" name="subtitleTextStyle" id="0x7f04034a" />
    <public type="attr" name="suffix" id="0x7f04034b" />
    <public type="attr" name="suggestionRowLayout" id="0x7f04034c" />
    <public type="attr" name="summary" id="0x7f04034d" />
    <public type="attr" name="summaryOff" id="0x7f04034e" />
    <public type="attr" name="summaryOn" id="0x7f04034f" />
    <public type="attr" name="swipeRefreshLayoutProgressSpinnerBackgroundColor" id="0x7f040350" />
    <public type="attr" name="switchMinWidth" id="0x7f040351" />
    <public type="attr" name="switchPadding" id="0x7f040352" />
    <public type="attr" name="switchPreferenceCompatStyle" id="0x7f040353" />
    <public type="attr" name="switchPreferenceStyle" id="0x7f040354" />
    <public type="attr" name="switchStyle" id="0x7f040355" />
    <public type="attr" name="switchTextAppearance" id="0x7f040356" />
    <public type="attr" name="switchTextOff" id="0x7f040357" />
    <public type="attr" name="switchTextOn" id="0x7f040358" />
    <public type="attr" name="tabBackground" id="0x7f040359" />
    <public type="attr" name="tabContentStart" id="0x7f04035a" />
    <public type="attr" name="tabGravity" id="0x7f04035b" />
    <public type="attr" name="tabIconTint" id="0x7f04035c" />
    <public type="attr" name="tabIconTintMode" id="0x7f04035d" />
    <public type="attr" name="tabIndicator" id="0x7f04035e" />
    <public type="attr" name="tabIndicatorAnimationDuration" id="0x7f04035f" />
    <public type="attr" name="tabIndicatorColor" id="0x7f040360" />
    <public type="attr" name="tabIndicatorFullWidth" id="0x7f040361" />
    <public type="attr" name="tabIndicatorGravity" id="0x7f040362" />
    <public type="attr" name="tabIndicatorHeight" id="0x7f040363" />
    <public type="attr" name="tabInlineLabel" id="0x7f040364" />
    <public type="attr" name="tabMaxWidth" id="0x7f040365" />
    <public type="attr" name="tabMinWidth" id="0x7f040366" />
    <public type="attr" name="tabMode" id="0x7f040367" />
    <public type="attr" name="tabPadding" id="0x7f040368" />
    <public type="attr" name="tabPaddingBottom" id="0x7f040369" />
    <public type="attr" name="tabPaddingEnd" id="0x7f04036a" />
    <public type="attr" name="tabPaddingStart" id="0x7f04036b" />
    <public type="attr" name="tabPaddingTop" id="0x7f04036c" />
    <public type="attr" name="tabRippleColor" id="0x7f04036d" />
    <public type="attr" name="tabSelectedTextColor" id="0x7f04036e" />
    <public type="attr" name="tabStyle" id="0x7f04036f" />
    <public type="attr" name="tabTextAppearance" id="0x7f040370" />
    <public type="attr" name="tabTextColor" id="0x7f040371" />
    <public type="attr" name="tabUnboundedRipple" id="0x7f040372" />
    <public type="attr" name="targetId" id="0x7f040373" />
    <public type="attr" name="telltales_tailColor" id="0x7f040374" />
    <public type="attr" name="telltales_tailScale" id="0x7f040375" />
    <public type="attr" name="telltales_velocityMode" id="0x7f040376" />
    <public type="attr" name="textAllCaps" id="0x7f040377" />
    <public type="attr" name="textAppearanceBody1" id="0x7f040378" />
    <public type="attr" name="textAppearanceBody2" id="0x7f040379" />
    <public type="attr" name="textAppearanceButton" id="0x7f04037a" />
    <public type="attr" name="textAppearanceCaption" id="0x7f04037b" />
    <public type="attr" name="textAppearanceHeadline1" id="0x7f04037c" />
    <public type="attr" name="textAppearanceHeadline2" id="0x7f04037d" />
    <public type="attr" name="textAppearanceHeadline3" id="0x7f04037e" />
    <public type="attr" name="textAppearanceHeadline4" id="0x7f04037f" />
    <public type="attr" name="textAppearanceHeadline5" id="0x7f040380" />
    <public type="attr" name="textAppearanceHeadline6" id="0x7f040381" />
    <public type="attr" name="textAppearanceLargePopupMenu" id="0x7f040382" />
    <public type="attr" name="textAppearanceLineHeightEnabled" id="0x7f040383" />
    <public type="attr" name="textAppearanceListItem" id="0x7f040384" />
    <public type="attr" name="textAppearanceListItemSecondary" id="0x7f040385" />
    <public type="attr" name="textAppearanceListItemSmall" id="0x7f040386" />
    <public type="attr" name="textAppearanceOverline" id="0x7f040387" />
    <public type="attr" name="textAppearancePopupMenuHeader" id="0x7f040388" />
    <public type="attr" name="textAppearanceSearchResultSubtitle" id="0x7f040389" />
    <public type="attr" name="textAppearanceSearchResultTitle" id="0x7f04038a" />
    <public type="attr" name="textAppearanceSmallPopupMenu" id="0x7f04038b" />
    <public type="attr" name="textAppearanceSubtitle1" id="0x7f04038c" />
    <public type="attr" name="textAppearanceSubtitle2" id="0x7f04038d" />
    <public type="attr" name="textBackground" id="0x7f04038e" />
    <public type="attr" name="textBackgroundPanX" id="0x7f04038f" />
    <public type="attr" name="textBackgroundPanY" id="0x7f040390" />
    <public type="attr" name="textBackgroundRotate" id="0x7f040391" />
    <public type="attr" name="textBackgroundZoom" id="0x7f040392" />
    <public type="attr" name="textColorAlertDialogListItem" id="0x7f040393" />
    <public type="attr" name="textColorSearchUrl" id="0x7f040394" />
    <public type="attr" name="textEndPadding" id="0x7f040395" />
    <public type="attr" name="textFillColor" id="0x7f040396" />
    <public type="attr" name="textInputStyle" id="0x7f040397" />
    <public type="attr" name="textLocale" id="0x7f040398" />
    <public type="attr" name="textOutlineColor" id="0x7f040399" />
    <public type="attr" name="textOutlineThickness" id="0x7f04039a" />
    <public type="attr" name="textPanX" id="0x7f04039b" />
    <public type="attr" name="textPanY" id="0x7f04039c" />
    <public type="attr" name="textSize" id="0x7f04039d" />
    <public type="attr" name="textStartPadding" id="0x7f04039e" />
    <public type="attr" name="textureBlurFactor" id="0x7f04039f" />
    <public type="attr" name="textureEffect" id="0x7f0403a0" />
    <public type="attr" name="textureHeight" id="0x7f0403a1" />
    <public type="attr" name="textureWidth" id="0x7f0403a2" />
    <public type="attr" name="theme" id="0x7f0403a3" />
    <public type="attr" name="themeLineHeight" id="0x7f0403a4" />
    <public type="attr" name="thickness" id="0x7f0403a5" />
    <public type="attr" name="thumbTextPadding" id="0x7f0403a6" />
    <public type="attr" name="thumbTint" id="0x7f0403a7" />
    <public type="attr" name="thumbTintMode" id="0x7f0403a8" />
    <public type="attr" name="tickMark" id="0x7f0403a9" />
    <public type="attr" name="tickMarkTint" id="0x7f0403aa" />
    <public type="attr" name="tickMarkTintMode" id="0x7f0403ab" />
    <public type="attr" name="tint" id="0x7f0403ac" />
    <public type="attr" name="tintMode" id="0x7f0403ad" />
    <public type="attr" name="title" id="0x7f0403ae" />
    <public type="attr" name="titleEnabled" id="0x7f0403af" />
    <public type="attr" name="titleMargin" id="0x7f0403b0" />
    <public type="attr" name="titleMarginBottom" id="0x7f0403b1" />
    <public type="attr" name="titleMarginEnd" id="0x7f0403b2" />
    <public type="attr" name="titleMarginStart" id="0x7f0403b3" />
    <public type="attr" name="titleMarginTop" id="0x7f0403b4" />
    <public type="attr" name="titleMargins" id="0x7f0403b5" />
    <public type="attr" name="titleTextAppearance" id="0x7f0403b6" />
    <public type="attr" name="titleTextColor" id="0x7f0403b7" />
    <public type="attr" name="titleTextStyle" id="0x7f0403b8" />
    <public type="attr" name="toolbarId" id="0x7f0403b9" />
    <public type="attr" name="toolbarNavigationButtonStyle" id="0x7f0403ba" />
    <public type="attr" name="toolbarStyle" id="0x7f0403bb" />
    <public type="attr" name="tooltipForegroundColor" id="0x7f0403bc" />
    <public type="attr" name="tooltipFrameBackground" id="0x7f0403bd" />
    <public type="attr" name="tooltipText" id="0x7f0403be" />
    <public type="attr" name="touchAnchorId" id="0x7f0403bf" />
    <public type="attr" name="touchAnchorSide" id="0x7f0403c0" />
    <public type="attr" name="touchRegionId" id="0x7f0403c1" />
    <public type="attr" name="track" id="0x7f0403c2" />
    <public type="attr" name="trackTint" id="0x7f0403c3" />
    <public type="attr" name="trackTintMode" id="0x7f0403c4" />
    <public type="attr" name="transformPivotTarget" id="0x7f0403c5" />
    <public type="attr" name="transitionDisable" id="0x7f0403c6" />
    <public type="attr" name="transitionEasing" id="0x7f0403c7" />
    <public type="attr" name="transitionFlags" id="0x7f0403c8" />
    <public type="attr" name="transitionPathRotate" id="0x7f0403c9" />
    <public type="attr" name="triggerId" id="0x7f0403ca" />
    <public type="attr" name="triggerReceiver" id="0x7f0403cb" />
    <public type="attr" name="triggerSlack" id="0x7f0403cc" />
    <public type="attr" name="ttcIndex" id="0x7f0403cd" />
    <public type="attr" name="upDuration" id="0x7f0403ce" />
    <public type="attr" name="updatesContinuously" id="0x7f0403cf" />
    <public type="attr" name="useCompatPadding" id="0x7f0403d0" />
    <public type="attr" name="useMaterialThemeColors" id="0x7f0403d1" />
    <public type="attr" name="useSimpleSummaryProvider" id="0x7f0403d2" />
    <public type="attr" name="viewInflaterClass" id="0x7f0403d3" />
    <public type="attr" name="viewTransitionMode" id="0x7f0403d4" />
    <public type="attr" name="viewTransitionOnCross" id="0x7f0403d5" />
    <public type="attr" name="viewTransitionOnNegativeCross" id="0x7f0403d6" />
    <public type="attr" name="viewTransitionOnPositiveCross" id="0x7f0403d7" />
    <public type="attr" name="visibilityMode" id="0x7f0403d8" />
    <public type="attr" name="voiceIcon" id="0x7f0403d9" />
    <public type="attr" name="warmth" id="0x7f0403da" />
    <public type="attr" name="waveDecay" id="0x7f0403db" />
    <public type="attr" name="waveOffset" id="0x7f0403dc" />
    <public type="attr" name="wavePeriod" id="0x7f0403dd" />
    <public type="attr" name="wavePhase" id="0x7f0403de" />
    <public type="attr" name="waveShape" id="0x7f0403df" />
    <public type="attr" name="waveVariesBy" id="0x7f0403e0" />
    <public type="attr" name="widgetLayout" id="0x7f0403e1" />
    <public type="attr" name="windowActionBar" id="0x7f0403e2" />
    <public type="attr" name="windowActionBarOverlay" id="0x7f0403e3" />
    <public type="attr" name="windowActionModeOverlay" id="0x7f0403e4" />
    <public type="attr" name="windowFixedHeightMajor" id="0x7f0403e5" />
    <public type="attr" name="windowFixedHeightMinor" id="0x7f0403e6" />
    <public type="attr" name="windowFixedWidthMajor" id="0x7f0403e7" />
    <public type="attr" name="windowFixedWidthMinor" id="0x7f0403e8" />
    <public type="attr" name="windowMinWidthMajor" id="0x7f0403e9" />
    <public type="attr" name="windowMinWidthMinor" id="0x7f0403ea" />
    <public type="attr" name="windowNoTitle" id="0x7f0403eb" />
    <public type="attr" name="yearSelectedStyle" id="0x7f0403ec" />
    <public type="attr" name="yearStyle" id="0x7f0403ed" />
    <public type="attr" name="yearTodayStyle" id="0x7f0403ee" />
    <public type="bool" name="abc_action_bar_embed_tabs" id="0x7f050000" />
    <public type="bool" name="abc_allow_stacked_button_bar" id="0x7f050001" />
    <public type="bool" name="abc_config_actionMenuItemAllCaps" id="0x7f050002" />
    <public type="bool" name="config_materialPreferenceIconSpaceReserved" id="0x7f050003" />
    <public type="bool" name="mtrl_btn_textappearance_all_caps" id="0x7f050004" />
    <public type="color" name="abc_background_cache_hint_selector_material_dark" id="0x7f060000" />
    <public type="color" name="abc_background_cache_hint_selector_material_light" id="0x7f060001" />
    <public type="color" name="abc_btn_colored_borderless_text_material" id="0x7f060002" />
    <public type="color" name="abc_btn_colored_text_material" id="0x7f060003" />
    <public type="color" name="abc_color_highlight_material" id="0x7f060004" />
    <public type="color" name="abc_decor_view_status_guard" id="0x7f060005" />
    <public type="color" name="abc_decor_view_status_guard_light" id="0x7f060006" />
    <public type="color" name="abc_hint_foreground_material_dark" id="0x7f060007" />
    <public type="color" name="abc_hint_foreground_material_light" id="0x7f060008" />
    <public type="color" name="abc_primary_text_disable_only_material_dark" id="0x7f060009" />
    <public type="color" name="abc_primary_text_disable_only_material_light" id="0x7f06000a" />
    <public type="color" name="abc_primary_text_material_dark" id="0x7f06000b" />
    <public type="color" name="abc_primary_text_material_light" id="0x7f06000c" />
    <public type="color" name="abc_search_url_text" id="0x7f06000d" />
    <public type="color" name="abc_search_url_text_normal" id="0x7f06000e" />
    <public type="color" name="abc_search_url_text_pressed" id="0x7f06000f" />
    <public type="color" name="abc_search_url_text_selected" id="0x7f060010" />
    <public type="color" name="abc_secondary_text_material_dark" id="0x7f060011" />
    <public type="color" name="abc_secondary_text_material_light" id="0x7f060012" />
    <public type="color" name="abc_tint_btn_checkable" id="0x7f060013" />
    <public type="color" name="abc_tint_default" id="0x7f060014" />
    <public type="color" name="abc_tint_edittext" id="0x7f060015" />
    <public type="color" name="abc_tint_seek_thumb" id="0x7f060016" />
    <public type="color" name="abc_tint_spinner" id="0x7f060017" />
    <public type="color" name="abc_tint_switch_track" id="0x7f060018" />
    <public type="color" name="accent_material_dark" id="0x7f060019" />
    <public type="color" name="accent_material_light" id="0x7f06001a" />
    <public type="color" name="androidx_core_ripple_material_light" id="0x7f06001b" />
    <public type="color" name="androidx_core_secondary_text_default_material_light" id="0x7f06001c" />
    <public type="color" name="background_floating_material_dark" id="0x7f06001d" />
    <public type="color" name="background_floating_material_light" id="0x7f06001e" />
    <public type="color" name="background_material_dark" id="0x7f06001f" />
    <public type="color" name="background_material_light" id="0x7f060020" />
    <public type="color" name="black" id="0x7f060021" />
    <public type="color" name="black_overlay" id="0x7f060022" />
    <public type="color" name="black_translucent_light" id="0x7f060023" />
    <public type="color" name="blue" id="0x7f060024" />
    <public type="color" name="blue009" id="0x7f060025" />
    <public type="color" name="blue7bf" id="0x7f060026" />
    <public type="color" name="blue_color" id="0x7f060027" />
    <public type="color" name="bottom_container_bg" id="0x7f060028" />
    <public type="color" name="bright_foreground_disabled_material_dark" id="0x7f060029" />
    <public type="color" name="bright_foreground_disabled_material_light" id="0x7f06002a" />
    <public type="color" name="bright_foreground_inverse_material_dark" id="0x7f06002b" />
    <public type="color" name="bright_foreground_inverse_material_light" id="0x7f06002c" />
    <public type="color" name="bright_foreground_material_dark" id="0x7f06002d" />
    <public type="color" name="bright_foreground_material_light" id="0x7f06002e" />
    <public type="color" name="button_material_dark" id="0x7f06002f" />
    <public type="color" name="button_material_light" id="0x7f060030" />
    <public type="color" name="cardview_dark_background" id="0x7f060031" />
    <public type="color" name="cardview_light_background" id="0x7f060032" />
    <public type="color" name="cardview_shadow_end_color" id="0x7f060033" />
    <public type="color" name="cardview_shadow_start_color" id="0x7f060034" />
    <public type="color" name="checkbox_themeable_attribute_color" id="0x7f060035" />
    <public type="color" name="color0B" id="0x7f060036" />
    <public type="color" name="color9C" id="0x7f060037" />
    <public type="color" name="colorBlueB6" id="0x7f060038" />
    <public type="color" name="colorC4" id="0x7f060039" />
    <public type="color" name="colorDB" id="0x7f06003a" />
    <public type="color" name="colorF6" id="0x7f06003b" />
    <public type="color" name="colorGray97" id="0x7f06003c" />
    <public type="color" name="colorGrayC" id="0x7f06003d" />
    <public type="color" name="colorGrayC3" id="0x7f06003e" />
    <public type="color" name="colorGrayE" id="0x7f06003f" />
    <public type="color" name="colorGrayE3" id="0x7f060040" />
    <public type="color" name="colorGrayEC" id="0x7f060041" />
    <public type="color" name="colorGrayF0" id="0x7f060042" />
    <public type="color" name="colorGrayF3" id="0x7f060043" />
    <public type="color" name="colorLightGray" id="0x7f060044" />
    <public type="color" name="colorLightGrayDivider" id="0x7f060045" />
    <public type="color" name="colorMediumRed" id="0x7f060046" />
    <public type="color" name="colorThemBlue" id="0x7f060047" />
    <public type="color" name="color_dark" id="0x7f060048" />
    <public type="color" name="color_light_black" id="0x7f060049" />
    <public type="color" name="color_text_99" id="0x7f06004a" />
    <public type="color" name="design_bottom_navigation_shadow_color" id="0x7f06004b" />
    <public type="color" name="design_box_stroke_color" id="0x7f06004c" />
    <public type="color" name="design_dark_default_color_background" id="0x7f06004d" />
    <public type="color" name="design_dark_default_color_error" id="0x7f06004e" />
    <public type="color" name="design_dark_default_color_on_background" id="0x7f06004f" />
    <public type="color" name="design_dark_default_color_on_error" id="0x7f060050" />
    <public type="color" name="design_dark_default_color_on_primary" id="0x7f060051" />
    <public type="color" name="design_dark_default_color_on_secondary" id="0x7f060052" />
    <public type="color" name="design_dark_default_color_on_surface" id="0x7f060053" />
    <public type="color" name="design_dark_default_color_primary" id="0x7f060054" />
    <public type="color" name="design_dark_default_color_primary_dark" id="0x7f060055" />
    <public type="color" name="design_dark_default_color_primary_variant" id="0x7f060056" />
    <public type="color" name="design_dark_default_color_secondary" id="0x7f060057" />
    <public type="color" name="design_dark_default_color_secondary_variant" id="0x7f060058" />
    <public type="color" name="design_dark_default_color_surface" id="0x7f060059" />
    <public type="color" name="design_default_color_background" id="0x7f06005a" />
    <public type="color" name="design_default_color_error" id="0x7f06005b" />
    <public type="color" name="design_default_color_on_background" id="0x7f06005c" />
    <public type="color" name="design_default_color_on_error" id="0x7f06005d" />
    <public type="color" name="design_default_color_on_primary" id="0x7f06005e" />
    <public type="color" name="design_default_color_on_secondary" id="0x7f06005f" />
    <public type="color" name="design_default_color_on_surface" id="0x7f060060" />
    <public type="color" name="design_default_color_primary" id="0x7f060061" />
    <public type="color" name="design_default_color_primary_dark" id="0x7f060062" />
    <public type="color" name="design_default_color_primary_variant" id="0x7f060063" />
    <public type="color" name="design_default_color_secondary" id="0x7f060064" />
    <public type="color" name="design_default_color_secondary_variant" id="0x7f060065" />
    <public type="color" name="design_default_color_surface" id="0x7f060066" />
    <public type="color" name="design_error" id="0x7f060067" />
    <public type="color" name="design_fab_shadow_end_color" id="0x7f060068" />
    <public type="color" name="design_fab_shadow_mid_color" id="0x7f060069" />
    <public type="color" name="design_fab_shadow_start_color" id="0x7f06006a" />
    <public type="color" name="design_fab_stroke_end_inner_color" id="0x7f06006b" />
    <public type="color" name="design_fab_stroke_end_outer_color" id="0x7f06006c" />
    <public type="color" name="design_fab_stroke_top_inner_color" id="0x7f06006d" />
    <public type="color" name="design_fab_stroke_top_outer_color" id="0x7f06006e" />
    <public type="color" name="design_icon_tint" id="0x7f06006f" />
    <public type="color" name="design_snackbar_background_color" id="0x7f060070" />
    <public type="color" name="dim_foreground_dark" id="0x7f060071" />
    <public type="color" name="dim_foreground_disabled_material_dark" id="0x7f060072" />
    <public type="color" name="dim_foreground_disabled_material_light" id="0x7f060073" />
    <public type="color" name="dim_foreground_material_dark" id="0x7f060074" />
    <public type="color" name="dim_foreground_material_light" id="0x7f060075" />
    <public type="color" name="divider_color" id="0x7f060076" />
    <public type="color" name="dividing_line_color" id="0x7f060077" />
    <public type="color" name="error_color_material_dark" id="0x7f060078" />
    <public type="color" name="error_color_material_light" id="0x7f060079" />
    <public type="color" name="foreground_material_dark" id="0x7f06007a" />
    <public type="color" name="foreground_material_light" id="0x7f06007b" />
    <public type="color" name="gray" id="0x7f06007c" />
    <public type="color" name="gray_light" id="0x7f06007d" />
    <public type="color" name="grey" id="0x7f06007e" />
    <public type="color" name="grey_light" id="0x7f06007f" />
    <public type="color" name="highlighted_text_material_dark" id="0x7f060080" />
    <public type="color" name="highlighted_text_material_light" id="0x7f060081" />
    <public type="color" name="history_line_bg_color" id="0x7f060082" />
    <public type="color" name="ijk_color_blue_100" id="0x7f060083" />
    <public type="color" name="ijk_color_blue_200" id="0x7f060084" />
    <public type="color" name="ijk_color_blue_300" id="0x7f060085" />
    <public type="color" name="ijk_color_blue_400" id="0x7f060086" />
    <public type="color" name="ijk_color_blue_50" id="0x7f060087" />
    <public type="color" name="ijk_color_blue_500" id="0x7f060088" />
    <public type="color" name="ijk_color_blue_600" id="0x7f060089" />
    <public type="color" name="ijk_color_blue_700" id="0x7f06008a" />
    <public type="color" name="ijk_color_blue_800" id="0x7f06008b" />
    <public type="color" name="ijk_color_blue_900" id="0x7f06008c" />
    <public type="color" name="ijk_color_blue_main" id="0x7f06008d" />
    <public type="color" name="ijk_transparent_dark" id="0x7f06008e" />
    <public type="color" name="item_bg" id="0x7f06008f" />
    <public type="color" name="list_detail_color" id="0x7f060090" />
    <public type="color" name="material_blue_grey_800" id="0x7f060091" />
    <public type="color" name="material_blue_grey_900" id="0x7f060092" />
    <public type="color" name="material_blue_grey_950" id="0x7f060093" />
    <public type="color" name="material_deep_teal_200" id="0x7f060094" />
    <public type="color" name="material_deep_teal_500" id="0x7f060095" />
    <public type="color" name="material_grey_100" id="0x7f060096" />
    <public type="color" name="material_grey_300" id="0x7f060097" />
    <public type="color" name="material_grey_50" id="0x7f060098" />
    <public type="color" name="material_grey_600" id="0x7f060099" />
    <public type="color" name="material_grey_800" id="0x7f06009a" />
    <public type="color" name="material_grey_850" id="0x7f06009b" />
    <public type="color" name="material_grey_900" id="0x7f06009c" />
    <public type="color" name="material_on_background_disabled" id="0x7f06009d" />
    <public type="color" name="material_on_background_emphasis_high_type" id="0x7f06009e" />
    <public type="color" name="material_on_background_emphasis_medium" id="0x7f06009f" />
    <public type="color" name="material_on_primary_disabled" id="0x7f0600a0" />
    <public type="color" name="material_on_primary_emphasis_high_type" id="0x7f0600a1" />
    <public type="color" name="material_on_primary_emphasis_medium" id="0x7f0600a2" />
    <public type="color" name="material_on_surface_disabled" id="0x7f0600a3" />
    <public type="color" name="material_on_surface_emphasis_high_type" id="0x7f0600a4" />
    <public type="color" name="material_on_surface_emphasis_medium" id="0x7f0600a5" />
    <public type="color" name="mtrl_bottom_nav_colored_item_tint" id="0x7f0600a6" />
    <public type="color" name="mtrl_bottom_nav_colored_ripple_color" id="0x7f0600a7" />
    <public type="color" name="mtrl_bottom_nav_item_tint" id="0x7f0600a8" />
    <public type="color" name="mtrl_bottom_nav_ripple_color" id="0x7f0600a9" />
    <public type="color" name="mtrl_btn_bg_color_selector" id="0x7f0600aa" />
    <public type="color" name="mtrl_btn_ripple_color" id="0x7f0600ab" />
    <public type="color" name="mtrl_btn_stroke_color_selector" id="0x7f0600ac" />
    <public type="color" name="mtrl_btn_text_btn_bg_color_selector" id="0x7f0600ad" />
    <public type="color" name="mtrl_btn_text_btn_ripple_color" id="0x7f0600ae" />
    <public type="color" name="mtrl_btn_text_color_disabled" id="0x7f0600af" />
    <public type="color" name="mtrl_btn_text_color_selector" id="0x7f0600b0" />
    <public type="color" name="mtrl_btn_transparent_bg_color" id="0x7f0600b1" />
    <public type="color" name="mtrl_calendar_item_stroke_color" id="0x7f0600b2" />
    <public type="color" name="mtrl_calendar_selected_range" id="0x7f0600b3" />
    <public type="color" name="mtrl_card_view_foreground" id="0x7f0600b4" />
    <public type="color" name="mtrl_card_view_ripple" id="0x7f0600b5" />
    <public type="color" name="mtrl_chip_background_color" id="0x7f0600b6" />
    <public type="color" name="mtrl_chip_close_icon_tint" id="0x7f0600b7" />
    <public type="color" name="mtrl_chip_ripple_color" id="0x7f0600b8" />
    <public type="color" name="mtrl_chip_surface_color" id="0x7f0600b9" />
    <public type="color" name="mtrl_chip_text_color" id="0x7f0600ba" />
    <public type="color" name="mtrl_choice_chip_background_color" id="0x7f0600bb" />
    <public type="color" name="mtrl_choice_chip_ripple_color" id="0x7f0600bc" />
    <public type="color" name="mtrl_choice_chip_text_color" id="0x7f0600bd" />
    <public type="color" name="mtrl_error" id="0x7f0600be" />
    <public type="color" name="mtrl_extended_fab_bg_color_selector" id="0x7f0600bf" />
    <public type="color" name="mtrl_extended_fab_ripple_color" id="0x7f0600c0" />
    <public type="color" name="mtrl_extended_fab_text_color_selector" id="0x7f0600c1" />
    <public type="color" name="mtrl_fab_ripple_color" id="0x7f0600c2" />
    <public type="color" name="mtrl_filled_background_color" id="0x7f0600c3" />
    <public type="color" name="mtrl_filled_icon_tint" id="0x7f0600c4" />
    <public type="color" name="mtrl_filled_stroke_color" id="0x7f0600c5" />
    <public type="color" name="mtrl_indicator_text_color" id="0x7f0600c6" />
    <public type="color" name="mtrl_navigation_item_background_color" id="0x7f0600c7" />
    <public type="color" name="mtrl_navigation_item_icon_tint" id="0x7f0600c8" />
    <public type="color" name="mtrl_navigation_item_text_color" id="0x7f0600c9" />
    <public type="color" name="mtrl_on_primary_text_btn_text_color_selector" id="0x7f0600ca" />
    <public type="color" name="mtrl_outlined_icon_tint" id="0x7f0600cb" />
    <public type="color" name="mtrl_outlined_stroke_color" id="0x7f0600cc" />
    <public type="color" name="mtrl_popupmenu_overlay_color" id="0x7f0600cd" />
    <public type="color" name="mtrl_scrim_color" id="0x7f0600ce" />
    <public type="color" name="mtrl_tabs_colored_ripple_color" id="0x7f0600cf" />
    <public type="color" name="mtrl_tabs_icon_color_selector" id="0x7f0600d0" />
    <public type="color" name="mtrl_tabs_icon_color_selector_colored" id="0x7f0600d1" />
    <public type="color" name="mtrl_tabs_legacy_text_color_selector" id="0x7f0600d2" />
    <public type="color" name="mtrl_tabs_ripple_color" id="0x7f0600d3" />
    <public type="color" name="mtrl_text_btn_text_color_selector" id="0x7f0600d4" />
    <public type="color" name="mtrl_textinput_default_box_stroke_color" id="0x7f0600d5" />
    <public type="color" name="mtrl_textinput_disabled_color" id="0x7f0600d6" />
    <public type="color" name="mtrl_textinput_filled_box_default_background_color" id="0x7f0600d7" />
    <public type="color" name="mtrl_textinput_focused_box_stroke_color" id="0x7f0600d8" />
    <public type="color" name="mtrl_textinput_hovered_box_stroke_color" id="0x7f0600d9" />
    <public type="color" name="notification_action_color_filter" id="0x7f0600da" />
    <public type="color" name="notification_icon_bg_color" id="0x7f0600db" />
    <public type="color" name="notification_material_background_media_default_color" id="0x7f0600dc" />
    <public type="color" name="playing_color" id="0x7f0600dd" />
    <public type="color" name="playing_color2" id="0x7f0600de" />
    <public type="color" name="playing_color3" id="0x7f0600df" />
    <public type="color" name="preference_fallback_accent_color" id="0x7f0600e0" />
    <public type="color" name="primaryColor" id="0x7f0600e1" />
    <public type="color" name="primaryDarkColor" id="0x7f0600e2" />
    <public type="color" name="primaryLightColor" id="0x7f0600e3" />
    <public type="color" name="primaryTextColor" id="0x7f0600e4" />
    <public type="color" name="primaryTextColor60" id="0x7f0600e5" />
    <public type="color" name="primary_dark" id="0x7f0600e6" />
    <public type="color" name="primary_dark_material_dark" id="0x7f0600e7" />
    <public type="color" name="primary_dark_material_light" id="0x7f0600e8" />
    <public type="color" name="primary_material_dark" id="0x7f0600e9" />
    <public type="color" name="primary_material_light" id="0x7f0600ea" />
    <public type="color" name="primary_text_default_material_dark" id="0x7f0600eb" />
    <public type="color" name="primary_text_default_material_light" id="0x7f0600ec" />
    <public type="color" name="primary_text_disabled_material_dark" id="0x7f0600ed" />
    <public type="color" name="primary_text_disabled_material_light" id="0x7f0600ee" />
    <public type="color" name="recover_screen_text" id="0x7f0600ef" />
    <public type="color" name="red" id="0x7f0600f0" />
    <public type="color" name="ripple_material_dark" id="0x7f0600f1" />
    <public type="color" name="ripple_material_light" id="0x7f0600f2" />
    <public type="color" name="secondary_text_default_material_dark" id="0x7f0600f3" />
    <public type="color" name="secondary_text_default_material_light" id="0x7f0600f4" />
    <public type="color" name="secondary_text_disabled_material_dark" id="0x7f0600f5" />
    <public type="color" name="secondary_text_disabled_material_light" id="0x7f0600f6" />
    <public type="color" name="splash_color" id="0x7f0600f7" />
    <public type="color" name="subtitle_color" id="0x7f0600f8" />
    <public type="color" name="switch_thumb_disabled_material_dark" id="0x7f0600f9" />
    <public type="color" name="switch_thumb_disabled_material_light" id="0x7f0600fa" />
    <public type="color" name="switch_thumb_material_dark" id="0x7f0600fb" />
    <public type="color" name="switch_thumb_material_light" id="0x7f0600fc" />
    <public type="color" name="switch_thumb_normal_material_dark" id="0x7f0600fd" />
    <public type="color" name="switch_thumb_normal_material_light" id="0x7f0600fe" />
    <public type="color" name="test_mtrl_calendar_day" id="0x7f0600ff" />
    <public type="color" name="test_mtrl_calendar_day_selected" id="0x7f060100" />
    <public type="color" name="textColorBlack" id="0x7f060101" />
    <public type="color" name="textColorGray" id="0x7f060102" />
    <public type="color" name="textColorGray8" id="0x7f060103" />
    <public type="color" name="textColorGray9" id="0x7f060104" />
    <public type="color" name="textColorGrayA6" id="0x7f060105" />
    <public type="color" name="textColorGrayE6" id="0x7f060106" />
    <public type="color" name="textLightGray" id="0x7f060107" />
    <public type="color" name="title_textcolor" id="0x7f060108" />
    <public type="color" name="tooltip_background_dark" id="0x7f060109" />
    <public type="color" name="tooltip_background_light" id="0x7f06010a" />
    <public type="color" name="transMediumRed" id="0x7f06010b" />
    <public type="color" name="transThemBlue" id="0x7f06010c" />
    <public type="color" name="transWhite" id="0x7f06010d" />
    <public type="color" name="trans_primaryColor" id="0x7f06010e" />
    <public type="color" name="translucent_black" id="0x7f06010f" />
    <public type="color" name="transparent" id="0x7f060110" />
    <public type="color" name="white" id="0x7f060111" />
    <public type="color" name="white_frame_bg" id="0x7f060112" />
    <public type="dimen" name="ImageButtonSize" id="0x7f070000" />
    <public type="dimen" name="abc_action_bar_content_inset_material" id="0x7f070001" />
    <public type="dimen" name="abc_action_bar_content_inset_with_nav" id="0x7f070002" />
    <public type="dimen" name="abc_action_bar_default_height_material" id="0x7f070003" />
    <public type="dimen" name="abc_action_bar_default_padding_end_material" id="0x7f070004" />
    <public type="dimen" name="abc_action_bar_default_padding_start_material" id="0x7f070005" />
    <public type="dimen" name="abc_action_bar_elevation_material" id="0x7f070006" />
    <public type="dimen" name="abc_action_bar_icon_vertical_padding_material" id="0x7f070007" />
    <public type="dimen" name="abc_action_bar_overflow_padding_end_material" id="0x7f070008" />
    <public type="dimen" name="abc_action_bar_overflow_padding_start_material" id="0x7f070009" />
    <public type="dimen" name="abc_action_bar_stacked_max_height" id="0x7f07000a" />
    <public type="dimen" name="abc_action_bar_stacked_tab_max_width" id="0x7f07000b" />
    <public type="dimen" name="abc_action_bar_subtitle_bottom_margin_material" id="0x7f07000c" />
    <public type="dimen" name="abc_action_bar_subtitle_top_margin_material" id="0x7f07000d" />
    <public type="dimen" name="abc_action_button_min_height_material" id="0x7f07000e" />
    <public type="dimen" name="abc_action_button_min_width_material" id="0x7f07000f" />
    <public type="dimen" name="abc_action_button_min_width_overflow_material" id="0x7f070010" />
    <public type="dimen" name="abc_alert_dialog_button_bar_height" id="0x7f070011" />
    <public type="dimen" name="abc_alert_dialog_button_dimen" id="0x7f070012" />
    <public type="dimen" name="abc_button_inset_horizontal_material" id="0x7f070013" />
    <public type="dimen" name="abc_button_inset_vertical_material" id="0x7f070014" />
    <public type="dimen" name="abc_button_padding_horizontal_material" id="0x7f070015" />
    <public type="dimen" name="abc_button_padding_vertical_material" id="0x7f070016" />
    <public type="dimen" name="abc_cascading_menus_min_smallest_width" id="0x7f070017" />
    <public type="dimen" name="abc_config_prefDialogWidth" id="0x7f070018" />
    <public type="dimen" name="abc_control_corner_material" id="0x7f070019" />
    <public type="dimen" name="abc_control_inset_material" id="0x7f07001a" />
    <public type="dimen" name="abc_control_padding_material" id="0x7f07001b" />
    <public type="dimen" name="abc_dialog_corner_radius_material" id="0x7f07001c" />
    <public type="dimen" name="abc_dialog_fixed_height_major" id="0x7f07001d" />
    <public type="dimen" name="abc_dialog_fixed_height_minor" id="0x7f07001e" />
    <public type="dimen" name="abc_dialog_fixed_width_major" id="0x7f07001f" />
    <public type="dimen" name="abc_dialog_fixed_width_minor" id="0x7f070020" />
    <public type="dimen" name="abc_dialog_list_padding_bottom_no_buttons" id="0x7f070021" />
    <public type="dimen" name="abc_dialog_list_padding_top_no_title" id="0x7f070022" />
    <public type="dimen" name="abc_dialog_min_width_major" id="0x7f070023" />
    <public type="dimen" name="abc_dialog_min_width_minor" id="0x7f070024" />
    <public type="dimen" name="abc_dialog_padding_material" id="0x7f070025" />
    <public type="dimen" name="abc_dialog_padding_top_material" id="0x7f070026" />
    <public type="dimen" name="abc_dialog_title_divider_material" id="0x7f070027" />
    <public type="dimen" name="abc_disabled_alpha_material_dark" id="0x7f070028" />
    <public type="dimen" name="abc_disabled_alpha_material_light" id="0x7f070029" />
    <public type="dimen" name="abc_dropdownitem_icon_width" id="0x7f07002a" />
    <public type="dimen" name="abc_dropdownitem_text_padding_left" id="0x7f07002b" />
    <public type="dimen" name="abc_dropdownitem_text_padding_right" id="0x7f07002c" />
    <public type="dimen" name="abc_edit_text_inset_bottom_material" id="0x7f07002d" />
    <public type="dimen" name="abc_edit_text_inset_horizontal_material" id="0x7f07002e" />
    <public type="dimen" name="abc_edit_text_inset_top_material" id="0x7f07002f" />
    <public type="dimen" name="abc_floating_window_z" id="0x7f070030" />
    <public type="dimen" name="abc_list_item_height_large_material" id="0x7f070031" />
    <public type="dimen" name="abc_list_item_height_material" id="0x7f070032" />
    <public type="dimen" name="abc_list_item_height_small_material" id="0x7f070033" />
    <public type="dimen" name="abc_list_item_padding_horizontal_material" id="0x7f070034" />
    <public type="dimen" name="abc_panel_menu_list_width" id="0x7f070035" />
    <public type="dimen" name="abc_progress_bar_height_material" id="0x7f070036" />
    <public type="dimen" name="abc_search_view_preferred_height" id="0x7f070037" />
    <public type="dimen" name="abc_search_view_preferred_width" id="0x7f070038" />
    <public type="dimen" name="abc_seekbar_track_background_height_material" id="0x7f070039" />
    <public type="dimen" name="abc_seekbar_track_progress_height_material" id="0x7f07003a" />
    <public type="dimen" name="abc_select_dialog_padding_start_material" id="0x7f07003b" />
    <public type="dimen" name="abc_switch_padding" id="0x7f07003c" />
    <public type="dimen" name="abc_text_size_body_1_material" id="0x7f07003d" />
    <public type="dimen" name="abc_text_size_body_2_material" id="0x7f07003e" />
    <public type="dimen" name="abc_text_size_button_material" id="0x7f07003f" />
    <public type="dimen" name="abc_text_size_caption_material" id="0x7f070040" />
    <public type="dimen" name="abc_text_size_display_1_material" id="0x7f070041" />
    <public type="dimen" name="abc_text_size_display_2_material" id="0x7f070042" />
    <public type="dimen" name="abc_text_size_display_3_material" id="0x7f070043" />
    <public type="dimen" name="abc_text_size_display_4_material" id="0x7f070044" />
    <public type="dimen" name="abc_text_size_headline_material" id="0x7f070045" />
    <public type="dimen" name="abc_text_size_large_material" id="0x7f070046" />
    <public type="dimen" name="abc_text_size_medium_material" id="0x7f070047" />
    <public type="dimen" name="abc_text_size_menu_header_material" id="0x7f070048" />
    <public type="dimen" name="abc_text_size_menu_material" id="0x7f070049" />
    <public type="dimen" name="abc_text_size_small_material" id="0x7f07004a" />
    <public type="dimen" name="abc_text_size_subhead_material" id="0x7f07004b" />
    <public type="dimen" name="abc_text_size_subtitle_material_toolbar" id="0x7f07004c" />
    <public type="dimen" name="abc_text_size_title_material" id="0x7f07004d" />
    <public type="dimen" name="abc_text_size_title_material_toolbar" id="0x7f07004e" />
    <public type="dimen" name="accinfo_delete_button_width" id="0x7f07004f" />
    <public type="dimen" name="account_img_size" id="0x7f070050" />
    <public type="dimen" name="action_bar_size" id="0x7f070051" />
    <public type="dimen" name="add_button_size" id="0x7f070052" />
    <public type="dimen" name="add_image_size" id="0x7f070053" />
    <public type="dimen" name="appcompat_dialog_background_inset" id="0x7f070054" />
    <public type="dimen" name="arrow_folder_size" id="0x7f070055" />
    <public type="dimen" name="back_image_size" id="0x7f070056" />
    <public type="dimen" name="button_corners_size" id="0x7f070057" />
    <public type="dimen" name="cardview_compat_inset_shadow" id="0x7f070058" />
    <public type="dimen" name="cardview_default_elevation" id="0x7f070059" />
    <public type="dimen" name="cardview_default_radius" id="0x7f07005a" />
    <public type="dimen" name="chapter_image_size" id="0x7f07005b" />
    <public type="dimen" name="close_img_size" id="0x7f07005c" />
    <public type="dimen" name="compat_button_inset_horizontal_material" id="0x7f07005d" />
    <public type="dimen" name="compat_button_inset_vertical_material" id="0x7f07005e" />
    <public type="dimen" name="compat_button_padding_horizontal_material" id="0x7f07005f" />
    <public type="dimen" name="compat_button_padding_vertical_material" id="0x7f070060" />
    <public type="dimen" name="compat_control_corner_material" id="0x7f070061" />
    <public type="dimen" name="compat_notification_large_icon_max_height" id="0x7f070062" />
    <public type="dimen" name="compat_notification_large_icon_max_width" id="0x7f070063" />
    <public type="dimen" name="course_img_height" id="0x7f070064" />
    <public type="dimen" name="course_img_margin" id="0x7f070065" />
    <public type="dimen" name="course_img_size" id="0x7f070066" />
    <public type="dimen" name="course_img_width" id="0x7f070067" />
    <public type="dimen" name="course_list_detail_textsize" id="0x7f070068" />
    <public type="dimen" name="course_list_title_textsize" id="0x7f070069" />
    <public type="dimen" name="course_setail_line_height" id="0x7f07006a" />
    <public type="dimen" name="default_dimension" id="0x7f07006b" />
    <public type="dimen" name="design_appbar_elevation" id="0x7f07006c" />
    <public type="dimen" name="design_bottom_navigation_active_item_max_width" id="0x7f07006d" />
    <public type="dimen" name="design_bottom_navigation_active_item_min_width" id="0x7f07006e" />
    <public type="dimen" name="design_bottom_navigation_active_text_size" id="0x7f07006f" />
    <public type="dimen" name="design_bottom_navigation_elevation" id="0x7f070070" />
    <public type="dimen" name="design_bottom_navigation_height" id="0x7f070071" />
    <public type="dimen" name="design_bottom_navigation_icon_size" id="0x7f070072" />
    <public type="dimen" name="design_bottom_navigation_item_max_width" id="0x7f070073" />
    <public type="dimen" name="design_bottom_navigation_item_min_width" id="0x7f070074" />
    <public type="dimen" name="design_bottom_navigation_margin" id="0x7f070075" />
    <public type="dimen" name="design_bottom_navigation_shadow_height" id="0x7f070076" />
    <public type="dimen" name="design_bottom_navigation_text_size" id="0x7f070077" />
    <public type="dimen" name="design_bottom_sheet_elevation" id="0x7f070078" />
    <public type="dimen" name="design_bottom_sheet_modal_elevation" id="0x7f070079" />
    <public type="dimen" name="design_bottom_sheet_peek_height_min" id="0x7f07007a" />
    <public type="dimen" name="design_fab_border_width" id="0x7f07007b" />
    <public type="dimen" name="design_fab_elevation" id="0x7f07007c" />
    <public type="dimen" name="design_fab_image_size" id="0x7f07007d" />
    <public type="dimen" name="design_fab_size_mini" id="0x7f07007e" />
    <public type="dimen" name="design_fab_size_normal" id="0x7f07007f" />
    <public type="dimen" name="design_fab_translation_z_hovered_focused" id="0x7f070080" />
    <public type="dimen" name="design_fab_translation_z_pressed" id="0x7f070081" />
    <public type="dimen" name="design_navigation_elevation" id="0x7f070082" />
    <public type="dimen" name="design_navigation_icon_padding" id="0x7f070083" />
    <public type="dimen" name="design_navigation_icon_size" id="0x7f070084" />
    <public type="dimen" name="design_navigation_item_horizontal_padding" id="0x7f070085" />
    <public type="dimen" name="design_navigation_item_icon_padding" id="0x7f070086" />
    <public type="dimen" name="design_navigation_max_width" id="0x7f070087" />
    <public type="dimen" name="design_navigation_padding_bottom" id="0x7f070088" />
    <public type="dimen" name="design_navigation_separator_vertical_padding" id="0x7f070089" />
    <public type="dimen" name="design_snackbar_action_inline_max_width" id="0x7f07008a" />
    <public type="dimen" name="design_snackbar_action_text_color_alpha" id="0x7f07008b" />
    <public type="dimen" name="design_snackbar_background_corner_radius" id="0x7f07008c" />
    <public type="dimen" name="design_snackbar_elevation" id="0x7f07008d" />
    <public type="dimen" name="design_snackbar_extra_spacing_horizontal" id="0x7f07008e" />
    <public type="dimen" name="design_snackbar_max_width" id="0x7f07008f" />
    <public type="dimen" name="design_snackbar_min_width" id="0x7f070090" />
    <public type="dimen" name="design_snackbar_padding_horizontal" id="0x7f070091" />
    <public type="dimen" name="design_snackbar_padding_vertical" id="0x7f070092" />
    <public type="dimen" name="design_snackbar_padding_vertical_2lines" id="0x7f070093" />
    <public type="dimen" name="design_snackbar_text_size" id="0x7f070094" />
    <public type="dimen" name="design_tab_max_width" id="0x7f070095" />
    <public type="dimen" name="design_tab_scrollable_min_width" id="0x7f070096" />
    <public type="dimen" name="design_tab_text_size" id="0x7f070097" />
    <public type="dimen" name="design_tab_text_size_2line" id="0x7f070098" />
    <public type="dimen" name="design_textinput_caption_translate_y" id="0x7f070099" />
    <public type="dimen" name="disabled_alpha_material_dark" id="0x7f07009a" />
    <public type="dimen" name="disabled_alpha_material_light" id="0x7f07009b" />
    <public type="dimen" name="download_image_size" id="0x7f07009c" />
    <public type="dimen" name="download_progress_size" id="0x7f07009d" />
    <public type="dimen" name="dp_0" id="0x7f07009e" />
    <public type="dimen" name="dp_0_1" id="0x7f07009f" />
    <public type="dimen" name="dp_0_5" id="0x7f0700a0" />
    <public type="dimen" name="dp_1" id="0x7f0700a1" />
    <public type="dimen" name="dp_10" id="0x7f0700a2" />
    <public type="dimen" name="dp_100" id="0x7f0700a3" />
    <public type="dimen" name="dp_11" id="0x7f0700a4" />
    <public type="dimen" name="dp_110" id="0x7f0700a5" />
    <public type="dimen" name="dp_115" id="0x7f0700a6" />
    <public type="dimen" name="dp_12" id="0x7f0700a7" />
    <public type="dimen" name="dp_120" id="0x7f0700a8" />
    <public type="dimen" name="dp_125" id="0x7f0700a9" />
    <public type="dimen" name="dp_13" id="0x7f0700aa" />
    <public type="dimen" name="dp_130" id="0x7f0700ab" />
    <public type="dimen" name="dp_135" id="0x7f0700ac" />
    <public type="dimen" name="dp_14" id="0x7f0700ad" />
    <public type="dimen" name="dp_140" id="0x7f0700ae" />
    <public type="dimen" name="dp_145" id="0x7f0700af" />
    <public type="dimen" name="dp_15" id="0x7f0700b0" />
    <public type="dimen" name="dp_150" id="0x7f0700b1" />
    <public type="dimen" name="dp_152" id="0x7f0700b2" />
    <public type="dimen" name="dp_155" id="0x7f0700b3" />
    <public type="dimen" name="dp_16" id="0x7f0700b4" />
    <public type="dimen" name="dp_160" id="0x7f0700b5" />
    <public type="dimen" name="dp_165" id="0x7f0700b6" />
    <public type="dimen" name="dp_17" id="0x7f0700b7" />
    <public type="dimen" name="dp_170" id="0x7f0700b8" />
    <public type="dimen" name="dp_18" id="0x7f0700b9" />
    <public type="dimen" name="dp_180" id="0x7f0700ba" />
    <public type="dimen" name="dp_19" id="0x7f0700bb" />
    <public type="dimen" name="dp_190" id="0x7f0700bc" />
    <public type="dimen" name="dp_1_5" id="0x7f0700bd" />
    <public type="dimen" name="dp_2" id="0x7f0700be" />
    <public type="dimen" name="dp_20" id="0x7f0700bf" />
    <public type="dimen" name="dp_200" id="0x7f0700c0" />
    <public type="dimen" name="dp_21" id="0x7f0700c1" />
    <public type="dimen" name="dp_22" id="0x7f0700c2" />
    <public type="dimen" name="dp_220" id="0x7f0700c3" />
    <public type="dimen" name="dp_23" id="0x7f0700c4" />
    <public type="dimen" name="dp_24" id="0x7f0700c5" />
    <public type="dimen" name="dp_240" id="0x7f0700c6" />
    <public type="dimen" name="dp_25" id="0x7f0700c7" />
    <public type="dimen" name="dp_250" id="0x7f0700c8" />
    <public type="dimen" name="dp_26" id="0x7f0700c9" />
    <public type="dimen" name="dp_260" id="0x7f0700ca" />
    <public type="dimen" name="dp_27" id="0x7f0700cb" />
    <public type="dimen" name="dp_270" id="0x7f0700cc" />
    <public type="dimen" name="dp_275" id="0x7f0700cd" />
    <public type="dimen" name="dp_28" id="0x7f0700ce" />
    <public type="dimen" name="dp_280" id="0x7f0700cf" />
    <public type="dimen" name="dp_29" id="0x7f0700d0" />
    <public type="dimen" name="dp_290" id="0x7f0700d1" />
    <public type="dimen" name="dp_2_5" id="0x7f0700d2" />
    <public type="dimen" name="dp_3" id="0x7f0700d3" />
    <public type="dimen" name="dp_30" id="0x7f0700d4" />
    <public type="dimen" name="dp_300" id="0x7f0700d5" />
    <public type="dimen" name="dp_31" id="0x7f0700d6" />
    <public type="dimen" name="dp_32" id="0x7f0700d7" />
    <public type="dimen" name="dp_320" id="0x7f0700d8" />
    <public type="dimen" name="dp_33" id="0x7f0700d9" />
    <public type="dimen" name="dp_34" id="0x7f0700da" />
    <public type="dimen" name="dp_340" id="0x7f0700db" />
    <public type="dimen" name="dp_35" id="0x7f0700dc" />
    <public type="dimen" name="dp_350" id="0x7f0700dd" />
    <public type="dimen" name="dp_36" id="0x7f0700de" />
    <public type="dimen" name="dp_360" id="0x7f0700df" />
    <public type="dimen" name="dp_37" id="0x7f0700e0" />
    <public type="dimen" name="dp_38" id="0x7f0700e1" />
    <public type="dimen" name="dp_380" id="0x7f0700e2" />
    <public type="dimen" name="dp_39" id="0x7f0700e3" />
    <public type="dimen" name="dp_3_5" id="0x7f0700e4" />
    <public type="dimen" name="dp_4" id="0x7f0700e5" />
    <public type="dimen" name="dp_40" id="0x7f0700e6" />
    <public type="dimen" name="dp_400" id="0x7f0700e7" />
    <public type="dimen" name="dp_41" id="0x7f0700e8" />
    <public type="dimen" name="dp_42" id="0x7f0700e9" />
    <public type="dimen" name="dp_43" id="0x7f0700ea" />
    <public type="dimen" name="dp_44" id="0x7f0700eb" />
    <public type="dimen" name="dp_45" id="0x7f0700ec" />
    <public type="dimen" name="dp_46" id="0x7f0700ed" />
    <public type="dimen" name="dp_47" id="0x7f0700ee" />
    <public type="dimen" name="dp_48" id="0x7f0700ef" />
    <public type="dimen" name="dp_49" id="0x7f0700f0" />
    <public type="dimen" name="dp_4_5" id="0x7f0700f1" />
    <public type="dimen" name="dp_5" id="0x7f0700f2" />
    <public type="dimen" name="dp_50" id="0x7f0700f3" />
    <public type="dimen" name="dp_51" id="0x7f0700f4" />
    <public type="dimen" name="dp_52" id="0x7f0700f5" />
    <public type="dimen" name="dp_53" id="0x7f0700f6" />
    <public type="dimen" name="dp_54" id="0x7f0700f7" />
    <public type="dimen" name="dp_55" id="0x7f0700f8" />
    <public type="dimen" name="dp_56" id="0x7f0700f9" />
    <public type="dimen" name="dp_57" id="0x7f0700fa" />
    <public type="dimen" name="dp_58" id="0x7f0700fb" />
    <public type="dimen" name="dp_59" id="0x7f0700fc" />
    <public type="dimen" name="dp_6" id="0x7f0700fd" />
    <public type="dimen" name="dp_60" id="0x7f0700fe" />
    <public type="dimen" name="dp_61" id="0x7f0700ff" />
    <public type="dimen" name="dp_62" id="0x7f070100" />
    <public type="dimen" name="dp_63" id="0x7f070101" />
    <public type="dimen" name="dp_64" id="0x7f070102" />
    <public type="dimen" name="dp_65" id="0x7f070103" />
    <public type="dimen" name="dp_66" id="0x7f070104" />
    <public type="dimen" name="dp_67" id="0x7f070105" />
    <public type="dimen" name="dp_68" id="0x7f070106" />
    <public type="dimen" name="dp_69" id="0x7f070107" />
    <public type="dimen" name="dp_7" id="0x7f070108" />
    <public type="dimen" name="dp_70" id="0x7f070109" />
    <public type="dimen" name="dp_71" id="0x7f07010a" />
    <public type="dimen" name="dp_72" id="0x7f07010b" />
    <public type="dimen" name="dp_73" id="0x7f07010c" />
    <public type="dimen" name="dp_74" id="0x7f07010d" />
    <public type="dimen" name="dp_75" id="0x7f07010e" />
    <public type="dimen" name="dp_76" id="0x7f07010f" />
    <public type="dimen" name="dp_77" id="0x7f070110" />
    <public type="dimen" name="dp_78" id="0x7f070111" />
    <public type="dimen" name="dp_79" id="0x7f070112" />
    <public type="dimen" name="dp_8" id="0x7f070113" />
    <public type="dimen" name="dp_80" id="0x7f070114" />
    <public type="dimen" name="dp_81" id="0x7f070115" />
    <public type="dimen" name="dp_82" id="0x7f070116" />
    <public type="dimen" name="dp_83" id="0x7f070117" />
    <public type="dimen" name="dp_84" id="0x7f070118" />
    <public type="dimen" name="dp_85" id="0x7f070119" />
    <public type="dimen" name="dp_86" id="0x7f07011a" />
    <public type="dimen" name="dp_87" id="0x7f07011b" />
    <public type="dimen" name="dp_88" id="0x7f07011c" />
    <public type="dimen" name="dp_89" id="0x7f07011d" />
    <public type="dimen" name="dp_9" id="0x7f07011e" />
    <public type="dimen" name="dp_90" id="0x7f07011f" />
    <public type="dimen" name="dp_91" id="0x7f070120" />
    <public type="dimen" name="dp_92" id="0x7f070121" />
    <public type="dimen" name="dp_93" id="0x7f070122" />
    <public type="dimen" name="dp_94" id="0x7f070123" />
    <public type="dimen" name="dp_95" id="0x7f070124" />
    <public type="dimen" name="dp_96" id="0x7f070125" />
    <public type="dimen" name="dp_97" id="0x7f070126" />
    <public type="dimen" name="dp_98" id="0x7f070127" />
    <public type="dimen" name="dp_99" id="0x7f070128" />
    <public type="dimen" name="dp_m_1" id="0x7f070129" />
    <public type="dimen" name="dp_m_2" id="0x7f07012a" />
    <public type="dimen" name="dp_m_5" id="0x7f07012b" />
    <public type="dimen" name="dp_m_8" id="0x7f07012c" />
    <public type="dimen" name="elevation" id="0x7f07012d" />
    <public type="dimen" name="empty_image_size" id="0x7f07012e" />
    <public type="dimen" name="expand_size" id="0x7f07012f" />
    <public type="dimen" name="fab_margin" id="0x7f070130" />
    <public type="dimen" name="fastscroll_default_thickness" id="0x7f070131" />
    <public type="dimen" name="fastscroll_margin" id="0x7f070132" />
    <public type="dimen" name="fastscroll_minimum_range" id="0x7f070133" />
    <public type="dimen" name="filename_width" id="0x7f070134" />
    <public type="dimen" name="highlight_alpha_material_colored" id="0x7f070135" />
    <public type="dimen" name="highlight_alpha_material_dark" id="0x7f070136" />
    <public type="dimen" name="highlight_alpha_material_light" id="0x7f070137" />
    <public type="dimen" name="hint_alpha_material_dark" id="0x7f070138" />
    <public type="dimen" name="hint_alpha_material_light" id="0x7f070139" />
    <public type="dimen" name="hint_pressed_alpha_material_dark" id="0x7f07013a" />
    <public type="dimen" name="hint_pressed_alpha_material_light" id="0x7f07013b" />
    <public type="dimen" name="icon_image_size" id="0x7f07013c" />
    <public type="dimen" name="item_margin" id="0x7f07013d" />
    <public type="dimen" name="item_touch_helper_max_drag_scroll_per_frame" id="0x7f07013e" />
    <public type="dimen" name="item_touch_helper_swipe_escape_max_velocity" id="0x7f07013f" />
    <public type="dimen" name="item_touch_helper_swipe_escape_velocity" id="0x7f070140" />
    <public type="dimen" name="kclb_textSize" id="0x7f070141" />
    <public type="dimen" name="list_item_height" id="0x7f070142" />
    <public type="dimen" name="list_item_spacing" id="0x7f070143" />
    <public type="dimen" name="list_item_spacing_half" id="0x7f070144" />
    <public type="dimen" name="login_button_margin" id="0x7f070145" />
    <public type="dimen" name="login_edit_margin" id="0x7f070146" />
    <public type="dimen" name="login_logo_layout_marginTop" id="0x7f070147" />
    <public type="dimen" name="login_logo_marginBottom" id="0x7f070148" />
    <public type="dimen" name="login_padding" id="0x7f070149" />
    <public type="dimen" name="login_title_textSize" id="0x7f07014a" />
    <public type="dimen" name="login_user_image_Size" id="0x7f07014b" />
    <public type="dimen" name="logo_image_size" id="0x7f07014c" />
    <public type="dimen" name="main_folder_size" id="0x7f07014d" />
    <public type="dimen" name="margin" id="0x7f07014e" />
    <public type="dimen" name="marginLeft" id="0x7f07014f" />
    <public type="dimen" name="margin_parent" id="0x7f070150" />
    <public type="dimen" name="material_emphasis_disabled" id="0x7f070151" />
    <public type="dimen" name="material_emphasis_high_type" id="0x7f070152" />
    <public type="dimen" name="material_emphasis_medium" id="0x7f070153" />
    <public type="dimen" name="material_text_view_test_line_height" id="0x7f070154" />
    <public type="dimen" name="material_text_view_test_line_height_override" id="0x7f070155" />
    <public type="dimen" name="mediaplayer_height" id="0x7f070156" />
    <public type="dimen" name="mtrl_alert_dialog_background_inset_bottom" id="0x7f070157" />
    <public type="dimen" name="mtrl_alert_dialog_background_inset_end" id="0x7f070158" />
    <public type="dimen" name="mtrl_alert_dialog_background_inset_start" id="0x7f070159" />
    <public type="dimen" name="mtrl_alert_dialog_background_inset_top" id="0x7f07015a" />
    <public type="dimen" name="mtrl_alert_dialog_picker_background_inset" id="0x7f07015b" />
    <public type="dimen" name="mtrl_badge_horizontal_edge_offset" id="0x7f07015c" />
    <public type="dimen" name="mtrl_badge_long_text_horizontal_padding" id="0x7f07015d" />
    <public type="dimen" name="mtrl_badge_radius" id="0x7f07015e" />
    <public type="dimen" name="mtrl_badge_text_horizontal_edge_offset" id="0x7f07015f" />
    <public type="dimen" name="mtrl_badge_text_size" id="0x7f070160" />
    <public type="dimen" name="mtrl_badge_with_text_radius" id="0x7f070161" />
    <public type="dimen" name="mtrl_bottomappbar_fabOffsetEndMode" id="0x7f070162" />
    <public type="dimen" name="mtrl_bottomappbar_fab_bottom_margin" id="0x7f070163" />
    <public type="dimen" name="mtrl_bottomappbar_fab_cradle_margin" id="0x7f070164" />
    <public type="dimen" name="mtrl_bottomappbar_fab_cradle_rounded_corner_radius" id="0x7f070165" />
    <public type="dimen" name="mtrl_bottomappbar_fab_cradle_vertical_offset" id="0x7f070166" />
    <public type="dimen" name="mtrl_bottomappbar_height" id="0x7f070167" />
    <public type="dimen" name="mtrl_btn_corner_radius" id="0x7f070168" />
    <public type="dimen" name="mtrl_btn_dialog_btn_min_width" id="0x7f070169" />
    <public type="dimen" name="mtrl_btn_disabled_elevation" id="0x7f07016a" />
    <public type="dimen" name="mtrl_btn_disabled_z" id="0x7f07016b" />
    <public type="dimen" name="mtrl_btn_elevation" id="0x7f07016c" />
    <public type="dimen" name="mtrl_btn_focused_z" id="0x7f07016d" />
    <public type="dimen" name="mtrl_btn_hovered_z" id="0x7f07016e" />
    <public type="dimen" name="mtrl_btn_icon_btn_padding_left" id="0x7f07016f" />
    <public type="dimen" name="mtrl_btn_icon_padding" id="0x7f070170" />
    <public type="dimen" name="mtrl_btn_inset" id="0x7f070171" />
    <public type="dimen" name="mtrl_btn_letter_spacing" id="0x7f070172" />
    <public type="dimen" name="mtrl_btn_padding_bottom" id="0x7f070173" />
    <public type="dimen" name="mtrl_btn_padding_left" id="0x7f070174" />
    <public type="dimen" name="mtrl_btn_padding_right" id="0x7f070175" />
    <public type="dimen" name="mtrl_btn_padding_top" id="0x7f070176" />
    <public type="dimen" name="mtrl_btn_pressed_z" id="0x7f070177" />
    <public type="dimen" name="mtrl_btn_stroke_size" id="0x7f070178" />
    <public type="dimen" name="mtrl_btn_text_btn_icon_padding" id="0x7f070179" />
    <public type="dimen" name="mtrl_btn_text_btn_padding_left" id="0x7f07017a" />
    <public type="dimen" name="mtrl_btn_text_btn_padding_right" id="0x7f07017b" />
    <public type="dimen" name="mtrl_btn_text_size" id="0x7f07017c" />
    <public type="dimen" name="mtrl_btn_z" id="0x7f07017d" />
    <public type="dimen" name="mtrl_calendar_action_height" id="0x7f07017e" />
    <public type="dimen" name="mtrl_calendar_action_padding" id="0x7f07017f" />
    <public type="dimen" name="mtrl_calendar_bottom_padding" id="0x7f070180" />
    <public type="dimen" name="mtrl_calendar_content_padding" id="0x7f070181" />
    <public type="dimen" name="mtrl_calendar_day_corner" id="0x7f070182" />
    <public type="dimen" name="mtrl_calendar_day_height" id="0x7f070183" />
    <public type="dimen" name="mtrl_calendar_day_horizontal_padding" id="0x7f070184" />
    <public type="dimen" name="mtrl_calendar_day_today_stroke" id="0x7f070185" />
    <public type="dimen" name="mtrl_calendar_day_vertical_padding" id="0x7f070186" />
    <public type="dimen" name="mtrl_calendar_day_width" id="0x7f070187" />
    <public type="dimen" name="mtrl_calendar_days_of_week_height" id="0x7f070188" />
    <public type="dimen" name="mtrl_calendar_dialog_background_inset" id="0x7f070189" />
    <public type="dimen" name="mtrl_calendar_header_content_padding" id="0x7f07018a" />
    <public type="dimen" name="mtrl_calendar_header_content_padding_fullscreen" id="0x7f07018b" />
    <public type="dimen" name="mtrl_calendar_header_divider_thickness" id="0x7f07018c" />
    <public type="dimen" name="mtrl_calendar_header_height" id="0x7f07018d" />
    <public type="dimen" name="mtrl_calendar_header_height_fullscreen" id="0x7f07018e" />
    <public type="dimen" name="mtrl_calendar_header_selection_line_height" id="0x7f07018f" />
    <public type="dimen" name="mtrl_calendar_header_text_padding" id="0x7f070190" />
    <public type="dimen" name="mtrl_calendar_header_toggle_margin_bottom" id="0x7f070191" />
    <public type="dimen" name="mtrl_calendar_header_toggle_margin_top" id="0x7f070192" />
    <public type="dimen" name="mtrl_calendar_landscape_header_width" id="0x7f070193" />
    <public type="dimen" name="mtrl_calendar_maximum_default_fullscreen_minor_axis" id="0x7f070194" />
    <public type="dimen" name="mtrl_calendar_month_horizontal_padding" id="0x7f070195" />
    <public type="dimen" name="mtrl_calendar_month_vertical_padding" id="0x7f070196" />
    <public type="dimen" name="mtrl_calendar_navigation_bottom_padding" id="0x7f070197" />
    <public type="dimen" name="mtrl_calendar_navigation_height" id="0x7f070198" />
    <public type="dimen" name="mtrl_calendar_navigation_top_padding" id="0x7f070199" />
    <public type="dimen" name="mtrl_calendar_pre_l_text_clip_padding" id="0x7f07019a" />
    <public type="dimen" name="mtrl_calendar_selection_baseline_to_top_fullscreen" id="0x7f07019b" />
    <public type="dimen" name="mtrl_calendar_selection_text_baseline_to_bottom" id="0x7f07019c" />
    <public type="dimen" name="mtrl_calendar_selection_text_baseline_to_bottom_fullscreen" id="0x7f07019d" />
    <public type="dimen" name="mtrl_calendar_selection_text_baseline_to_top" id="0x7f07019e" />
    <public type="dimen" name="mtrl_calendar_text_input_padding_top" id="0x7f07019f" />
    <public type="dimen" name="mtrl_calendar_title_baseline_to_top" id="0x7f0701a0" />
    <public type="dimen" name="mtrl_calendar_title_baseline_to_top_fullscreen" id="0x7f0701a1" />
    <public type="dimen" name="mtrl_calendar_year_corner" id="0x7f0701a2" />
    <public type="dimen" name="mtrl_calendar_year_height" id="0x7f0701a3" />
    <public type="dimen" name="mtrl_calendar_year_horizontal_padding" id="0x7f0701a4" />
    <public type="dimen" name="mtrl_calendar_year_vertical_padding" id="0x7f0701a5" />
    <public type="dimen" name="mtrl_calendar_year_width" id="0x7f0701a6" />
    <public type="dimen" name="mtrl_card_checked_icon_margin" id="0x7f0701a7" />
    <public type="dimen" name="mtrl_card_checked_icon_size" id="0x7f0701a8" />
    <public type="dimen" name="mtrl_card_corner_radius" id="0x7f0701a9" />
    <public type="dimen" name="mtrl_card_dragged_z" id="0x7f0701aa" />
    <public type="dimen" name="mtrl_card_elevation" id="0x7f0701ab" />
    <public type="dimen" name="mtrl_card_spacing" id="0x7f0701ac" />
    <public type="dimen" name="mtrl_chip_pressed_translation_z" id="0x7f0701ad" />
    <public type="dimen" name="mtrl_chip_text_size" id="0x7f0701ae" />
    <public type="dimen" name="mtrl_exposed_dropdown_menu_popup_elevation" id="0x7f0701af" />
    <public type="dimen" name="mtrl_exposed_dropdown_menu_popup_vertical_offset" id="0x7f0701b0" />
    <public type="dimen" name="mtrl_exposed_dropdown_menu_popup_vertical_padding" id="0x7f0701b1" />
    <public type="dimen" name="mtrl_extended_fab_bottom_padding" id="0x7f0701b2" />
    <public type="dimen" name="mtrl_extended_fab_corner_radius" id="0x7f0701b3" />
    <public type="dimen" name="mtrl_extended_fab_disabled_elevation" id="0x7f0701b4" />
    <public type="dimen" name="mtrl_extended_fab_disabled_translation_z" id="0x7f0701b5" />
    <public type="dimen" name="mtrl_extended_fab_elevation" id="0x7f0701b6" />
    <public type="dimen" name="mtrl_extended_fab_end_padding" id="0x7f0701b7" />
    <public type="dimen" name="mtrl_extended_fab_end_padding_icon" id="0x7f0701b8" />
    <public type="dimen" name="mtrl_extended_fab_icon_size" id="0x7f0701b9" />
    <public type="dimen" name="mtrl_extended_fab_icon_text_spacing" id="0x7f0701ba" />
    <public type="dimen" name="mtrl_extended_fab_min_height" id="0x7f0701bb" />
    <public type="dimen" name="mtrl_extended_fab_min_width" id="0x7f0701bc" />
    <public type="dimen" name="mtrl_extended_fab_start_padding" id="0x7f0701bd" />
    <public type="dimen" name="mtrl_extended_fab_start_padding_icon" id="0x7f0701be" />
    <public type="dimen" name="mtrl_extended_fab_top_padding" id="0x7f0701bf" />
    <public type="dimen" name="mtrl_extended_fab_translation_z_base" id="0x7f0701c0" />
    <public type="dimen" name="mtrl_extended_fab_translation_z_hovered_focused" id="0x7f0701c1" />
    <public type="dimen" name="mtrl_extended_fab_translation_z_pressed" id="0x7f0701c2" />
    <public type="dimen" name="mtrl_fab_elevation" id="0x7f0701c3" />
    <public type="dimen" name="mtrl_fab_min_touch_target" id="0x7f0701c4" />
    <public type="dimen" name="mtrl_fab_translation_z_hovered_focused" id="0x7f0701c5" />
    <public type="dimen" name="mtrl_fab_translation_z_pressed" id="0x7f0701c6" />
    <public type="dimen" name="mtrl_high_ripple_default_alpha" id="0x7f0701c7" />
    <public type="dimen" name="mtrl_high_ripple_focused_alpha" id="0x7f0701c8" />
    <public type="dimen" name="mtrl_high_ripple_hovered_alpha" id="0x7f0701c9" />
    <public type="dimen" name="mtrl_high_ripple_pressed_alpha" id="0x7f0701ca" />
    <public type="dimen" name="mtrl_large_touch_target" id="0x7f0701cb" />
    <public type="dimen" name="mtrl_low_ripple_default_alpha" id="0x7f0701cc" />
    <public type="dimen" name="mtrl_low_ripple_focused_alpha" id="0x7f0701cd" />
    <public type="dimen" name="mtrl_low_ripple_hovered_alpha" id="0x7f0701ce" />
    <public type="dimen" name="mtrl_low_ripple_pressed_alpha" id="0x7f0701cf" />
    <public type="dimen" name="mtrl_min_touch_target_size" id="0x7f0701d0" />
    <public type="dimen" name="mtrl_navigation_elevation" id="0x7f0701d1" />
    <public type="dimen" name="mtrl_navigation_item_horizontal_padding" id="0x7f0701d2" />
    <public type="dimen" name="mtrl_navigation_item_icon_padding" id="0x7f0701d3" />
    <public type="dimen" name="mtrl_navigation_item_icon_size" id="0x7f0701d4" />
    <public type="dimen" name="mtrl_navigation_item_shape_horizontal_margin" id="0x7f0701d5" />
    <public type="dimen" name="mtrl_navigation_item_shape_vertical_margin" id="0x7f0701d6" />
    <public type="dimen" name="mtrl_shape_corner_size_large_component" id="0x7f0701d7" />
    <public type="dimen" name="mtrl_shape_corner_size_medium_component" id="0x7f0701d8" />
    <public type="dimen" name="mtrl_shape_corner_size_small_component" id="0x7f0701d9" />
    <public type="dimen" name="mtrl_snackbar_action_text_color_alpha" id="0x7f0701da" />
    <public type="dimen" name="mtrl_snackbar_background_corner_radius" id="0x7f0701db" />
    <public type="dimen" name="mtrl_snackbar_background_overlay_color_alpha" id="0x7f0701dc" />
    <public type="dimen" name="mtrl_snackbar_margin" id="0x7f0701dd" />
    <public type="dimen" name="mtrl_switch_thumb_elevation" id="0x7f0701de" />
    <public type="dimen" name="mtrl_textinput_box_corner_radius_medium" id="0x7f0701df" />
    <public type="dimen" name="mtrl_textinput_box_corner_radius_small" id="0x7f0701e0" />
    <public type="dimen" name="mtrl_textinput_box_label_cutout_padding" id="0x7f0701e1" />
    <public type="dimen" name="mtrl_textinput_box_stroke_width_default" id="0x7f0701e2" />
    <public type="dimen" name="mtrl_textinput_box_stroke_width_focused" id="0x7f0701e3" />
    <public type="dimen" name="mtrl_textinput_end_icon_margin_start" id="0x7f0701e4" />
    <public type="dimen" name="mtrl_textinput_outline_box_expanded_padding" id="0x7f0701e5" />
    <public type="dimen" name="mtrl_textinput_start_icon_margin_end" id="0x7f0701e6" />
    <public type="dimen" name="mtrl_toolbar_default_height" id="0x7f0701e7" />
    <public type="dimen" name="notification_action_icon_size" id="0x7f0701e8" />
    <public type="dimen" name="notification_action_text_size" id="0x7f0701e9" />
    <public type="dimen" name="notification_big_circle_margin" id="0x7f0701ea" />
    <public type="dimen" name="notification_content_margin_start" id="0x7f0701eb" />
    <public type="dimen" name="notification_large_icon_height" id="0x7f0701ec" />
    <public type="dimen" name="notification_large_icon_width" id="0x7f0701ed" />
    <public type="dimen" name="notification_main_column_padding_top" id="0x7f0701ee" />
    <public type="dimen" name="notification_media_narrow_margin" id="0x7f0701ef" />
    <public type="dimen" name="notification_right_icon_size" id="0x7f0701f0" />
    <public type="dimen" name="notification_right_side_padding_top" id="0x7f0701f1" />
    <public type="dimen" name="notification_small_icon_background_padding" id="0x7f0701f2" />
    <public type="dimen" name="notification_small_icon_size_as_large" id="0x7f0701f3" />
    <public type="dimen" name="notification_subtext_size" id="0x7f0701f4" />
    <public type="dimen" name="notification_top_pad" id="0x7f0701f5" />
    <public type="dimen" name="notification_top_pad_large_text" id="0x7f0701f6" />
    <public type="dimen" name="padding_10" id="0x7f0701f7" />
    <public type="dimen" name="padding_12" id="0x7f0701f8" />
    <public type="dimen" name="padding_16" id="0x7f0701f9" />
    <public type="dimen" name="padding_2" id="0x7f0701fa" />
    <public type="dimen" name="padding_5" id="0x7f0701fb" />
    <public type="dimen" name="padding_6" id="0x7f0701fc" />
    <public type="dimen" name="padding_8" id="0x7f0701fd" />
    <public type="dimen" name="play_icon_size" id="0x7f0701fe" />
    <public type="dimen" name="preference_dropdown_padding_start" id="0x7f0701ff" />
    <public type="dimen" name="preference_icon_minWidth" id="0x7f070200" />
    <public type="dimen" name="preference_seekbar_padding_horizontal" id="0x7f070201" />
    <public type="dimen" name="preference_seekbar_padding_vertical" id="0x7f070202" />
    <public type="dimen" name="preference_seekbar_value_minWidth" id="0x7f070203" />
    <public type="dimen" name="progress_stroke_width" id="0x7f070204" />
    <public type="dimen" name="reload_img_size" id="0x7f070205" />
    <public type="dimen" name="setting_dimen" id="0x7f070206" />
    <public type="dimen" name="sp_10" id="0x7f070207" />
    <public type="dimen" name="sp_11" id="0x7f070208" />
    <public type="dimen" name="sp_12" id="0x7f070209" />
    <public type="dimen" name="sp_13" id="0x7f07020a" />
    <public type="dimen" name="sp_14" id="0x7f07020b" />
    <public type="dimen" name="sp_15" id="0x7f07020c" />
    <public type="dimen" name="sp_16" id="0x7f07020d" />
    <public type="dimen" name="sp_17" id="0x7f07020e" />
    <public type="dimen" name="sp_18" id="0x7f07020f" />
    <public type="dimen" name="sp_19" id="0x7f070210" />
    <public type="dimen" name="sp_20" id="0x7f070211" />
    <public type="dimen" name="sp_21" id="0x7f070212" />
    <public type="dimen" name="sp_22" id="0x7f070213" />
    <public type="dimen" name="sp_23" id="0x7f070214" />
    <public type="dimen" name="sp_24" id="0x7f070215" />
    <public type="dimen" name="sp_25" id="0x7f070216" />
    <public type="dimen" name="sp_28" id="0x7f070217" />
    <public type="dimen" name="sp_30" id="0x7f070218" />
    <public type="dimen" name="sp_32" id="0x7f070219" />
    <public type="dimen" name="sp_34" id="0x7f07021a" />
    <public type="dimen" name="sp_36" id="0x7f07021b" />
    <public type="dimen" name="sp_38" id="0x7f07021c" />
    <public type="dimen" name="sp_40" id="0x7f07021d" />
    <public type="dimen" name="sp_42" id="0x7f07021e" />
    <public type="dimen" name="sp_48" id="0x7f07021f" />
    <public type="dimen" name="sp_6" id="0x7f070220" />
    <public type="dimen" name="sp_7" id="0x7f070221" />
    <public type="dimen" name="sp_8" id="0x7f070222" />
    <public type="dimen" name="sp_9" id="0x7f070223" />
    <public type="dimen" name="subtitle_corner_radius" id="0x7f070224" />
    <public type="dimen" name="subtitle_outline_width" id="0x7f070225" />
    <public type="dimen" name="subtitle_shadow_offset" id="0x7f070226" />
    <public type="dimen" name="subtitle_shadow_radius" id="0x7f070227" />
    <public type="dimen" name="test_mtrl_calendar_day_cornerSize" id="0x7f070228" />
    <public type="dimen" name="textSize" id="0x7f070229" />
    <public type="dimen" name="textSize_12" id="0x7f07022a" />
    <public type="dimen" name="text_10" id="0x7f07022b" />
    <public type="dimen" name="text_11" id="0x7f07022c" />
    <public type="dimen" name="text_12" id="0x7f07022d" />
    <public type="dimen" name="text_13" id="0x7f07022e" />
    <public type="dimen" name="text_14" id="0x7f07022f" />
    <public type="dimen" name="text_15" id="0x7f070230" />
    <public type="dimen" name="text_16" id="0x7f070231" />
    <public type="dimen" name="text_17" id="0x7f070232" />
    <public type="dimen" name="text_18" id="0x7f070233" />
    <public type="dimen" name="text_19" id="0x7f070234" />
    <public type="dimen" name="text_20" id="0x7f070235" />
    <public type="dimen" name="text_21" id="0x7f070236" />
    <public type="dimen" name="text_22" id="0x7f070237" />
    <public type="dimen" name="textsize_10" id="0x7f070238" />
    <public type="dimen" name="textsize_14" id="0x7f070239" />
    <public type="dimen" name="tile_image_size" id="0x7f07023a" />
    <public type="dimen" name="titleMargin" id="0x7f07023b" />
    <public type="dimen" name="tjsqzh_button_padding" id="0x7f07023c" />
    <public type="dimen" name="tjsqzh_margin_top" id="0x7f07023d" />
    <public type="dimen" name="tjsqzh_padding_right" id="0x7f07023e" />
    <public type="dimen" name="toolbar_height" id="0x7f07023f" />
    <public type="dimen" name="toolbar_textSize" id="0x7f070240" />
    <public type="dimen" name="tooltip_corner_radius" id="0x7f070241" />
    <public type="dimen" name="tooltip_horizontal_padding" id="0x7f070242" />
    <public type="dimen" name="tooltip_margin" id="0x7f070243" />
    <public type="dimen" name="tooltip_precise_anchor_extra_offset" id="0x7f070244" />
    <public type="dimen" name="tooltip_precise_anchor_threshold" id="0x7f070245" />
    <public type="dimen" name="tooltip_vertical_padding" id="0x7f070246" />
    <public type="dimen" name="tooltip_y_offset_non_touch" id="0x7f070247" />
    <public type="dimen" name="tooltip_y_offset_touch" id="0x7f070248" />
    <public type="dimen" name="treeview_paddingleft" id="0x7f070249" />
    <public type="dimen" name="view_height" id="0x7f07024a" />
    <public type="drawable" name="$avd_hide_password__0" id="0x7f080000" />
    <public type="drawable" name="$avd_hide_password__1" id="0x7f080001" />
    <public type="drawable" name="$avd_hide_password__2" id="0x7f080002" />
    <public type="drawable" name="$avd_show_password__0" id="0x7f080003" />
    <public type="drawable" name="$avd_show_password__1" id="0x7f080004" />
    <public type="drawable" name="$avd_show_password__2" id="0x7f080005" />
    <public type="drawable" name="abc_ab_share_pack_mtrl_alpha" id="0x7f080006" />
    <public type="drawable" name="abc_action_bar_item_background_material" id="0x7f080007" />
    <public type="drawable" name="abc_btn_borderless_material" id="0x7f080008" />
    <public type="drawable" name="abc_btn_check_material" id="0x7f080009" />
    <public type="drawable" name="abc_btn_check_material_anim" id="0x7f08000a" />
    <public type="drawable" name="abc_btn_check_to_on_mtrl_000" id="0x7f08000b" />
    <public type="drawable" name="abc_btn_check_to_on_mtrl_015" id="0x7f08000c" />
    <public type="drawable" name="abc_btn_colored_material" id="0x7f08000d" />
    <public type="drawable" name="abc_btn_default_mtrl_shape" id="0x7f08000e" />
    <public type="drawable" name="abc_btn_radio_material" id="0x7f08000f" />
    <public type="drawable" name="abc_btn_radio_material_anim" id="0x7f080010" />
    <public type="drawable" name="abc_btn_radio_to_on_mtrl_000" id="0x7f080011" />
    <public type="drawable" name="abc_btn_radio_to_on_mtrl_015" id="0x7f080012" />
    <public type="drawable" name="abc_btn_switch_to_on_mtrl_00001" id="0x7f080013" />
    <public type="drawable" name="abc_btn_switch_to_on_mtrl_00012" id="0x7f080014" />
    <public type="drawable" name="abc_cab_background_internal_bg" id="0x7f080015" />
    <public type="drawable" name="abc_cab_background_top_material" id="0x7f080016" />
    <public type="drawable" name="abc_cab_background_top_mtrl_alpha" id="0x7f080017" />
    <public type="drawable" name="abc_control_background_material" id="0x7f080018" />
    <public type="drawable" name="abc_dialog_material_background" id="0x7f080019" />
    <public type="drawable" name="abc_edit_text_material" id="0x7f08001a" />
    <public type="drawable" name="abc_ic_ab_back_material" id="0x7f08001b" />
    <public type="drawable" name="abc_ic_arrow_drop_right_black_24dp" id="0x7f08001c" />
    <public type="drawable" name="abc_ic_clear_material" id="0x7f08001d" />
    <public type="drawable" name="abc_ic_commit_search_api_mtrl_alpha" id="0x7f08001e" />
    <public type="drawable" name="abc_ic_go_search_api_material" id="0x7f08001f" />
    <public type="drawable" name="abc_ic_menu_copy_mtrl_am_alpha" id="0x7f080020" />
    <public type="drawable" name="abc_ic_menu_cut_mtrl_alpha" id="0x7f080021" />
    <public type="drawable" name="abc_ic_menu_overflow_material" id="0x7f080022" />
    <public type="drawable" name="abc_ic_menu_paste_mtrl_am_alpha" id="0x7f080023" />
    <public type="drawable" name="abc_ic_menu_selectall_mtrl_alpha" id="0x7f080024" />
    <public type="drawable" name="abc_ic_menu_share_mtrl_alpha" id="0x7f080025" />
    <public type="drawable" name="abc_ic_search_api_material" id="0x7f080026" />
    <public type="drawable" name="abc_ic_star_black_16dp" id="0x7f080027" />
    <public type="drawable" name="abc_ic_star_black_36dp" id="0x7f080028" />
    <public type="drawable" name="abc_ic_star_black_48dp" id="0x7f080029" />
    <public type="drawable" name="abc_ic_star_half_black_16dp" id="0x7f08002a" />
    <public type="drawable" name="abc_ic_star_half_black_36dp" id="0x7f08002b" />
    <public type="drawable" name="abc_ic_star_half_black_48dp" id="0x7f08002c" />
    <public type="drawable" name="abc_ic_voice_search_api_material" id="0x7f08002d" />
    <public type="drawable" name="abc_item_background_holo_dark" id="0x7f08002e" />
    <public type="drawable" name="abc_item_background_holo_light" id="0x7f08002f" />
    <public type="drawable" name="abc_list_divider_material" id="0x7f080030" />
    <public type="drawable" name="abc_list_divider_mtrl_alpha" id="0x7f080031" />
    <public type="drawable" name="abc_list_focused_holo" id="0x7f080032" />
    <public type="drawable" name="abc_list_longpressed_holo" id="0x7f080033" />
    <public type="drawable" name="abc_list_pressed_holo_dark" id="0x7f080034" />
    <public type="drawable" name="abc_list_pressed_holo_light" id="0x7f080035" />
    <public type="drawable" name="abc_list_selector_background_transition_holo_dark" id="0x7f080036" />
    <public type="drawable" name="abc_list_selector_background_transition_holo_light" id="0x7f080037" />
    <public type="drawable" name="abc_list_selector_disabled_holo_dark" id="0x7f080038" />
    <public type="drawable" name="abc_list_selector_disabled_holo_light" id="0x7f080039" />
    <public type="drawable" name="abc_list_selector_holo_dark" id="0x7f08003a" />
    <public type="drawable" name="abc_list_selector_holo_light" id="0x7f08003b" />
    <public type="drawable" name="abc_menu_hardkey_panel_mtrl_mult" id="0x7f08003c" />
    <public type="drawable" name="abc_popup_background_mtrl_mult" id="0x7f08003d" />
    <public type="drawable" name="abc_ratingbar_indicator_material" id="0x7f08003e" />
    <public type="drawable" name="abc_ratingbar_material" id="0x7f08003f" />
    <public type="drawable" name="abc_ratingbar_small_material" id="0x7f080040" />
    <public type="drawable" name="abc_scrubber_control_off_mtrl_alpha" id="0x7f080041" />
    <public type="drawable" name="abc_scrubber_control_to_pressed_mtrl_000" id="0x7f080042" />
    <public type="drawable" name="abc_scrubber_control_to_pressed_mtrl_005" id="0x7f080043" />
    <public type="drawable" name="abc_scrubber_primary_mtrl_alpha" id="0x7f080044" />
    <public type="drawable" name="abc_scrubber_track_mtrl_alpha" id="0x7f080045" />
    <public type="drawable" name="abc_seekbar_thumb_material" id="0x7f080046" />
    <public type="drawable" name="abc_seekbar_tick_mark_material" id="0x7f080047" />
    <public type="drawable" name="abc_seekbar_track_material" id="0x7f080048" />
    <public type="drawable" name="abc_spinner_mtrl_am_alpha" id="0x7f080049" />
    <public type="drawable" name="abc_spinner_textfield_background_material" id="0x7f08004a" />
    <public type="drawable" name="abc_switch_thumb_material" id="0x7f08004b" />
    <public type="drawable" name="abc_switch_track_mtrl_alpha" id="0x7f08004c" />
    <public type="drawable" name="abc_tab_indicator_material" id="0x7f08004d" />
    <public type="drawable" name="abc_tab_indicator_mtrl_alpha" id="0x7f08004e" />
    <public type="drawable" name="abc_text_cursor_material" id="0x7f08004f" />
    <public type="drawable" name="abc_text_select_handle_left_mtrl_dark" id="0x7f080050" />
    <public type="drawable" name="abc_text_select_handle_left_mtrl_light" id="0x7f080051" />
    <public type="drawable" name="abc_text_select_handle_middle_mtrl_dark" id="0x7f080052" />
    <public type="drawable" name="abc_text_select_handle_middle_mtrl_light" id="0x7f080053" />
    <public type="drawable" name="abc_text_select_handle_right_mtrl_dark" id="0x7f080054" />
    <public type="drawable" name="abc_text_select_handle_right_mtrl_light" id="0x7f080055" />
    <public type="drawable" name="abc_textfield_activated_mtrl_alpha" id="0x7f080056" />
    <public type="drawable" name="abc_textfield_default_mtrl_alpha" id="0x7f080057" />
    <public type="drawable" name="abc_textfield_search_activated_mtrl_alpha" id="0x7f080058" />
    <public type="drawable" name="abc_textfield_search_default_mtrl_alpha" id="0x7f080059" />
    <public type="drawable" name="abc_textfield_search_material" id="0x7f08005a" />
    <public type="drawable" name="abc_vector_test" id="0x7f08005b" />
    <public type="drawable" name="add_selector" id="0x7f08005c" />
    <public type="drawable" name="add_white" id="0x7f08005d" />
    <public type="drawable" name="add_white_blue" id="0x7f08005e" />
    <public type="drawable" name="adduser" id="0x7f08005f" />
    <public type="drawable" name="adduser_selector" id="0x7f080060" />
    <public type="drawable" name="adduser_white" id="0x7f080061" />
    <public type="drawable" name="agree_button_blue" id="0x7f080062" />
    <public type="drawable" name="agree_button_gray" id="0x7f080063" />
    <public type="drawable" name="agree_button_style" id="0x7f080064" />
    <public type="drawable" name="agree_button_white" id="0x7f080065" />
    <public type="drawable" name="audio" id="0x7f080066" />
    <public type="drawable" name="audiobg" id="0x7f080067" />
    <public type="drawable" name="avd_hide_password" id="0x7f080068" />
    <public type="drawable" name="avd_show_password" id="0x7f080069" />
    <public type="drawable" name="btn_checkbox_checked_mtrl" id="0x7f08006a" />
    <public type="drawable" name="btn_checkbox_checked_to_unchecked_mtrl_animation" id="0x7f08006b" />
    <public type="drawable" name="btn_checkbox_unchecked_mtrl" id="0x7f08006c" />
    <public type="drawable" name="btn_checkbox_unchecked_to_checked_mtrl_animation" id="0x7f08006d" />
    <public type="drawable" name="btn_radio_off_mtrl" id="0x7f08006e" />
    <public type="drawable" name="btn_radio_off_to_on_mtrl_animation" id="0x7f08006f" />
    <public type="drawable" name="btn_radio_on_mtrl" id="0x7f080070" />
    <public type="drawable" name="btn_radio_on_to_off_mtrl_animation" id="0x7f080071" />
    <public type="drawable" name="button_font_style" id="0x7f080072" />
    <public type="drawable" name="click_error_selector" id="0x7f080073" />
    <public type="drawable" name="comm_audio" id="0x7f080074" />
    <public type="drawable" name="confirm_dialog_bg" id="0x7f080075" />
    <public type="drawable" name="design_bottom_navigation_item_background" id="0x7f080076" />
    <public type="drawable" name="design_fab_background" id="0x7f080077" />
    <public type="drawable" name="design_ic_visibility" id="0x7f080078" />
    <public type="drawable" name="design_ic_visibility_off" id="0x7f080079" />
    <public type="drawable" name="design_password_eye" id="0x7f08007a" />
    <public type="drawable" name="design_snackbar_background" id="0x7f08007b" />
    <public type="drawable" name="empty_drawable" id="0x7f08007c" />
    <public type="drawable" name="gif_playing" id="0x7f08007d" />
    <public type="drawable" name="history_line_bg_color" id="0x7f08007e" />
    <public type="drawable" name="ic_ab_loop_act" id="0x7f08007f" />
    <public type="drawable" name="ic_ab_loop_close" id="0x7f080080" />
    <public type="drawable" name="ic_action_dark_aspect_ratio" id="0x7f080081" />
    <public type="drawable" name="ic_action_dark_filter" id="0x7f080082" />
    <public type="drawable" name="ic_action_dark_settings" id="0x7f080083" />
    <public type="drawable" name="ic_arrow_down_24dp" id="0x7f080084" />
    <public type="drawable" name="ic_calendar_black_24dp" id="0x7f080085" />
    <public type="drawable" name="ic_clear_black_24dp" id="0x7f080086" />
    <public type="drawable" name="ic_close" id="0x7f080087" />
    <public type="drawable" name="ic_edit_black_24dp" id="0x7f080088" />
    <public type="drawable" name="ic_float_window" id="0x7f080089" />
    <public type="drawable" name="ic_icon_lock" id="0x7f08008a" />
    <public type="drawable" name="ic_icon_unlock" id="0x7f08008b" />
    <public type="drawable" name="ic_keyboard_arrow_left_black_24dp" id="0x7f08008c" />
    <public type="drawable" name="ic_keyboard_arrow_right_black_24dp" id="0x7f08008d" />
    <public type="drawable" name="ic_launcher" id="0x7f08008e" />
    <public type="drawable" name="ic_launcher_background" id="0x7f08008f" />
    <public type="drawable" name="ic_logo" id="0x7f080090" />
    <public type="drawable" name="ic_menu_arrow_down_black_24dp" id="0x7f080091" />
    <public type="drawable" name="ic_menu_arrow_up_black_24dp" id="0x7f080092" />
    <public type="drawable" name="ic_mtrl_checked_circle" id="0x7f080093" />
    <public type="drawable" name="ic_mtrl_chip_checked_black" id="0x7f080094" />
    <public type="drawable" name="ic_mtrl_chip_checked_circle" id="0x7f080095" />
    <public type="drawable" name="ic_mtrl_chip_close_circle" id="0x7f080096" />
    <public type="drawable" name="ic_player_backward" id="0x7f080097" />
    <public type="drawable" name="ic_player_forward" id="0x7f080098" />
    <public type="drawable" name="ic_subtitle_off" id="0x7f080099" />
    <public type="drawable" name="ic_subtitle_on" id="0x7f08009a" />
    <public type="drawable" name="ic_theme_description" id="0x7f08009b" />
    <public type="drawable" name="ic_theme_folder" id="0x7f08009c" />
    <public type="drawable" name="ic_theme_play_arrow" id="0x7f08009d" />
    <public type="drawable" name="ic_video_more" id="0x7f08009e" />
    <public type="drawable" name="icon_arrow_right" id="0x7f08009f" />
    <public type="drawable" name="icon_close" id="0x7f0800a0" />
    <public type="drawable" name="icon_download" id="0x7f0800a1" />
    <public type="drawable" name="icon_media_next" id="0x7f0800a2" />
    <public type="drawable" name="icon_music_green" id="0x7f0800a3" />
    <public type="drawable" name="icon_net_loss" id="0x7f0800a4" />
    <public type="drawable" name="icon_ori_hor" id="0x7f0800a5" />
    <public type="drawable" name="icon_ori_ver" id="0x7f0800a6" />
    <public type="drawable" name="icon_video_green" id="0x7f0800a7" />
    <public type="drawable" name="img" id="0x7f0800a8" />
    <public type="drawable" name="light_100" id="0x7f0800a9" />
    <public type="drawable" name="loading" id="0x7f0800aa" />
    <public type="drawable" name="loading_circle" id="0x7f0800ab" />
    <public type="drawable" name="loading_gif" id="0x7f0800ac" />
    <public type="drawable" name="login_button" id="0x7f0800ad" />
    <public type="drawable" name="login_button_selector" id="0x7f0800ae" />
    <public type="drawable" name="login_button_white" id="0x7f0800af" />
    <public type="drawable" name="media_gradient_background" id="0x7f0800b0" />
    <public type="drawable" name="menu_back_bg" id="0x7f0800b1" />
    <public type="drawable" name="menu_set_bg" id="0x7f0800b2" />
    <public type="drawable" name="mtrl_dialog_background" id="0x7f0800b3" />
    <public type="drawable" name="mtrl_dropdown_arrow" id="0x7f0800b4" />
    <public type="drawable" name="mtrl_ic_arrow_drop_down" id="0x7f0800b5" />
    <public type="drawable" name="mtrl_ic_arrow_drop_up" id="0x7f0800b6" />
    <public type="drawable" name="mtrl_ic_cancel" id="0x7f0800b7" />
    <public type="drawable" name="mtrl_ic_error" id="0x7f0800b8" />
    <public type="drawable" name="mtrl_popupmenu_background" id="0x7f0800b9" />
    <public type="drawable" name="mtrl_popupmenu_background_dark" id="0x7f0800ba" />
    <public type="drawable" name="mtrl_tabs_default_indicator" id="0x7f0800bb" />
    <public type="drawable" name="music" id="0x7f0800bc" />
    <public type="drawable" name="navigation_empty_icon" id="0x7f0800bd" />
    <public type="drawable" name="notification_action_background" id="0x7f0800be" />
    <public type="drawable" name="notification_bg" id="0x7f0800bf" />
    <public type="drawable" name="notification_bg_low" id="0x7f0800c0" />
    <public type="drawable" name="notification_bg_low_normal" id="0x7f0800c1" />
    <public type="drawable" name="notification_bg_low_pressed" id="0x7f0800c2" />
    <public type="drawable" name="notification_bg_normal" id="0x7f0800c3" />
    <public type="drawable" name="notification_bg_normal_pressed" id="0x7f0800c4" />
    <public type="drawable" name="notification_icon_background" id="0x7f0800c5" />
    <public type="drawable" name="notification_template_icon_bg" id="0x7f0800c6" />
    <public type="drawable" name="notification_template_icon_low_bg" id="0x7f0800c7" />
    <public type="drawable" name="notification_tile_bg" id="0x7f0800c8" />
    <public type="drawable" name="notify_panel_notification_icon_bg" id="0x7f0800c9" />
    <public type="drawable" name="option_bg_shape" id="0x7f0800ca" />
    <public type="drawable" name="pause" id="0x7f0800cb" />
    <public type="drawable" name="play" id="0x7f0800cc" />
    <public type="drawable" name="play_icon" id="0x7f0800cd" />
    <public type="drawable" name="player_setting_bright_progressbar" id="0x7f0800ce" />
    <public type="drawable" name="player_settings_bright_thumb" id="0x7f0800cf" />
    <public type="drawable" name="playerbar_add_icon" id="0x7f0800d0" />
    <public type="drawable" name="playerbar_back_icon" id="0x7f0800d1" />
    <public type="drawable" name="preference_list_divider_material" id="0x7f0800d2" />
    <public type="drawable" name="reloadbg" id="0x7f0800d3" />
    <public type="drawable" name="seek_thumb" id="0x7f0800d4" />
    <public type="drawable" name="selector_black_round_bg" id="0x7f0800d5" />
    <public type="drawable" name="selector_border_gray_bg" id="0x7f0800d6" />
    <public type="drawable" name="selector_border_white_bg" id="0x7f0800d7" />
    <public type="drawable" name="selector_btn_border2_bg" id="0x7f0800d8" />
    <public type="drawable" name="selector_btn_border3_bg" id="0x7f0800d9" />
    <public type="drawable" name="selector_btn_border_bg" id="0x7f0800da" />
    <public type="drawable" name="selector_btn_comme_bg" id="0x7f0800db" />
    <public type="drawable" name="selector_btn_round_bg" id="0x7f0800dc" />
    <public type="drawable" name="selector_btn_text_color" id="0x7f0800dd" />
    <public type="drawable" name="selector_item_click_bg" id="0x7f0800de" />
    <public type="drawable" name="selector_item_click_bg2" id="0x7f0800df" />
    <public type="drawable" name="selector_item_click_bg3" id="0x7f0800e0" />
    <public type="drawable" name="selector_text_white_blue_color" id="0x7f0800e1" />
    <public type="drawable" name="setting_bright_seekbar_background" id="0x7f0800e2" />
    <public type="drawable" name="setting_bright_seekbar_progress" id="0x7f0800e3" />
    <public type="drawable" name="setting_bright_seekbar_secondprogress" id="0x7f0800e4" />
    <public type="drawable" name="shap_white_round_10dp_bg" id="0x7f0800e5" />
    <public type="drawable" name="shape_black_4dp_bg" id="0x7f0800e6" />
    <public type="drawable" name="shape_black_border_4dp_bg" id="0x7f0800e7" />
    <public type="drawable" name="shape_black_round_60" id="0x7f0800e8" />
    <public type="drawable" name="shape_black_round_75" id="0x7f0800e9" />
    <public type="drawable" name="shape_black_round_tran" id="0x7f0800ea" />
    <public type="drawable" name="shape_blue_point" id="0x7f0800eb" />
    <public type="drawable" name="shape_broder_blue_4dp_bg" id="0x7f0800ec" />
    <public type="drawable" name="shape_gray_bg" id="0x7f0800ed" />
    <public type="drawable" name="shape_gray_bg2" id="0x7f0800ee" />
    <public type="drawable" name="shape_gray_bg_f0" id="0x7f0800ef" />
    <public type="drawable" name="shape_gray_border_4dp_bg" id="0x7f0800f0" />
    <public type="drawable" name="shape_gray_flag_bg" id="0x7f0800f1" />
    <public type="drawable" name="shape_gray_ligt_round_bg" id="0x7f0800f2" />
    <public type="drawable" name="shape_gray_round_bg" id="0x7f0800f3" />
    <public type="drawable" name="shape_item_bottom_line" id="0x7f0800f4" />
    <public type="drawable" name="shape_item_divider" id="0x7f0800f5" />
    <public type="drawable" name="shape_red_corner_bg" id="0x7f0800f6" />
    <public type="drawable" name="shape_red_round_bg" id="0x7f0800f7" />
    <public type="drawable" name="shape_round_blue2_4dp_bg" id="0x7f0800f8" />
    <public type="drawable" name="shape_round_blue_4dp_bg" id="0x7f0800f9" />
    <public type="drawable" name="shape_round_gray_4dp_bg" id="0x7f0800fa" />
    <public type="drawable" name="shape_sheet_dialog_bg" id="0x7f0800fb" />
    <public type="drawable" name="shape_white_20_bg" id="0x7f0800fc" />
    <public type="drawable" name="shape_white_bg" id="0x7f0800fd" />
    <public type="drawable" name="shape_white_round_bg" id="0x7f0800fe" />
    <public type="drawable" name="shortcut" id="0x7f0800ff" />
    <public type="drawable" name="splash_button_bg" id="0x7f080100" />
    <public type="drawable" name="test_custom_background" id="0x7f080101" />
    <public type="drawable" name="thumb" id="0x7f080102" />
    <public type="drawable" name="thumb_hover" id="0x7f080103" />
    <public type="drawable" name="toolbar_gradient_background" id="0x7f080104" />
    <public type="drawable" name="tooltip_frame_dark" id="0x7f080105" />
    <public type="drawable" name="tooltip_frame_light" id="0x7f080106" />
    <public type="drawable" name="video" id="0x7f080107" />
    <public type="drawable" name="video_dialog_progress" id="0x7f080108" />
    <public type="drawable" name="video_dialog_progress_bg" id="0x7f080109" />
    <public type="drawable" name="video_jump_btn_bg" id="0x7f08010a" />
    <public type="drawable" name="video_player_bg_color" id="0x7f08010b" />
    <public type="drawable" name="video_progress" id="0x7f08010c" />
    <public type="drawable" name="video_seek_progress" id="0x7f08010d" />
    <public type="drawable" name="video_seek_thumb" id="0x7f08010e" />
    <public type="drawable" name="video_seek_thumb_normal" id="0x7f08010f" />
    <public type="drawable" name="video_seek_thumb_press" id="0x7f080110" />
    <public type="drawable" name="video_seek_thumb_pressed" id="0x7f080111" />
    <public type="drawable" name="video_switch_close" id="0x7f080112" />
    <public type="drawable" name="video_switch_open" id="0x7f080113" />
    <public type="drawable" name="video_title_bg" id="0x7f080114" />
    <public type="drawable" name="video_title_bg_trans" id="0x7f080115" />
    <public type="drawable" name="video_volume_progress_bg" id="0x7f080116" />
    <public type="drawable" name="video_volumn_bg" id="0x7f080117" />
    <public type="drawable" name="vvc_ic_media_ff" id="0x7f080118" />
    <public type="drawable" name="vvc_ic_media_next" id="0x7f080119" />
    <public type="drawable" name="vvc_ic_media_pause" id="0x7f08011a" />
    <public type="drawable" name="vvc_ic_media_play" id="0x7f08011b" />
    <public type="drawable" name="vvc_ic_media_previous" id="0x7f08011c" />
    <public type="drawable" name="vvc_ic_media_rew" id="0x7f08011d" />
    <public type="id" name="ALT" id="0x7f090000" />
    <public type="id" name="BOTTOM_END" id="0x7f090001" />
    <public type="id" name="BOTTOM_START" id="0x7f090002" />
    <public type="id" name="CTRL" id="0x7f090003" />
    <public type="id" name="FUNCTION" id="0x7f090004" />
    <public type="id" name="META" id="0x7f090005" />
    <public type="id" name="NO_DEBUG" id="0x7f090006" />
    <public type="id" name="SHIFT" id="0x7f090007" />
    <public type="id" name="SHOW_ALL" id="0x7f090008" />
    <public type="id" name="SHOW_PATH" id="0x7f090009" />
    <public type="id" name="SHOW_PROGRESS" id="0x7f09000a" />
    <public type="id" name="SYM" id="0x7f09000b" />
    <public type="id" name="TOP_END" id="0x7f09000c" />
    <public type="id" name="TOP_START" id="0x7f09000d" />
    <public type="id" name="about_us" id="0x7f09000e" />
    <public type="id" name="accelerate" id="0x7f09000f" />
    <public type="id" name="accessibility_action_clickable_span" id="0x7f090010" />
    <public type="id" name="accessibility_custom_action_0" id="0x7f090011" />
    <public type="id" name="accessibility_custom_action_1" id="0x7f090012" />
    <public type="id" name="accessibility_custom_action_10" id="0x7f090013" />
    <public type="id" name="accessibility_custom_action_11" id="0x7f090014" />
    <public type="id" name="accessibility_custom_action_12" id="0x7f090015" />
    <public type="id" name="accessibility_custom_action_13" id="0x7f090016" />
    <public type="id" name="accessibility_custom_action_14" id="0x7f090017" />
    <public type="id" name="accessibility_custom_action_15" id="0x7f090018" />
    <public type="id" name="accessibility_custom_action_16" id="0x7f090019" />
    <public type="id" name="accessibility_custom_action_17" id="0x7f09001a" />
    <public type="id" name="accessibility_custom_action_18" id="0x7f09001b" />
    <public type="id" name="accessibility_custom_action_19" id="0x7f09001c" />
    <public type="id" name="accessibility_custom_action_2" id="0x7f09001d" />
    <public type="id" name="accessibility_custom_action_20" id="0x7f09001e" />
    <public type="id" name="accessibility_custom_action_21" id="0x7f09001f" />
    <public type="id" name="accessibility_custom_action_22" id="0x7f090020" />
    <public type="id" name="accessibility_custom_action_23" id="0x7f090021" />
    <public type="id" name="accessibility_custom_action_24" id="0x7f090022" />
    <public type="id" name="accessibility_custom_action_25" id="0x7f090023" />
    <public type="id" name="accessibility_custom_action_26" id="0x7f090024" />
    <public type="id" name="accessibility_custom_action_27" id="0x7f090025" />
    <public type="id" name="accessibility_custom_action_28" id="0x7f090026" />
    <public type="id" name="accessibility_custom_action_29" id="0x7f090027" />
    <public type="id" name="accessibility_custom_action_3" id="0x7f090028" />
    <public type="id" name="accessibility_custom_action_30" id="0x7f090029" />
    <public type="id" name="accessibility_custom_action_31" id="0x7f09002a" />
    <public type="id" name="accessibility_custom_action_4" id="0x7f09002b" />
    <public type="id" name="accessibility_custom_action_5" id="0x7f09002c" />
    <public type="id" name="accessibility_custom_action_6" id="0x7f09002d" />
    <public type="id" name="accessibility_custom_action_7" id="0x7f09002e" />
    <public type="id" name="accessibility_custom_action_8" id="0x7f09002f" />
    <public type="id" name="accessibility_custom_action_9" id="0x7f090030" />
    <public type="id" name="action0" id="0x7f090031" />
    <public type="id" name="actionDown" id="0x7f090032" />
    <public type="id" name="actionDownUp" id="0x7f090033" />
    <public type="id" name="actionUp" id="0x7f090034" />
    <public type="id" name="action_bar" id="0x7f090035" />
    <public type="id" name="action_bar_activity_content" id="0x7f090036" />
    <public type="id" name="action_bar_container" id="0x7f090037" />
    <public type="id" name="action_bar_root" id="0x7f090038" />
    <public type="id" name="action_bar_spinner" id="0x7f090039" />
    <public type="id" name="action_bar_subtitle" id="0x7f09003a" />
    <public type="id" name="action_bar_title" id="0x7f09003b" />
    <public type="id" name="action_commit" id="0x7f09003c" />
    <public type="id" name="action_container" id="0x7f09003d" />
    <public type="id" name="action_context_bar" id="0x7f09003e" />
    <public type="id" name="action_divider" id="0x7f09003f" />
    <public type="id" name="action_image" id="0x7f090040" />
    <public type="id" name="action_menu_divider" id="0x7f090041" />
    <public type="id" name="action_menu_presenter" id="0x7f090042" />
    <public type="id" name="action_mode_bar" id="0x7f090043" />
    <public type="id" name="action_mode_bar_stub" id="0x7f090044" />
    <public type="id" name="action_mode_close_button" id="0x7f090045" />
    <public type="id" name="action_settings" id="0x7f090046" />
    <public type="id" name="action_text" id="0x7f090047" />
    <public type="id" name="actions" id="0x7f090048" />
    <public type="id" name="activity_chooser_view_content" id="0x7f090049" />
    <public type="id" name="ad_time" id="0x7f09004a" />
    <public type="id" name="add" id="0x7f09004b" />
    <public type="id" name="addImg" id="0x7f09004c" />
    <public type="id" name="addUser" id="0x7f09004d" />
    <public type="id" name="add_button" id="0x7f09004e" />
    <public type="id" name="add_user_line" id="0x7f09004f" />
    <public type="id" name="agreeline" id="0x7f090050" />
    <public type="id" name="agreementTv" id="0x7f090051" />
    <public type="id" name="alertTitle" id="0x7f090052" />
    <public type="id" name="aligned" id="0x7f090053" />
    <public type="id" name="all" id="0x7f090054" />
    <public type="id" name="allStates" id="0x7f090055" />
    <public type="id" name="always" id="0x7f090056" />
    <public type="id" name="animateToEnd" id="0x7f090057" />
    <public type="id" name="animateToStart" id="0x7f090058" />
    <public type="id" name="antiClockwise" id="0x7f090059" />
    <public type="id" name="anticipate" id="0x7f09005a" />
    <public type="id" name="appVersion" id="0x7f09005b" />
    <public type="id" name="app_bar" id="0x7f09005c" />
    <public type="id" name="app_video_brightness" id="0x7f09005d" />
    <public type="id" name="app_video_brightness_box" id="0x7f09005e" />
    <public type="id" name="app_video_brightness_icon" id="0x7f09005f" />
    <public type="id" name="arrowIv" id="0x7f090060" />
    <public type="id" name="asConfigured" id="0x7f090061" />
    <public type="id" name="async" id="0x7f090062" />
    <public type="id" name="auto" id="0x7f090063" />
    <public type="id" name="autoComplete" id="0x7f090064" />
    <public type="id" name="autoCompleteToEnd" id="0x7f090065" />
    <public type="id" name="autoCompleteToStart" id="0x7f090066" />
    <public type="id" name="back" id="0x7f090067" />
    <public type="id" name="backIV" id="0x7f090068" />
    <public type="id" name="backPlay" id="0x7f090069" />
    <public type="id" name="backPlaySwitch" id="0x7f09006a" />
    <public type="id" name="backTv" id="0x7f09006b" />
    <public type="id" name="back_buffer" id="0x7f09006c" />
    <public type="id" name="back_tiny" id="0x7f09006d" />
    <public type="id" name="banner_data_key" id="0x7f09006e" />
    <public type="id" name="banner_pos_key" id="0x7f09006f" />
    <public type="id" name="barrier" id="0x7f090070" />
    <public type="id" name="baseline" id="0x7f090071" />
    <public type="id" name="beginOnFirstDraw" id="0x7f090072" />
    <public type="id" name="beginning" id="0x7f090073" />
    <public type="id" name="bestChoice" id="0x7f090074" />
    <public type="id" name="blocking" id="0x7f090075" />
    <public type="id" name="bottom" id="0x7f090076" />
    <public type="id" name="bottom_progressbar" id="0x7f090077" />
    <public type="id" name="bounce" id="0x7f090078" />
    <public type="id" name="bounceBoth" id="0x7f090079" />
    <public type="id" name="bounceEnd" id="0x7f09007a" />
    <public type="id" name="bounceStart" id="0x7f09007b" />
    <public type="id" name="btnSmsVerify" id="0x7f09007c" />
    <public type="id" name="btnSubmit" id="0x7f09007d" />
    <public type="id" name="btnVerifyCode" id="0x7f09007e" />
    <public type="id" name="btn_cancel" id="0x7f09007f" />
    <public type="id" name="btn_ok" id="0x7f090080" />
    <public type="id" name="btn_shortcut" id="0x7f090081" />
    <public type="id" name="buffer_switch" id="0x7f090082" />
    <public type="id" name="buffering_container" id="0x7f090083" />
    <public type="id" name="buffering_imageview" id="0x7f090084" />
    <public type="id" name="buttonPanel" id="0x7f090085" />
    <public type="id" name="cacheSet" id="0x7f090086" />
    <public type="id" name="cacheUsed" id="0x7f090087" />
    <public type="id" name="cache_measures" id="0x7f090088" />
    <public type="id" name="callMeasure" id="0x7f090089" />
    <public type="id" name="cancelTv" id="0x7f09008a" />
    <public type="id" name="cancel_action" id="0x7f09008b" />
    <public type="id" name="cancel_button" id="0x7f09008c" />
    <public type="id" name="carryVelocity" id="0x7f09008d" />
    <public type="id" name="center" id="0x7f09008e" />
    <public type="id" name="center_container" id="0x7f09008f" />
    <public type="id" name="center_horizontal" id="0x7f090090" />
    <public type="id" name="center_vertical" id="0x7f090091" />
    <public type="id" name="chain" id="0x7f090092" />
    <public type="id" name="chain2" id="0x7f090093" />
    <public type="id" name="chains" id="0x7f090094" />
    <public type="id" name="checkRemind" id="0x7f090095" />
    <public type="id" name="checkVersion" id="0x7f090096" />
    <public type="id" name="checkbox" id="0x7f090097" />
    <public type="id" name="checked" id="0x7f090098" />
    <public type="id" name="chip" id="0x7f090099" />
    <public type="id" name="chip_group" id="0x7f09009a" />
    <public type="id" name="choiceItemA" id="0x7f09009b" />
    <public type="id" name="choiceItemB" id="0x7f09009c" />
    <public type="id" name="choiceItemC" id="0x7f09009d" />
    <public type="id" name="choiceItemD" id="0x7f09009e" />
    <public type="id" name="chronometer" id="0x7f09009f" />
    <public type="id" name="clearCache" id="0x7f0900a0" />
    <public type="id" name="clear_text" id="0x7f0900a1" />
    <public type="id" name="clip_horizontal" id="0x7f0900a2" />
    <public type="id" name="clip_vertical" id="0x7f0900a3" />
    <public type="id" name="clockwise" id="0x7f0900a4" />
    <public type="id" name="closest" id="0x7f0900a5" />
    <public type="id" name="collapseActionView" id="0x7f0900a6" />
    <public type="id" name="column" id="0x7f0900a7" />
    <public type="id" name="column_reverse" id="0x7f0900a8" />
    <public type="id" name="companyLine" id="0x7f0900a9" />
    <public type="id" name="confirm_button" id="0x7f0900aa" />
    <public type="id" name="constraint" id="0x7f0900ab" />
    <public type="id" name="contactEt" id="0x7f0900ac" />
    <public type="id" name="container" id="0x7f0900ad" />
    <public type="id" name="content" id="0x7f0900ae" />
    <public type="id" name="contentEt" id="0x7f0900af" />
    <public type="id" name="contentPanel" id="0x7f0900b0" />
    <public type="id" name="continueTv" id="0x7f0900b1" />
    <public type="id" name="continuousVelocity" id="0x7f0900b2" />
    <public type="id" name="coordinator" id="0x7f0900b3" />
    <public type="id" name="cos" id="0x7f0900b4" />
    <public type="id" name="courseImg" id="0x7f0900b5" />
    <public type="id" name="courseListPager" id="0x7f0900b6" />
    <public type="id" name="courseRecycler" id="0x7f0900b7" />
    <public type="id" name="courseRefresh" id="0x7f0900b8" />
    <public type="id" name="cover" id="0x7f0900b9" />
    <public type="id" name="coverDirectory" id="0x7f0900ba" />
    <public type="id" name="crash_error_activity_close_button" id="0x7f0900bb" />
    <public type="id" name="crash_error_activity_image" id="0x7f0900bc" />
    <public type="id" name="crash_error_activity_more_info_button" id="0x7f0900bd" />
    <public type="id" name="crash_error_activity_restart_button" id="0x7f0900be" />
    <public type="id" name="crash_error_locate_more_info_button" id="0x7f0900bf" />
    <public type="id" name="crash_error_message" id="0x7f0900c0" />
    <public type="id" name="current" id="0x7f0900c1" />
    <public type="id" name="currentState" id="0x7f0900c2" />
    <public type="id" name="currentTime_Tv" id="0x7f0900c3" />
    <public type="id" name="custom" id="0x7f0900c4" />
    <public type="id" name="customPanel" id="0x7f0900c5" />
    <public type="id" name="custom_full_id" id="0x7f0900c6" />
    <public type="id" name="custom_small_id" id="0x7f0900c7" />
    <public type="id" name="cut" id="0x7f0900c8" />
    <public type="id" name="date_picker_actions" id="0x7f0900c9" />
    <public type="id" name="decelerate" id="0x7f0900ca" />
    <public type="id" name="decelerateAndComplete" id="0x7f0900cb" />
    <public type="id" name="decor_content_parent" id="0x7f0900cc" />
    <public type="id" name="default_activity_button" id="0x7f0900cd" />
    <public type="id" name="deleteIv" id="0x7f0900ce" />
    <public type="id" name="deleteTv" id="0x7f0900cf" />
    <public type="id" name="deltaRelative" id="0x7f0900d0" />
    <public type="id" name="dependency_ordering" id="0x7f0900d1" />
    <public type="id" name="desc" id="0x7f0900d2" />
    <public type="id" name="descriptionLine" id="0x7f0900d3" />
    <public type="id" name="descriptionTv" id="0x7f0900d4" />
    <public type="id" name="design_bottom_sheet" id="0x7f0900d5" />
    <public type="id" name="design_menu_item_action_area" id="0x7f0900d6" />
    <public type="id" name="design_menu_item_action_area_stub" id="0x7f0900d7" />
    <public type="id" name="design_menu_item_text" id="0x7f0900d8" />
    <public type="id" name="design_navigation_view" id="0x7f0900d9" />
    <public type="id" name="detailTv" id="0x7f0900da" />
    <public type="id" name="detail_view" id="0x7f0900db" />
    <public type="id" name="dialog_button" id="0x7f0900dc" />
    <public type="id" name="dimensions" id="0x7f0900dd" />
    <public type="id" name="dirLayout" id="0x7f0900de" />
    <public type="id" name="direct" id="0x7f0900df" />
    <public type="id" name="directoryTv" id="0x7f0900e0" />
    <public type="id" name="disableHome" id="0x7f0900e1" />
    <public type="id" name="disableIntraAutoTransition" id="0x7f0900e2" />
    <public type="id" name="disablePostScroll" id="0x7f0900e3" />
    <public type="id" name="disableScroll" id="0x7f0900e4" />
    <public type="id" name="discribleTv" id="0x7f0900e5" />
    <public type="id" name="do_play_view" id="0x7f0900e6" />
    <public type="id" name="downloadLine" id="0x7f0900e7" />
    <public type="id" name="dragAnticlockwise" id="0x7f0900e8" />
    <public type="id" name="dragClockwise" id="0x7f0900e9" />
    <public type="id" name="dragDown" id="0x7f0900ea" />
    <public type="id" name="dragEnd" id="0x7f0900eb" />
    <public type="id" name="dragLeft" id="0x7f0900ec" />
    <public type="id" name="dragRight" id="0x7f0900ed" />
    <public type="id" name="dragStart" id="0x7f0900ee" />
    <public type="id" name="dragUp" id="0x7f0900ef" />
    <public type="id" name="dropdown_menu" id="0x7f0900f0" />
    <public type="id" name="durationTv" id="0x7f0900f1" />
    <public type="id" name="duration_image_tip" id="0x7f0900f2" />
    <public type="id" name="duration_progressbar" id="0x7f0900f3" />
    <public type="id" name="easeIn" id="0x7f0900f4" />
    <public type="id" name="easeInOut" id="0x7f0900f5" />
    <public type="id" name="easeOut" id="0x7f0900f6" />
    <public type="id" name="east" id="0x7f0900f7" />
    <public type="id" name="editAnswer" id="0x7f0900f8" />
    <public type="id" name="editTextPhone" id="0x7f0900f9" />
    <public type="id" name="editVerifyCode" id="0x7f0900fa" />
    <public type="id" name="edit_query" id="0x7f0900fb" />
    <public type="id" name="empty_View" id="0x7f0900fc" />
    <public type="id" name="empty_view" id="0x7f0900fd" />
    <public type="id" name="end" id="0x7f0900fe" />
    <public type="id" name="end_padder" id="0x7f0900ff" />
    <public type="id" name="enterAlways" id="0x7f090100" />
    <public type="id" name="enterAlwaysCollapsed" id="0x7f090101" />
    <public type="id" name="error_container" id="0x7f090102" />
    <public type="id" name="error_retry" id="0x7f090103" />
    <public type="id" name="error_tips" id="0x7f090104" />
    <public type="id" name="etName" id="0x7f090105" />
    <public type="id" name="exitUntilCollapsed" id="0x7f090106" />
    <public type="id" name="expand_activities_button" id="0x7f090107" />
    <public type="id" name="expanded_menu" id="0x7f090108" />
    <public type="id" name="fade" id="0x7f090109" />
    <public type="id" name="fileTypeIv" id="0x7f09010a" />
    <public type="id" name="filenameTv" id="0x7f09010b" />
    <public type="id" name="fill" id="0x7f09010c" />
    <public type="id" name="fill_horizontal" id="0x7f09010d" />
    <public type="id" name="fill_vertical" id="0x7f09010e" />
    <public type="id" name="filled" id="0x7f09010f" />
    <public type="id" name="filter_chip" id="0x7f090110" />
    <public type="id" name="fitToContents" id="0x7f090111" />
    <public type="id" name="fixed" id="0x7f090112" />
    <public type="id" name="flFiller" id="0x7f090113" />
    <public type="id" name="flLoopA" id="0x7f090114" />
    <public type="id" name="flLoopB" id="0x7f090115" />
    <public type="id" name="flexHistory" id="0x7f090116" />
    <public type="id" name="flex_end" id="0x7f090117" />
    <public type="id" name="flex_start" id="0x7f090118" />
    <public type="id" name="flip" id="0x7f090119" />
    <public type="id" name="forever" id="0x7f09011a" />
    <public type="id" name="fragment_container_view_tag" id="0x7f09011b" />
    <public type="id" name="frameLayout" id="0x7f09011c" />
    <public type="id" name="frameLayout2" id="0x7f09011d" />
    <public type="id" name="frameLayout3" id="0x7f09011e" />
    <public type="id" name="frameLeft" id="0x7f09011f" />
    <public type="id" name="frameRight" id="0x7f090120" />
    <public type="id" name="frost" id="0x7f090121" />
    <public type="id" name="fullscreen" id="0x7f090122" />
    <public type="id" name="gesture_control" id="0x7f090123" />
    <public type="id" name="ghost_view" id="0x7f090124" />
    <public type="id" name="ghost_view_holder" id="0x7f090125" />
    <public type="id" name="gkfsTv" id="0x7f090126" />
    <public type="id" name="glide_custom_view_target_tag" id="0x7f090127" />
    <public type="id" name="gone" id="0x7f090128" />
    <public type="id" name="graph" id="0x7f090129" />
    <public type="id" name="graph_wrap" id="0x7f09012a" />
    <public type="id" name="group_divider" id="0x7f09012b" />
    <public type="id" name="grouping" id="0x7f09012c" />
    <public type="id" name="groups" id="0x7f09012d" />
    <public type="id" name="headLayout" id="0x7f09012e" />
    <public type="id" name="help_ab_loop" id="0x7f09012f" />
    <public type="id" name="help_back" id="0x7f090130" />
    <public type="id" name="help_current" id="0x7f090131" />
    <public type="id" name="help_float_close" id="0x7f090132" />
    <public type="id" name="help_float_goback" id="0x7f090133" />
    <public type="id" name="help_float_lock" id="0x7f090134" />
    <public type="id" name="help_fullscreen" id="0x7f090135" />
    <public type="id" name="help_hide" id="0x7f090136" />
    <public type="id" name="help_next" id="0x7f090137" />
    <public type="id" name="help_progress" id="0x7f090138" />
    <public type="id" name="help_seekbar" id="0x7f090139" />
    <public type="id" name="help_start" id="0x7f09013a" />
    <public type="id" name="help_start2" id="0x7f09013b" />
    <public type="id" name="help_title" id="0x7f09013c" />
    <public type="id" name="help_total" id="0x7f09013d" />
    <public type="id" name="help_view" id="0x7f09013e" />
    <public type="id" name="hideable" id="0x7f09013f" />
    <public type="id" name="history_layout" id="0x7f090140" />
    <public type="id" name="history_line" id="0x7f090141" />
    <public type="id" name="home" id="0x7f090142" />
    <public type="id" name="homeAsUp" id="0x7f090143" />
    <public type="id" name="honorRequest" id="0x7f090144" />
    <public type="id" name="horizontal" id="0x7f090145" />
    <public type="id" name="horizontal_only" id="0x7f090146" />
    <public type="id" name="hud_view" id="0x7f090147" />
    <public type="id" name="ib_shortcut" id="0x7f090148" />
    <public type="id" name="icon" id="0x7f090149" />
    <public type="id" name="icon_frame" id="0x7f09014a" />
    <public type="id" name="icon_group" id="0x7f09014b" />
    <public type="id" name="ifRoom" id="0x7f09014c" />
    <public type="id" name="ignore" id="0x7f09014d" />
    <public type="id" name="ignoreRequest" id="0x7f09014e" />
    <public type="id" name="image" id="0x7f09014f" />
    <public type="id" name="image_banner" id="0x7f090150" />
    <public type="id" name="imgLoading" id="0x7f090151" />
    <public type="id" name="imgPauseStart" id="0x7f090152" />
    <public type="id" name="imgPlayLast" id="0x7f090153" />
    <public type="id" name="imgPlayNext" id="0x7f090154" />
    <public type="id" name="imgRotate" id="0x7f090155" />
    <public type="id" name="imgTips" id="0x7f090156" />
    <public type="id" name="img_empty" id="0x7f090157" />
    <public type="id" name="img_load" id="0x7f090158" />
    <public type="id" name="img_loading" id="0x7f090159" />
    <public type="id" name="immediateStop" id="0x7f09015a" />
    <public type="id" name="include" id="0x7f09015b" />
    <public type="id" name="included" id="0x7f09015c" />
    <public type="id" name="info" id="0x7f09015d" />
    <public type="id" name="info_collection_line" id="0x7f09015e" />
    <public type="id" name="invisible" id="0x7f09015f" />
    <public type="id" name="is_full_screen" id="0x7f090160" />
    <public type="id" name="italic" id="0x7f090161" />
    <public type="id" name="item_flag" id="0x7f090162" />
    <public type="id" name="item_img" id="0x7f090163" />
    <public type="id" name="item_sub_title" id="0x7f090164" />
    <public type="id" name="item_title" id="0x7f090165" />
    <public type="id" name="item_touch_helper_previous_elevation" id="0x7f090166" />
    <public type="id" name="iv" id="0x7f090167" />
    <public type="id" name="ivBack" id="0x7f090168" />
    <public type="id" name="ivClear" id="0x7f090169" />
    <public type="id" name="ivFloat" id="0x7f09016a" />
    <public type="id" name="ivSearch" id="0x7f09016b" />
    <public type="id" name="ivStClose" id="0x7f09016c" />
    <public type="id" name="iv_close" id="0x7f09016d" />
    <public type="id" name="iv_do_play" id="0x7f09016e" />
    <public type="id" name="iv_empty" id="0x7f09016f" />
    <public type="id" name="iv_icon" id="0x7f090170" />
    <public type="id" name="iv_more" id="0x7f090171" />
    <public type="id" name="iv_point" id="0x7f090172" />
    <public type="id" name="iv_shortcut" id="0x7f090173" />
    <public type="id" name="iv_subtitle" id="0x7f090174" />
    <public type="id" name="iv_title" id="0x7f090175" />
    <public type="id" name="jumpToEnd" id="0x7f090176" />
    <public type="id" name="jumpToStart" id="0x7f090177" />
    <public type="id" name="jump_ad" id="0x7f090178" />
    <public type="id" name="labeled" id="0x7f090179" />
    <public type="id" name="land_progress_line" id="0x7f09017a" />
    <public type="id" name="largeLabel" id="0x7f09017b" />
    <public type="id" name="layout" id="0x7f09017c" />
    <public type="id" name="layoutBody" id="0x7f09017d" />
    <public type="id" name="layoutEmptyLine" id="0x7f09017e" />
    <public type="id" name="layoutFloatController" id="0x7f09017f" />
    <public type="id" name="layoutFloatView" id="0x7f090180" />
    <public type="id" name="layoutHistory" id="0x7f090181" />
    <public type="id" name="layoutLanguage" id="0x7f090182" />
    <public type="id" name="layoutList" id="0x7f090183" />
    <public type="id" name="layoutLoop" id="0x7f090184" />
    <public type="id" name="layoutMultipleChoice" id="0x7f090185" />
    <public type="id" name="layoutRightArea" id="0x7f090186" />
    <public type="id" name="layoutScale" id="0x7f090187" />
    <public type="id" name="layoutSearch" id="0x7f090188" />
    <public type="id" name="layoutTitle" id="0x7f090189" />
    <public type="id" name="layout_bottom" id="0x7f09018a" />
    <public type="id" name="layout_top" id="0x7f09018b" />
    <public type="id" name="left" id="0x7f09018c" />
    <public type="id" name="left_img" id="0x7f09018d" />
    <public type="id" name="legacy" id="0x7f09018e" />
    <public type="id" name="line1" id="0x7f09018f" />
    <public type="id" name="line3" id="0x7f090190" />
    <public type="id" name="linear" id="0x7f090191" />
    <public type="id" name="list" id="0x7f090192" />
    <public type="id" name="listIv" id="0x7f090193" />
    <public type="id" name="listMode" id="0x7f090194" />
    <public type="id" name="list_item" id="0x7f090195" />
    <public type="id" name="list_line" id="0x7f090196" />
    <public type="id" name="llCacheSet" id="0x7f090197" />
    <public type="id" name="ll_bottom" id="0x7f090198" />
    <public type="id" name="ll_checkbox" id="0x7f090199" />
    <public type="id" name="ll_subtitle" id="0x7f09019a" />
    <public type="id" name="ll_view" id="0x7f09019b" />
    <public type="id" name="loading" id="0x7f09019c" />
    <public type="id" name="loading_container" id="0x7f09019d" />
    <public type="id" name="loading_imageview" id="0x7f09019e" />
    <public type="id" name="loading_line" id="0x7f09019f" />
    <public type="id" name="loading_music" id="0x7f0901a0" />
    <public type="id" name="loading_music_view" id="0x7f0901a1" />
    <public type="id" name="loading_speed" id="0x7f0901a2" />
    <public type="id" name="loading_view" id="0x7f0901a3" />
    <public type="id" name="lock_screen" id="0x7f0901a4" />
    <public type="id" name="loginBtn" id="0x7f0901a5" />
    <public type="id" name="login_layout" id="0x7f0901a6" />
    <public type="id" name="ly_title" id="0x7f0901a7" />
    <public type="id" name="masked" id="0x7f0901a8" />
    <public type="id" name="match_constraint" id="0x7f0901a9" />
    <public type="id" name="match_parent" id="0x7f0901aa" />
    <public type="id" name="media_actions" id="0x7f0901ab" />
    <public type="id" name="mediacontroller_progress" id="0x7f0901ac" />
    <public type="id" name="menuIV" id="0x7f0901ad" />
    <public type="id" name="menuTv" id="0x7f0901ae" />
    <public type="id" name="menu_view" id="0x7f0901af" />
    <public type="id" name="message" id="0x7f0901b0" />
    <public type="id" name="middle" id="0x7f0901b1" />
    <public type="id" name="mini" id="0x7f0901b2" />
    <public type="id" name="month_grid" id="0x7f0901b3" />
    <public type="id" name="month_navigation_bar" id="0x7f0901b4" />
    <public type="id" name="month_navigation_fragment_toggle" id="0x7f0901b5" />
    <public type="id" name="month_navigation_next" id="0x7f0901b6" />
    <public type="id" name="month_navigation_previous" id="0x7f0901b7" />
    <public type="id" name="month_title" id="0x7f0901b8" />
    <public type="id" name="motion_base" id="0x7f0901b9" />
    <public type="id" name="mtrl_calendar_day_selector_frame" id="0x7f0901ba" />
    <public type="id" name="mtrl_calendar_days_of_week" id="0x7f0901bb" />
    <public type="id" name="mtrl_calendar_frame" id="0x7f0901bc" />
    <public type="id" name="mtrl_calendar_main_pane" id="0x7f0901bd" />
    <public type="id" name="mtrl_calendar_months" id="0x7f0901be" />
    <public type="id" name="mtrl_calendar_selection_frame" id="0x7f0901bf" />
    <public type="id" name="mtrl_calendar_text_input_frame" id="0x7f0901c0" />
    <public type="id" name="mtrl_calendar_year_selector_frame" id="0x7f0901c1" />
    <public type="id" name="mtrl_card_checked_layer_id" id="0x7f0901c2" />
    <public type="id" name="mtrl_child_content_container" id="0x7f0901c3" />
    <public type="id" name="mtrl_internal_children_alpha_tag" id="0x7f0901c4" />
    <public type="id" name="mtrl_picker_fullscreen" id="0x7f0901c5" />
    <public type="id" name="mtrl_picker_header" id="0x7f0901c6" />
    <public type="id" name="mtrl_picker_header_selection_text" id="0x7f0901c7" />
    <public type="id" name="mtrl_picker_header_title_and_selection" id="0x7f0901c8" />
    <public type="id" name="mtrl_picker_header_toggle" id="0x7f0901c9" />
    <public type="id" name="mtrl_picker_text_input_date" id="0x7f0901ca" />
    <public type="id" name="mtrl_picker_text_input_range_end" id="0x7f0901cb" />
    <public type="id" name="mtrl_picker_text_input_range_start" id="0x7f0901cc" />
    <public type="id" name="mtrl_picker_title_text" id="0x7f0901cd" />
    <public type="id" name="multiply" id="0x7f0901ce" />
    <public type="id" name="name" id="0x7f0901cf" />
    <public type="id" name="nameTv" id="0x7f0901d0" />
    <public type="id" name="navigation_header_container" id="0x7f0901d1" />
    <public type="id" name="net_error" id="0x7f0901d2" />
    <public type="id" name="never" id="0x7f0901d3" />
    <public type="id" name="neverCompleteToEnd" id="0x7f0901d4" />
    <public type="id" name="neverCompleteToStart" id="0x7f0901d5" />
    <public type="id" name="noScroll" id="0x7f0901d6" />
    <public type="id" name="noState" id="0x7f0901d7" />
    <public type="id" name="none" id="0x7f0901d8" />
    <public type="id" name="normal" id="0x7f0901d9" />
    <public type="id" name="north" id="0x7f0901da" />
    <public type="id" name="notice_tips" id="0x7f0901db" />
    <public type="id" name="notification_background" id="0x7f0901dc" />
    <public type="id" name="notification_main_column" id="0x7f0901dd" />
    <public type="id" name="notification_main_column_container" id="0x7f0901de" />
    <public type="id" name="nowrap" id="0x7f0901df" />
    <public type="id" name="off" id="0x7f0901e0" />
    <public type="id" name="on" id="0x7f0901e1" />
    <public type="id" name="onInterceptTouchReturnSwipe" id="0x7f0901e2" />
    <public type="id" name="operation_bar" id="0x7f0901e3" />
    <public type="id" name="operation_bg" id="0x7f0901e4" />
    <public type="id" name="operation_tv" id="0x7f0901e5" />
    <public type="id" name="operation_view" id="0x7f0901e6" />
    <public type="id" name="outline" id="0x7f0901e7" />
    <public type="id" name="overshoot" id="0x7f0901e8" />
    <public type="id" name="packed" id="0x7f0901e9" />
    <public type="id" name="parallax" id="0x7f0901ea" />
    <public type="id" name="parent" id="0x7f0901eb" />
    <public type="id" name="parentPanel" id="0x7f0901ec" />
    <public type="id" name="parentRelative" id="0x7f0901ed" />
    <public type="id" name="parent_matrix" id="0x7f0901ee" />
    <public type="id" name="passwordEt" id="0x7f0901ef" />
    <public type="id" name="password_toggle" id="0x7f0901f0" />
    <public type="id" name="path" id="0x7f0901f1" />
    <public type="id" name="pathRelative" id="0x7f0901f2" />
    <public type="id" name="pause" id="0x7f0901f3" />
    <public type="id" name="peekHeight" id="0x7f0901f4" />
    <public type="id" name="percent" id="0x7f0901f5" />
    <public type="id" name="pin" id="0x7f0901f6" />
    <public type="id" name="player" id="0x7f0901f7" />
    <public type="id" name="playerBtn" id="0x7f0901f8" />
    <public type="id" name="player_frame" id="0x7f0901f9" />
    <public type="id" name="player_toolbar" id="0x7f0901fa" />
    <public type="id" name="policyTv" id="0x7f0901fb" />
    <public type="id" name="port_controller_area" id="0x7f0901fc" />
    <public type="id" name="port_controller_scrollView" id="0x7f0901fd" />
    <public type="id" name="portrait_progress_line" id="0x7f0901fe" />
    <public type="id" name="position" id="0x7f0901ff" />
    <public type="id" name="postLayout" id="0x7f090200" />
    <public type="id" name="privacy_policy_line" id="0x7f090201" />
    <public type="id" name="privacy_security" id="0x7f090202" />
    <public type="id" name="pro_percent" id="0x7f090203" />
    <public type="id" name="progress" id="0x7f090204" />
    <public type="id" name="progressBar" id="0x7f090205" />
    <public type="id" name="progressFloat" id="0x7f090206" />
    <public type="id" name="progress_circular" id="0x7f090207" />
    <public type="id" name="progress_horizontal" id="0x7f090208" />
    <public type="id" name="q_layout_bottom" id="0x7f090209" />
    <public type="id" name="qqLine" id="0x7f09020a" />
    <public type="id" name="qs_videoview" id="0x7f09020b" />
    <public type="id" name="radio" id="0x7f09020c" />
    <public type="id" name="radioButtonA" id="0x7f09020d" />
    <public type="id" name="radioButtonB" id="0x7f09020e" />
    <public type="id" name="radioButtonC" id="0x7f09020f" />
    <public type="id" name="radioButtonD" id="0x7f090210" />
    <public type="id" name="radioGroup" id="0x7f090211" />
    <public type="id" name="ratio" id="0x7f090212" />
    <public type="id" name="recover_screen" id="0x7f090213" />
    <public type="id" name="rectangles" id="0x7f090214" />
    <public type="id" name="recyclerView" id="0x7f090215" />
    <public type="id" name="recycler_view" id="0x7f090216" />
    <public type="id" name="reverseSawtooth" id="0x7f090217" />
    <public type="id" name="right" id="0x7f090218" />
    <public type="id" name="right_icon" id="0x7f090219" />
    <public type="id" name="right_side" id="0x7f09021a" />
    <public type="id" name="rootView" id="0x7f09021b" />
    <public type="id" name="rounded" id="0x7f09021c" />
    <public type="id" name="row" id="0x7f09021d" />
    <public type="id" name="row_reverse" id="0x7f09021e" />
    <public type="id" name="rvVideo" id="0x7f09021f" />
    <public type="id" name="rx_crash_tool" id="0x7f090220" />
    <public type="id" name="save_non_transition_alpha" id="0x7f090221" />
    <public type="id" name="save_overlay_view" id="0x7f090222" />
    <public type="id" name="sawtooth" id="0x7f090223" />
    <public type="id" name="scale" id="0x7f090224" />
    <public type="id" name="screen" id="0x7f090225" />
    <public type="id" name="scroll" id="0x7f090226" />
    <public type="id" name="scrollIndicatorDown" id="0x7f090227" />
    <public type="id" name="scrollIndicatorUp" id="0x7f090228" />
    <public type="id" name="scrollView" id="0x7f090229" />
    <public type="id" name="scrollView2" id="0x7f09022a" />
    <public type="id" name="scrollable" id="0x7f09022b" />
    <public type="id" name="sdk_line" id="0x7f09022c" />
    <public type="id" name="search_badge" id="0x7f09022d" />
    <public type="id" name="search_bar" id="0x7f09022e" />
    <public type="id" name="search_button" id="0x7f09022f" />
    <public type="id" name="search_close_btn" id="0x7f090230" />
    <public type="id" name="search_edit_frame" id="0x7f090231" />
    <public type="id" name="search_go_btn" id="0x7f090232" />
    <public type="id" name="search_mag_icon" id="0x7f090233" />
    <public type="id" name="search_plate" id="0x7f090234" />
    <public type="id" name="search_src_text" id="0x7f090235" />
    <public type="id" name="search_voice_btn" id="0x7f090236" />
    <public type="id" name="seekbar" id="0x7f090237" />
    <public type="id" name="seekbar2" id="0x7f090238" />
    <public type="id" name="seekbar_value" id="0x7f090239" />
    <public type="id" name="select_dialog_listview" id="0x7f09023a" />
    <public type="id" name="select_title_layout" id="0x7f09023b" />
    <public type="id" name="selected" id="0x7f09023c" />
    <public type="id" name="service_agreement_line" id="0x7f09023d" />
    <public type="id" name="share_button" id="0x7f09023e" />
    <public type="id" name="share_log" id="0x7f09023f" />
    <public type="id" name="sharedValueSet" id="0x7f090240" />
    <public type="id" name="sharedValueUnset" id="0x7f090241" />
    <public type="id" name="shortcut" id="0x7f090242" />
    <public type="id" name="showCustom" id="0x7f090243" />
    <public type="id" name="showHome" id="0x7f090244" />
    <public type="id" name="showTitle" id="0x7f090245" />
    <public type="id" name="show_clause_layout" id="0x7f090246" />
    <public type="id" name="sin" id="0x7f090247" />
    <public type="id" name="sizeTv" id="0x7f090248" />
    <public type="id" name="skipCollapsed" id="0x7f090249" />
    <public type="id" name="skipped" id="0x7f09024a" />
    <public type="id" name="slide" id="0x7f09024b" />
    <public type="id" name="slide_line" id="0x7f09024c" />
    <public type="id" name="smallLabel" id="0x7f09024d" />
    <public type="id" name="small_close" id="0x7f09024e" />
    <public type="id" name="sms_title" id="0x7f09024f" />
    <public type="id" name="snackbar_action" id="0x7f090250" />
    <public type="id" name="snackbar_text" id="0x7f090251" />
    <public type="id" name="snap" id="0x7f090252" />
    <public type="id" name="snapMargins" id="0x7f090253" />
    <public type="id" name="south" id="0x7f090254" />
    <public type="id" name="space_around" id="0x7f090255" />
    <public type="id" name="space_between" id="0x7f090256" />
    <public type="id" name="space_evenly" id="0x7f090257" />
    <public type="id" name="spacer" id="0x7f090258" />
    <public type="id" name="special_effects_controller_view_tag" id="0x7f090259" />
    <public type="id" name="speedModel" id="0x7f09025a" />
    <public type="id" name="speedSelect" id="0x7f09025b" />
    <public type="id" name="speedTouch" id="0x7f09025c" />
    <public type="id" name="speedTouchModel" id="0x7f09025d" />
    <public type="id" name="speedTv" id="0x7f09025e" />
    <public type="id" name="speed_list" id="0x7f09025f" />
    <public type="id" name="spinner" id="0x7f090260" />
    <public type="id" name="spline" id="0x7f090261" />
    <public type="id" name="split_action_bar" id="0x7f090262" />
    <public type="id" name="spread" id="0x7f090263" />
    <public type="id" name="spread_inside" id="0x7f090264" />
    <public type="id" name="spring" id="0x7f090265" />
    <public type="id" name="square" id="0x7f090266" />
    <public type="id" name="src_atop" id="0x7f090267" />
    <public type="id" name="src_in" id="0x7f090268" />
    <public type="id" name="src_over" id="0x7f090269" />
    <public type="id" name="standard" id="0x7f09026a" />
    <public type="id" name="start" id="0x7f09026b" />
    <public type="id" name="startHorizontal" id="0x7f09026c" />
    <public type="id" name="startVertical" id="0x7f09026d" />
    <public type="id" name="stateIv" id="0x7f09026e" />
    <public type="id" name="stateTv" id="0x7f09026f" />
    <public type="id" name="staticLayout" id="0x7f090270" />
    <public type="id" name="staticPostLayout" id="0x7f090271" />
    <public type="id" name="status_bar_latest_event_content" id="0x7f090272" />
    <public type="id" name="stop" id="0x7f090273" />
    <public type="id" name="stretch" id="0x7f090274" />
    <public type="id" name="submenuarrow" id="0x7f090275" />
    <public type="id" name="submit_area" id="0x7f090276" />
    <public type="id" name="subtitleDisplay" id="0x7f090277" />
    <public type="id" name="subtitle_display" id="0x7f090278" />
    <public type="id" name="subtitle_layout" id="0x7f090279" />
    <public type="id" name="subtitle_space" id="0x7f09027a" />
    <public type="id" name="subtitle_switch" id="0x7f09027b" />
    <public type="id" name="supportScrollUp" id="0x7f09027c" />
    <public type="id" name="surface_container" id="0x7f09027d" />
    <public type="id" name="swiperefreshlayout" id="0x7f09027e" />
    <public type="id" name="switchScale" id="0x7f09027f" />
    <public type="id" name="switchWidget" id="0x7f090280" />
    <public type="id" name="tabMode" id="0x7f090281" />
    <public type="id" name="table" id="0x7f090282" />
    <public type="id" name="tag_accessibility_actions" id="0x7f090283" />
    <public type="id" name="tag_accessibility_clickable_spans" id="0x7f090284" />
    <public type="id" name="tag_accessibility_heading" id="0x7f090285" />
    <public type="id" name="tag_accessibility_pane_title" id="0x7f090286" />
    <public type="id" name="tag_on_apply_window_listener" id="0x7f090287" />
    <public type="id" name="tag_on_receive_content_listener" id="0x7f090288" />
    <public type="id" name="tag_on_receive_content_mime_types" id="0x7f090289" />
    <public type="id" name="tag_screen_reader_focusable" id="0x7f09028a" />
    <public type="id" name="tag_state_description" id="0x7f09028b" />
    <public type="id" name="tag_transition_group" id="0x7f09028c" />
    <public type="id" name="tag_unhandled_key_event_manager" id="0x7f09028d" />
    <public type="id" name="tag_unhandled_key_listeners" id="0x7f09028e" />
    <public type="id" name="tag_window_insets_animation_callback" id="0x7f09028f" />
    <public type="id" name="techLine" id="0x7f090290" />
    <public type="id" name="telphoneLine" id="0x7f090291" />
    <public type="id" name="test_checkbox_android_button_tint" id="0x7f090292" />
    <public type="id" name="test_checkbox_app_button_tint" id="0x7f090293" />
    <public type="id" name="text" id="0x7f090294" />
    <public type="id" name="text2" id="0x7f090295" />
    <public type="id" name="textEnd" id="0x7f090296" />
    <public type="id" name="textPhoneError" id="0x7f090297" />
    <public type="id" name="textScale" id="0x7f090298" />
    <public type="id" name="textScaleDesc" id="0x7f090299" />
    <public type="id" name="textSpacerNoButtons" id="0x7f09029a" />
    <public type="id" name="textSpacerNoTitle" id="0x7f09029b" />
    <public type="id" name="textStart" id="0x7f09029c" />
    <public type="id" name="textVerifyError" id="0x7f09029d" />
    <public type="id" name="textView" id="0x7f09029e" />
    <public type="id" name="textView2" id="0x7f09029f" />
    <public type="id" name="textView3" id="0x7f0902a0" />
    <public type="id" name="text_input_end_icon" id="0x7f0902a1" />
    <public type="id" name="text_input_start_icon" id="0x7f0902a2" />
    <public type="id" name="text_mark" id="0x7f0902a3" />
    <public type="id" name="text_view" id="0x7f0902a4" />
    <public type="id" name="textinput_counter" id="0x7f0902a5" />
    <public type="id" name="textinput_error" id="0x7f0902a6" />
    <public type="id" name="textinput_helper_text" id="0x7f0902a7" />
    <public type="id" name="thumb" id="0x7f0902a8" />
    <public type="id" name="thumbImage" id="0x7f0902a9" />
    <public type="id" name="tileiV" id="0x7f0902aa" />
    <public type="id" name="time" id="0x7f0902ab" />
    <public type="id" name="time2" id="0x7f0902ac" />
    <public type="id" name="time_current" id="0x7f0902ad" />
    <public type="id" name="time_current2" id="0x7f0902ae" />
    <public type="id" name="title" id="0x7f0902af" />
    <public type="id" name="titleBar" id="0x7f0902b0" />
    <public type="id" name="titleDividerNoCustom" id="0x7f0902b1" />
    <public type="id" name="titleTv" id="0x7f0902b2" />
    <public type="id" name="title_name" id="0x7f0902b3" />
    <public type="id" name="title_template" id="0x7f0902b4" />
    <public type="id" name="toggle" id="0x7f0902b5" />
    <public type="id" name="toggleSwitch" id="0x7f0902b6" />
    <public type="id" name="toolbar" id="0x7f0902b7" />
    <public type="id" name="top" id="0x7f0902b8" />
    <public type="id" name="topPanel" id="0x7f0902b9" />
    <public type="id" name="total" id="0x7f0902ba" />
    <public type="id" name="totalTime_Tv" id="0x7f0902bb" />
    <public type="id" name="touchView" id="0x7f0902bc" />
    <public type="id" name="touch_outside" id="0x7f0902bd" />
    <public type="id" name="track_list_view" id="0x7f0902be" />
    <public type="id" name="transitionToEnd" id="0x7f0902bf" />
    <public type="id" name="transitionToStart" id="0x7f0902c0" />
    <public type="id" name="transition_current_scene" id="0x7f0902c1" />
    <public type="id" name="transition_layout_save" id="0x7f0902c2" />
    <public type="id" name="transition_position" id="0x7f0902c3" />
    <public type="id" name="transition_scene_layoutid_cache" id="0x7f0902c4" />
    <public type="id" name="transition_transform" id="0x7f0902c5" />
    <public type="id" name="triangle" id="0x7f0902c6" />
    <public type="id" name="tvBackPlay" id="0x7f0902c7" />
    <public type="id" name="tvBackPlayDesc" id="0x7f0902c8" />
    <public type="id" name="tvEndTime" id="0x7f0902c9" />
    <public type="id" name="tvFloat" id="0x7f0902ca" />
    <public type="id" name="tvHistoryClear" id="0x7f0902cb" />
    <public type="id" name="tvLanguage" id="0x7f0902cc" />
    <public type="id" name="tvLoopA" id="0x7f0902cd" />
    <public type="id" name="tvLoopB" id="0x7f0902ce" />
    <public type="id" name="tvQuestionTitle" id="0x7f0902cf" />
    <public type="id" name="tvRecoverScreen" id="0x7f0902d0" />
    <public type="id" name="tvSearch" id="0x7f0902d1" />
    <public type="id" name="tvStTime" id="0x7f0902d2" />
    <public type="id" name="tvTitle" id="0x7f0902d3" />
    <public type="id" name="tvUpdate" id="0x7f0902d4" />
    <public type="id" name="tv_agree" id="0x7f0902d5" />
    <public type="id" name="tv_content_desc" id="0x7f0902d6" />
    <public type="id" name="tv_current" id="0x7f0902d7" />
    <public type="id" name="tv_duration" id="0x7f0902d8" />
    <public type="id" name="tv_empty" id="0x7f0902d9" />
    <public type="id" name="tv_error" id="0x7f0902da" />
    <public type="id" name="tv_event_desc" id="0x7f0902db" />
    <public type="id" name="tv_exit" id="0x7f0902dc" />
    <public type="id" name="tv_filing_link" id="0x7f0902dd" />
    <public type="id" name="tv_filing_number" id="0x7f0902de" />
    <public type="id" name="tv_helplees" id="0x7f0902df" />
    <public type="id" name="tv_image_index" id="0x7f0902e0" />
    <public type="id" name="tv_loading" id="0x7f0902e1" />
    <public type="id" name="tv_percent" id="0x7f0902e2" />
    <public type="id" name="tv_refresh" id="0x7f0902e3" />
    <public type="id" name="tv_subtitle" id="0x7f0902e4" />
    <public type="id" name="tv_text_content" id="0x7f0902e5" />
    <public type="id" name="tv_text_desc" id="0x7f0902e6" />
    <public type="id" name="tv_tips" id="0x7f0902e7" />
    <public type="id" name="tv_title" id="0x7f0902e8" />
    <public type="id" name="txt_left_title" id="0x7f0902e9" />
    <public type="id" name="txt_main_title" id="0x7f0902ea" />
    <public type="id" name="txt_right_title" id="0x7f0902eb" />
    <public type="id" name="unchecked" id="0x7f0902ec" />
    <public type="id" name="uniform" id="0x7f0902ed" />
    <public type="id" name="unlabeled" id="0x7f0902ee" />
    <public type="id" name="up" id="0x7f0902ef" />
    <public type="id" name="useLogo" id="0x7f0902f0" />
    <public type="id" name="userEt" id="0x7f0902f1" />
    <public type="id" name="value" id="0x7f0902f2" />
    <public type="id" name="vertical" id="0x7f0902f3" />
    <public type="id" name="vertical_only" id="0x7f0902f4" />
    <public type="id" name="video_view" id="0x7f0902f5" />
    <public type="id" name="view_offset_helper" id="0x7f0902f6" />
    <public type="id" name="view_stub" id="0x7f0902f7" />
    <public type="id" name="view_transition" id="0x7f0902f8" />
    <public type="id" name="view_tree_lifecycle_owner" id="0x7f0902f9" />
    <public type="id" name="view_tree_on_back_pressed_dispatcher_owner" id="0x7f0902fa" />
    <public type="id" name="view_tree_saved_state_registry_owner" id="0x7f0902fb" />
    <public type="id" name="view_tree_view_model_store_owner" id="0x7f0902fc" />
    <public type="id" name="visible" id="0x7f0902fd" />
    <public type="id" name="visible_removing_fragment_view_tag" id="0x7f0902fe" />
    <public type="id" name="volume_progressbar" id="0x7f0902ff" />
    <public type="id" name="vp_images" id="0x7f090300" />
    <public type="id" name="websiteLine" id="0x7f090301" />
    <public type="id" name="websiteTv" id="0x7f090302" />
    <public type="id" name="webview" id="0x7f090303" />
    <public type="id" name="wechatLine" id="0x7f090304" />
    <public type="id" name="west" id="0x7f090305" />
    <public type="id" name="withText" id="0x7f090306" />
    <public type="id" name="wrap" id="0x7f090307" />
    <public type="id" name="wrap_content" id="0x7f090308" />
    <public type="id" name="wrap_content_constrained" id="0x7f090309" />
    <public type="id" name="wrap_reverse" id="0x7f09030a" />
    <public type="id" name="x_left" id="0x7f09030b" />
    <public type="id" name="x_right" id="0x7f09030c" />
    <public type="id" name="yjfkyjb" id="0x7f09030d" />
    <public type="integer" name="abc_config_activityDefaultDur" id="0x7f0a0000" />
    <public type="integer" name="abc_config_activityShortDur" id="0x7f0a0001" />
    <public type="integer" name="app_bar_elevation_anim_duration" id="0x7f0a0002" />
    <public type="integer" name="bottom_sheet_slide_duration" id="0x7f0a0003" />
    <public type="integer" name="cancel_button_image_alpha" id="0x7f0a0004" />
    <public type="integer" name="config_tooltipAnimTime" id="0x7f0a0005" />
    <public type="integer" name="design_snackbar_text_max_lines" id="0x7f0a0006" />
    <public type="integer" name="design_tab_indicator_anim_duration_ms" id="0x7f0a0007" />
    <public type="integer" name="hide_password_duration" id="0x7f0a0008" />
    <public type="integer" name="mtrl_badge_max_character_count" id="0x7f0a0009" />
    <public type="integer" name="mtrl_btn_anim_delay_ms" id="0x7f0a000a" />
    <public type="integer" name="mtrl_btn_anim_duration_ms" id="0x7f0a000b" />
    <public type="integer" name="mtrl_calendar_header_orientation" id="0x7f0a000c" />
    <public type="integer" name="mtrl_calendar_selection_text_lines" id="0x7f0a000d" />
    <public type="integer" name="mtrl_calendar_year_selector_span" id="0x7f0a000e" />
    <public type="integer" name="mtrl_card_anim_delay_ms" id="0x7f0a000f" />
    <public type="integer" name="mtrl_card_anim_duration_ms" id="0x7f0a0010" />
    <public type="integer" name="mtrl_chip_anim_duration" id="0x7f0a0011" />
    <public type="integer" name="mtrl_tab_indicator_anim_duration_ms" id="0x7f0a0012" />
    <public type="integer" name="show_password_duration" id="0x7f0a0013" />
    <public type="integer" name="status_bar_notification_info_maxnum" id="0x7f0a0014" />
    <public type="interpolator" name="btn_checkbox_checked_mtrl_animation_interpolator_0" id="0x7f0b0000" />
    <public type="interpolator" name="btn_checkbox_checked_mtrl_animation_interpolator_1" id="0x7f0b0001" />
    <public type="interpolator" name="btn_checkbox_unchecked_mtrl_animation_interpolator_0" id="0x7f0b0002" />
    <public type="interpolator" name="btn_checkbox_unchecked_mtrl_animation_interpolator_1" id="0x7f0b0003" />
    <public type="interpolator" name="btn_radio_to_off_mtrl_animation_interpolator_0" id="0x7f0b0004" />
    <public type="interpolator" name="btn_radio_to_on_mtrl_animation_interpolator_0" id="0x7f0b0005" />
    <public type="interpolator" name="fast_out_slow_in" id="0x7f0b0006" />
    <public type="interpolator" name="mtrl_fast_out_linear_in" id="0x7f0b0007" />
    <public type="interpolator" name="mtrl_fast_out_slow_in" id="0x7f0b0008" />
    <public type="interpolator" name="mtrl_linear" id="0x7f0b0009" />
    <public type="interpolator" name="mtrl_linear_out_slow_in" id="0x7f0b000a" />
    <public type="layout" name="abc_action_bar_title_item" id="0x7f0c0000" />
    <public type="layout" name="abc_action_bar_up_container" id="0x7f0c0001" />
    <public type="layout" name="abc_action_menu_item_layout" id="0x7f0c0002" />
    <public type="layout" name="abc_action_menu_layout" id="0x7f0c0003" />
    <public type="layout" name="abc_action_mode_bar" id="0x7f0c0004" />
    <public type="layout" name="abc_action_mode_close_item_material" id="0x7f0c0005" />
    <public type="layout" name="abc_activity_chooser_view" id="0x7f0c0006" />
    <public type="layout" name="abc_activity_chooser_view_list_item" id="0x7f0c0007" />
    <public type="layout" name="abc_alert_dialog_button_bar_material" id="0x7f0c0008" />
    <public type="layout" name="abc_alert_dialog_material" id="0x7f0c0009" />
    <public type="layout" name="abc_alert_dialog_title_material" id="0x7f0c000a" />
    <public type="layout" name="abc_cascading_menu_item_layout" id="0x7f0c000b" />
    <public type="layout" name="abc_dialog_title_material" id="0x7f0c000c" />
    <public type="layout" name="abc_expanded_menu_layout" id="0x7f0c000d" />
    <public type="layout" name="abc_list_menu_item_checkbox" id="0x7f0c000e" />
    <public type="layout" name="abc_list_menu_item_icon" id="0x7f0c000f" />
    <public type="layout" name="abc_list_menu_item_layout" id="0x7f0c0010" />
    <public type="layout" name="abc_list_menu_item_radio" id="0x7f0c0011" />
    <public type="layout" name="abc_popup_menu_header_item_layout" id="0x7f0c0012" />
    <public type="layout" name="abc_popup_menu_item_layout" id="0x7f0c0013" />
    <public type="layout" name="abc_screen_content_include" id="0x7f0c0014" />
    <public type="layout" name="abc_screen_simple" id="0x7f0c0015" />
    <public type="layout" name="abc_screen_simple_overlay_action_mode" id="0x7f0c0016" />
    <public type="layout" name="abc_screen_toolbar" id="0x7f0c0017" />
    <public type="layout" name="abc_search_dropdown_item_icons_2line" id="0x7f0c0018" />
    <public type="layout" name="abc_search_view" id="0x7f0c0019" />
    <public type="layout" name="abc_select_dialog_material" id="0x7f0c001a" />
    <public type="layout" name="abc_tooltip" id="0x7f0c001b" />
    <public type="layout" name="activity_about_us" id="0x7f0c001c" />
    <public type="layout" name="activity_course" id="0x7f0c001d" />
    <public type="layout" name="activity_course_detail" id="0x7f0c001e" />
    <public type="layout" name="activity_crash" id="0x7f0c001f" />
    <public type="layout" name="activity_login" id="0x7f0c0020" />
    <public type="layout" name="activity_main" id="0x7f0c0021" />
    <public type="layout" name="activity_privacy_and_security" id="0x7f0c0022" />
    <public type="layout" name="activity_question" id="0x7f0c0023" />
    <public type="layout" name="activity_setting" id="0x7f0c0024" />
    <public type="layout" name="activity_show_clause" id="0x7f0c0025" />
    <public type="layout" name="activity_sms_check" id="0x7f0c0026" />
    <public type="layout" name="activity_splash" id="0x7f0c0027" />
    <public type="layout" name="activity_statement" id="0x7f0c0028" />
    <public type="layout" name="activity_suggestion" id="0x7f0c0029" />
    <public type="layout" name="activity_web_view" id="0x7f0c002a" />
    <public type="layout" name="common_title_layout" id="0x7f0c002b" />
    <public type="layout" name="confirm_dialog" id="0x7f0c002c" />
    <public type="layout" name="course_img_list_item" id="0x7f0c002d" />
    <public type="layout" name="custom_dialog" id="0x7f0c002e" />
    <public type="layout" name="design_bottom_navigation_item" id="0x7f0c002f" />
    <public type="layout" name="design_bottom_sheet_dialog" id="0x7f0c0030" />
    <public type="layout" name="design_layout_snackbar" id="0x7f0c0031" />
    <public type="layout" name="design_layout_snackbar_include" id="0x7f0c0032" />
    <public type="layout" name="design_layout_tab_icon" id="0x7f0c0033" />
    <public type="layout" name="design_layout_tab_text" id="0x7f0c0034" />
    <public type="layout" name="design_menu_item_action_area" id="0x7f0c0035" />
    <public type="layout" name="design_navigation_item" id="0x7f0c0036" />
    <public type="layout" name="design_navigation_item_header" id="0x7f0c0037" />
    <public type="layout" name="design_navigation_item_separator" id="0x7f0c0038" />
    <public type="layout" name="design_navigation_item_subheader" id="0x7f0c0039" />
    <public type="layout" name="design_navigation_menu" id="0x7f0c003a" />
    <public type="layout" name="design_navigation_menu_item" id="0x7f0c003b" />
    <public type="layout" name="design_text_input_end_icon" id="0x7f0c003c" />
    <public type="layout" name="design_text_input_start_icon" id="0x7f0c003d" />
    <public type="layout" name="dialog_images_brower" id="0x7f0c003e" />
    <public type="layout" name="expand_button" id="0x7f0c003f" />
    <public type="layout" name="fragment_cache_dialog" id="0x7f0c0040" />
    <public type="layout" name="fragment_cache_dialog_head" id="0x7f0c0041" />
    <public type="layout" name="fragment_cache_dialog_item" id="0x7f0c0042" />
    <public type="layout" name="fragment_common_list_dialog" id="0x7f0c0043" />
    <public type="layout" name="fragment_common_list_dialog_item" id="0x7f0c0044" />
    <public type="layout" name="fragment_course" id="0x7f0c0045" />
    <public type="layout" name="fragment_detail" id="0x7f0c0046" />
    <public type="layout" name="fragment_dialog" id="0x7f0c0047" />
    <public type="layout" name="fragment_list" id="0x7f0c0048" />
    <public type="layout" name="fragment_model_choose" id="0x7f0c0049" />
    <public type="layout" name="fragment_model_choose_item" id="0x7f0c004a" />
    <public type="layout" name="fragment_speed_choose" id="0x7f0c004b" />
    <public type="layout" name="fragment_speed_choose_item" id="0x7f0c004c" />
    <public type="layout" name="fragment_track_list" id="0x7f0c004d" />
    <public type="layout" name="fragment_vertical_list" id="0x7f0c004e" />
    <public type="layout" name="fragment_video_more" id="0x7f0c004f" />
    <public type="layout" name="fragment_video_search" id="0x7f0c0050" />
    <public type="layout" name="image_frame" id="0x7f0c0051" />
    <public type="layout" name="kclb_select_title_layout" id="0x7f0c0052" />
    <public type="layout" name="kclb_to_login_layout" id="0x7f0c0053" />
    <public type="layout" name="layout_bigcate_node" id="0x7f0c0054" />
    <public type="layout" name="layout_chapter_node" id="0x7f0c0055" />
    <public type="layout" name="layout_course_detail_list_header" id="0x7f0c0056" />
    <public type="layout" name="layout_course_item" id="0x7f0c0057" />
    <public type="layout" name="layout_course_node" id="0x7f0c0058" />
    <public type="layout" name="layout_do_play" id="0x7f0c0059" />
    <public type="layout" name="layout_empty_view" id="0x7f0c005a" />
    <public type="layout" name="layout_file_node" id="0x7f0c005b" />
    <public type="layout" name="layout_gesture_cover" id="0x7f0c005c" />
    <public type="layout" name="layout_music_player" id="0x7f0c005d" />
    <public type="layout" name="layout_player_gb" id="0x7f0c005e" />
    <public type="layout" name="layout_video_info_item" id="0x7f0c005f" />
    <public type="layout" name="layout_web_view" id="0x7f0c0060" />
    <public type="layout" name="media_controller" id="0x7f0c0061" />
    <public type="layout" name="mtrl_alert_dialog" id="0x7f0c0062" />
    <public type="layout" name="mtrl_alert_dialog_actions" id="0x7f0c0063" />
    <public type="layout" name="mtrl_alert_dialog_title" id="0x7f0c0064" />
    <public type="layout" name="mtrl_alert_select_dialog_item" id="0x7f0c0065" />
    <public type="layout" name="mtrl_alert_select_dialog_multichoice" id="0x7f0c0066" />
    <public type="layout" name="mtrl_alert_select_dialog_singlechoice" id="0x7f0c0067" />
    <public type="layout" name="mtrl_calendar_day" id="0x7f0c0068" />
    <public type="layout" name="mtrl_calendar_day_of_week" id="0x7f0c0069" />
    <public type="layout" name="mtrl_calendar_days_of_week" id="0x7f0c006a" />
    <public type="layout" name="mtrl_calendar_horizontal" id="0x7f0c006b" />
    <public type="layout" name="mtrl_calendar_month" id="0x7f0c006c" />
    <public type="layout" name="mtrl_calendar_month_labeled" id="0x7f0c006d" />
    <public type="layout" name="mtrl_calendar_month_navigation" id="0x7f0c006e" />
    <public type="layout" name="mtrl_calendar_months" id="0x7f0c006f" />
    <public type="layout" name="mtrl_calendar_vertical" id="0x7f0c0070" />
    <public type="layout" name="mtrl_calendar_year" id="0x7f0c0071" />
    <public type="layout" name="mtrl_layout_snackbar" id="0x7f0c0072" />
    <public type="layout" name="mtrl_layout_snackbar_include" id="0x7f0c0073" />
    <public type="layout" name="mtrl_picker_actions" id="0x7f0c0074" />
    <public type="layout" name="mtrl_picker_dialog" id="0x7f0c0075" />
    <public type="layout" name="mtrl_picker_fullscreen" id="0x7f0c0076" />
    <public type="layout" name="mtrl_picker_header_dialog" id="0x7f0c0077" />
    <public type="layout" name="mtrl_picker_header_fullscreen" id="0x7f0c0078" />
    <public type="layout" name="mtrl_picker_header_selection_text" id="0x7f0c0079" />
    <public type="layout" name="mtrl_picker_header_title_text" id="0x7f0c007a" />
    <public type="layout" name="mtrl_picker_header_toggle" id="0x7f0c007b" />
    <public type="layout" name="mtrl_picker_text_input_date" id="0x7f0c007c" />
    <public type="layout" name="mtrl_picker_text_input_date_range" id="0x7f0c007d" />
    <public type="layout" name="notification_action" id="0x7f0c007e" />
    <public type="layout" name="notification_action_tombstone" id="0x7f0c007f" />
    <public type="layout" name="notification_media_action" id="0x7f0c0080" />
    <public type="layout" name="notification_media_cancel_action" id="0x7f0c0081" />
    <public type="layout" name="notification_template_big_media" id="0x7f0c0082" />
    <public type="layout" name="notification_template_big_media_custom" id="0x7f0c0083" />
    <public type="layout" name="notification_template_big_media_narrow" id="0x7f0c0084" />
    <public type="layout" name="notification_template_big_media_narrow_custom" id="0x7f0c0085" />
    <public type="layout" name="notification_template_custom_big" id="0x7f0c0086" />
    <public type="layout" name="notification_template_icon_group" id="0x7f0c0087" />
    <public type="layout" name="notification_template_lines_media" id="0x7f0c0088" />
    <public type="layout" name="notification_template_media" id="0x7f0c0089" />
    <public type="layout" name="notification_template_media_custom" id="0x7f0c008a" />
    <public type="layout" name="notification_template_part_chronometer" id="0x7f0c008b" />
    <public type="layout" name="notification_template_part_time" id="0x7f0c008c" />
    <public type="layout" name="player_history_layout" id="0x7f0c008d" />
    <public type="layout" name="player_operate_view" id="0x7f0c008e" />
    <public type="layout" name="player_tips_view" id="0x7f0c008f" />
    <public type="layout" name="playerview_content" id="0x7f0c0090" />
    <public type="layout" name="preference" id="0x7f0c0091" />
    <public type="layout" name="preference_category" id="0x7f0c0092" />
    <public type="layout" name="preference_category_material" id="0x7f0c0093" />
    <public type="layout" name="preference_dialog_edittext" id="0x7f0c0094" />
    <public type="layout" name="preference_dropdown" id="0x7f0c0095" />
    <public type="layout" name="preference_dropdown_material" id="0x7f0c0096" />
    <public type="layout" name="preference_information" id="0x7f0c0097" />
    <public type="layout" name="preference_information_material" id="0x7f0c0098" />
    <public type="layout" name="preference_list_fragment" id="0x7f0c0099" />
    <public type="layout" name="preference_material" id="0x7f0c009a" />
    <public type="layout" name="preference_recyclerview" id="0x7f0c009b" />
    <public type="layout" name="preference_widget_checkbox" id="0x7f0c009c" />
    <public type="layout" name="preference_widget_seekbar" id="0x7f0c009d" />
    <public type="layout" name="preference_widget_seekbar_material" id="0x7f0c009e" />
    <public type="layout" name="preference_widget_switch" id="0x7f0c009f" />
    <public type="layout" name="preference_widget_switch_compat" id="0x7f0c00a0" />
    <public type="layout" name="recycler_accinfo_item" id="0x7f0c00a1" />
    <public type="layout" name="select_dialog_item_material" id="0x7f0c00a2" />
    <public type="layout" name="select_dialog_multichoice_material" id="0x7f0c00a3" />
    <public type="layout" name="select_dialog_singlechoice_material" id="0x7f0c00a4" />
    <public type="layout" name="support_simple_spinner_dropdown_item" id="0x7f0c00a5" />
    <public type="layout" name="table_media_info" id="0x7f0c00a6" />
    <public type="layout" name="table_media_info_row1" id="0x7f0c00a7" />
    <public type="layout" name="table_media_info_row2" id="0x7f0c00a8" />
    <public type="layout" name="table_media_info_section" id="0x7f0c00a9" />
    <public type="layout" name="test_action_chip" id="0x7f0c00aa" />
    <public type="layout" name="test_design_checkbox" id="0x7f0c00ab" />
    <public type="layout" name="test_reflow_chipgroup" id="0x7f0c00ac" />
    <public type="layout" name="test_toolbar" id="0x7f0c00ad" />
    <public type="layout" name="test_toolbar_custom_background" id="0x7f0c00ae" />
    <public type="layout" name="test_toolbar_elevation" id="0x7f0c00af" />
    <public type="layout" name="test_toolbar_surface" id="0x7f0c00b0" />
    <public type="layout" name="text_view_with_line_height_from_appearance" id="0x7f0c00b1" />
    <public type="layout" name="text_view_with_line_height_from_layout" id="0x7f0c00b2" />
    <public type="layout" name="text_view_with_line_height_from_style" id="0x7f0c00b3" />
    <public type="layout" name="text_view_with_theme_line_height" id="0x7f0c00b4" />
    <public type="layout" name="text_view_without_line_height" id="0x7f0c00b5" />
    <public type="layout" name="video_brightness" id="0x7f0c00b6" />
    <public type="layout" name="video_layout_ad" id="0x7f0c00b7" />
    <public type="layout" name="video_layout_standard" id="0x7f0c00b8" />
    <public type="layout" name="video_progress_dialog" id="0x7f0c00b9" />
    <public type="layout" name="video_view" id="0x7f0c00ba" />
    <public type="layout" name="video_view_controller" id="0x7f0c00bb" />
    <public type="layout" name="video_view_title" id="0x7f0c00bc" />
    <public type="layout" name="video_volume_dialog" id="0x7f0c00bd" />
    <public type="layout" name="widget_toolbar" id="0x7f0c00be" />
    <public type="menu" name="main_menu" id="0x7f0d0000" />
    <public type="menu" name="suggest_menu" id="0x7f0d0001" />
    <public type="mipmap" name="account" id="0x7f0e0000" />
    <public type="mipmap" name="add" id="0x7f0e0001" />
    <public type="mipmap" name="add_white" id="0x7f0e0002" />
    <public type="mipmap" name="audio_img" id="0x7f0e0003" />
    <public type="mipmap" name="back" id="0x7f0e0004" />
    <public type="mipmap" name="back_white" id="0x7f0e0005" />
    <public type="mipmap" name="chapter" id="0x7f0e0006" />
    <public type="mipmap" name="close" id="0x7f0e0007" />
    <public type="mipmap" name="complete" id="0x7f0e0008" />
    <public type="mipmap" name="course" id="0x7f0e0009" />
    <public type="mipmap" name="default_img" id="0x7f0e000a" />
    <public type="mipmap" name="download" id="0x7f0e000b" />
    <public type="mipmap" name="empty_1" id="0x7f0e000c" />
    <public type="mipmap" name="ic_launcher" id="0x7f0e000d" />
    <public type="mipmap" name="ic_splash" id="0x7f0e000e" />
    <public type="mipmap" name="icon_close_1" id="0x7f0e000f" />
    <public type="mipmap" name="icon_float_close" id="0x7f0e0010" />
    <public type="mipmap" name="iv_search" id="0x7f0e0011" />
    <public type="mipmap" name="jc_error_normal" id="0x7f0e0012" />
    <public type="mipmap" name="jc_error_pressed" id="0x7f0e0013" />
    <public type="mipmap" name="list" id="0x7f0e0014" />
    <public type="mipmap" name="list_active" id="0x7f0e0015" />
    <public type="mipmap" name="lock" id="0x7f0e0016" />
    <public type="mipmap" name="main_folder" id="0x7f0e0017" />
    <public type="mipmap" name="part" id="0x7f0e0018" />
    <public type="mipmap" name="play_icon" id="0x7f0e0019" />
    <public type="mipmap" name="player_center_cover" id="0x7f0e001a" />
    <public type="mipmap" name="player_default" id="0x7f0e001b" />
    <public type="mipmap" name="qs_goback" id="0x7f0e001c" />
    <public type="mipmap" name="reload" id="0x7f0e001d" />
    <public type="mipmap" name="retract" id="0x7f0e001e" />
    <public type="mipmap" name="set" id="0x7f0e001f" />
    <public type="mipmap" name="stop" id="0x7f0e0020" />
    <public type="mipmap" name="sub_folder" id="0x7f0e0021" />
    <public type="mipmap" name="suspend" id="0x7f0e0022" />
    <public type="mipmap" name="tile" id="0x7f0e0023" />
    <public type="mipmap" name="tile_active" id="0x7f0e0024" />
    <public type="mipmap" name="unfold" id="0x7f0e0025" />
    <public type="mipmap" name="unlock" id="0x7f0e0026" />
    <public type="mipmap" name="user_icon" id="0x7f0e0027" />
    <public type="mipmap" name="user_icon_white" id="0x7f0e0028" />
    <public type="mipmap" name="user_password" id="0x7f0e0029" />
    <public type="mipmap" name="video_pause_normal" id="0x7f0e002a" />
    <public type="mipmap" name="video_play_normal" id="0x7f0e002b" />
    <public type="mipmap" name="video_small_close" id="0x7f0e002c" />
    <public type="plurals" name="mtrl_badge_content_description" id="0x7f0f0000" />
    <public type="raw" name="screen_anima" id="0x7f100000" />
    <public type="string" name="Info_protection_guidelines" id="0x7f110000" />
    <public type="string" name="N_A" id="0x7f110001" />
    <public type="string" name="TrackType_audio" id="0x7f110002" />
    <public type="string" name="TrackType_metadata" id="0x7f110003" />
    <public type="string" name="TrackType_subtitle" id="0x7f110004" />
    <public type="string" name="TrackType_timedtext" id="0x7f110005" />
    <public type="string" name="TrackType_unknown" id="0x7f110006" />
    <public type="string" name="TrackType_video" id="0x7f110007" />
    <public type="string" name="VideoView_ar_16_9_fit_parent" id="0x7f110008" />
    <public type="string" name="VideoView_ar_4_3_fit_parent" id="0x7f110009" />
    <public type="string" name="VideoView_ar_aspect_fill_parent" id="0x7f11000a" />
    <public type="string" name="VideoView_ar_aspect_fit_parent" id="0x7f11000b" />
    <public type="string" name="VideoView_ar_aspect_wrap_content" id="0x7f11000c" />
    <public type="string" name="VideoView_ar_match_parent" id="0x7f11000d" />
    <public type="string" name="VideoView_error_button" id="0x7f11000e" />
    <public type="string" name="VideoView_error_text_invalid_progressive_playback" id="0x7f11000f" />
    <public type="string" name="VideoView_error_text_unknown" id="0x7f110010" />
    <public type="string" name="VideoView_player_AndroidMediaPlayer" id="0x7f110011" />
    <public type="string" name="VideoView_player_IjkExoMediaPlayer" id="0x7f110012" />
    <public type="string" name="VideoView_player_IjkMediaPlayer" id="0x7f110013" />
    <public type="string" name="VideoView_player_none" id="0x7f110014" />
    <public type="string" name="VideoView_render_none" id="0x7f110015" />
    <public type="string" name="VideoView_render_surface_view" id="0x7f110016" />
    <public type="string" name="VideoView_render_texture_view" id="0x7f110017" />
    <public type="string" name="a_cache" id="0x7f110018" />
    <public type="string" name="ab_loop_close" id="0x7f110019" />
    <public type="string" name="abc_action_bar_home_description" id="0x7f11001a" />
    <public type="string" name="abc_action_bar_up_description" id="0x7f11001b" />
    <public type="string" name="abc_action_menu_overflow_description" id="0x7f11001c" />
    <public type="string" name="abc_action_mode_done" id="0x7f11001d" />
    <public type="string" name="abc_activity_chooser_view_see_all" id="0x7f11001e" />
    <public type="string" name="abc_activitychooserview_choose_application" id="0x7f11001f" />
    <public type="string" name="abc_capital_off" id="0x7f110020" />
    <public type="string" name="abc_capital_on" id="0x7f110021" />
    <public type="string" name="abc_menu_alt_shortcut_label" id="0x7f110022" />
    <public type="string" name="abc_menu_ctrl_shortcut_label" id="0x7f110023" />
    <public type="string" name="abc_menu_delete_shortcut_label" id="0x7f110024" />
    <public type="string" name="abc_menu_enter_shortcut_label" id="0x7f110025" />
    <public type="string" name="abc_menu_function_shortcut_label" id="0x7f110026" />
    <public type="string" name="abc_menu_meta_shortcut_label" id="0x7f110027" />
    <public type="string" name="abc_menu_shift_shortcut_label" id="0x7f110028" />
    <public type="string" name="abc_menu_space_shortcut_label" id="0x7f110029" />
    <public type="string" name="abc_menu_sym_shortcut_label" id="0x7f11002a" />
    <public type="string" name="abc_prepend_shortcut_label" id="0x7f11002b" />
    <public type="string" name="abc_search_hint" id="0x7f11002c" />
    <public type="string" name="abc_searchview_description_clear" id="0x7f11002d" />
    <public type="string" name="abc_searchview_description_query" id="0x7f11002e" />
    <public type="string" name="abc_searchview_description_search" id="0x7f11002f" />
    <public type="string" name="abc_searchview_description_submit" id="0x7f110030" />
    <public type="string" name="abc_searchview_description_voice" id="0x7f110031" />
    <public type="string" name="abc_shareactionprovider_share_with" id="0x7f110032" />
    <public type="string" name="abc_shareactionprovider_share_with_application" id="0x7f110033" />
    <public type="string" name="abc_toolbar_collapse_description" id="0x7f110034" />
    <public type="string" name="about_us" id="0x7f110035" />
    <public type="string" name="about_us_title_name" id="0x7f110036" />
    <public type="string" name="account_end_time" id="0x7f110037" />
    <public type="string" name="account_id" id="0x7f110038" />
    <public type="string" name="account_name" id="0x7f110039" />
    <public type="string" name="account_pass_incorrect" id="0x7f11003a" />
    <public type="string" name="action_sign_in_short" id="0x7f11003b" />
    <public type="string" name="add" id="0x7f11003c" />
    <public type="string" name="add_account" id="0x7f11003d" />
    <public type="string" name="add_auth_account" id="0x7f11003e" />
    <public type="string" name="address_web" id="0x7f11003f" />
    <public type="string" name="agreement_policy" id="0x7f110040" />
    <public type="string" name="agreement_with_policy" id="0x7f110041" />
    <public type="string" name="answer_failure" id="0x7f110042" />
    <public type="string" name="answer_time_limit" id="0x7f110043" />
    <public type="string" name="answer_wrong" id="0x7f110044" />
    <public type="string" name="app_error_tip" id="0x7f110045" />
    <public type="string" name="app_name" id="0x7f110046" />
    <public type="string" name="appbar_scrolling_view_behavior" id="0x7f110047" />
    <public type="string" name="auth_account" id="0x7f110048" />
    <public type="string" name="auth_error" id="0x7f110049" />
    <public type="string" name="auth_parsing_failed" id="0x7f11004a" />
    <public type="string" name="authentication_failure" id="0x7f11004b" />
    <public type="string" name="back_buffer" id="0x7f11004c" />
    <public type="string" name="banner_adapter_null_error" id="0x7f11004d" />
    <public type="string" name="baseUrl" id="0x7f11004e" />
    <public type="string" name="bit_rate" id="0x7f11004f" />
    <public type="string" name="bottom_sheet_behavior" id="0x7f110050" />
    <public type="string" name="breakpoint_continuation_error" id="0x7f110051" />
    <public type="string" name="cache_info_init" id="0x7f110052" />
    <public type="string" name="cache_set_desc" id="0x7f110053" />
    <public type="string" name="cache_set_error_out_size" id="0x7f110054" />
    <public type="string" name="cache_size" id="0x7f110055" />
    <public type="string" name="can_not_download" id="0x7f110056" />
    <public type="string" name="character_counter_content_description" id="0x7f110057" />
    <public type="string" name="character_counter_overflowed_content_description" id="0x7f110058" />
    <public type="string" name="character_counter_pattern" id="0x7f110059" />
    <public type="string" name="check_version" id="0x7f11005a" />
    <public type="string" name="chip_text" id="0x7f11005b" />
    <public type="string" name="clea_cache" id="0x7f11005c" />
    <public type="string" name="clear_cache_failed" id="0x7f11005d" />
    <public type="string" name="clear_cache_success" id="0x7f11005e" />
    <public type="string" name="clear_text_end_icon_content_description" id="0x7f11005f" />
    <public type="string" name="click_re_try" id="0x7f110060" />
    <public type="string" name="close" id="0x7f110061" />
    <public type="string" name="company" id="0x7f110062" />
    <public type="string" name="company_description" id="0x7f110063" />
    <public type="string" name="company_name" id="0x7f110064" />
    <public type="string" name="connect_server_failed" id="0x7f110065" />
    <public type="string" name="contact_us" id="0x7f110066" />
    <public type="string" name="copy" id="0x7f110067" />
    <public type="string" name="course_error_unknow" id="0x7f110068" />
    <public type="string" name="course_info_error" id="0x7f110069" />
    <public type="string" name="course_list_load_error" id="0x7f11006a" />
    <public type="string" name="crash_error_close_app" id="0x7f11006b" />
    <public type="string" name="crash_error_details" id="0x7f11006c" />
    <public type="string" name="crash_error_details_clipboard_label" id="0x7f11006d" />
    <public type="string" name="crash_error_details_close" id="0x7f11006e" />
    <public type="string" name="crash_error_details_copied" id="0x7f11006f" />
    <public type="string" name="crash_error_details_copy" id="0x7f110070" />
    <public type="string" name="crash_error_details_title" id="0x7f110071" />
    <public type="string" name="crash_error_file" id="0x7f110072" />
    <public type="string" name="crash_error_occurred_explanation" id="0x7f110073" />
    <public type="string" name="crash_error_restart_app" id="0x7f110074" />
    <public type="string" name="current_unrecognized_format" id="0x7f110075" />
    <public type="string" name="data_error" id="0x7f110076" />
    <public type="string" name="data_lost" id="0x7f110077" />
    <public type="string" name="data_parsing_failed" id="0x7f110078" />
    <public type="string" name="data_remove_success" id="0x7f110079" />
    <public type="string" name="del_failed" id="0x7f11007a" />
    <public type="string" name="del_success" id="0x7f11007b" />
    <public type="string" name="delete" id="0x7f11007c" />
    <public type="string" name="delete_failure" id="0x7f11007d" />
    <public type="string" name="delete_success" id="0x7f11007e" />
    <public type="string" name="deleting_files" id="0x7f11007f" />
    <public type="string" name="directory" id="0x7f110080" />
    <public type="string" name="directory_change" id="0x7f110081" />
    <public type="string" name="double_tap_close" id="0x7f110082" />
    <public type="string" name="download_error_auth_failed" id="0x7f110083" />
    <public type="string" name="download_error_failed" id="0x7f110084" />
    <public type="string" name="download_error_part" id="0x7f110085" />
    <public type="string" name="download_failed" id="0x7f110086" />
    <public type="string" name="download_first" id="0x7f110087" />
    <public type="string" name="download_info_error" id="0x7f110088" />
    <public type="string" name="download_or_online" id="0x7f110089" />
    <public type="string" name="download_service" id="0x7f11008a" />
    <public type="string" name="download_text" id="0x7f11008b" />
    <public type="string" name="ename" id="0x7f11008c" />
    <public type="string" name="end_time" id="0x7f11008d" />
    <public type="string" name="error_cache" id="0x7f11008e" />
    <public type="string" name="error_cache_net" id="0x7f11008f" />
    <public type="string" name="error_course_info" id="0x7f110090" />
    <public type="string" name="error_def" id="0x7f110091" />
    <public type="string" name="error_icon_content_description" id="0x7f110092" />
    <public type="string" name="error_memory_low" id="0x7f110093" />
    <public type="string" name="error_params" id="0x7f110094" />
    <public type="string" name="error_unknow" id="0x7f110095" />
    <public type="string" name="error_url_re" id="0x7f110096" />
    <public type="string" name="evs_back_buffer" id="0x7f110097" />
    <public type="string" name="exit" id="0x7f110098" />
    <public type="string" name="exit_current_account" id="0x7f110099" />
    <public type="string" name="exit_failure" id="0x7f11009a" />
    <public type="string" name="exo_download_completed" id="0x7f11009b" />
    <public type="string" name="exo_download_description" id="0x7f11009c" />
    <public type="string" name="exo_download_downloading" id="0x7f11009d" />
    <public type="string" name="exo_download_failed" id="0x7f11009e" />
    <public type="string" name="exo_download_notification_channel_name" id="0x7f11009f" />
    <public type="string" name="exo_download_paused" id="0x7f1100a0" />
    <public type="string" name="exo_download_paused_for_network" id="0x7f1100a1" />
    <public type="string" name="exo_download_paused_for_wifi" id="0x7f1100a2" />
    <public type="string" name="exo_download_removing" id="0x7f1100a3" />
    <public type="string" name="expand_button_title" id="0x7f1100a4" />
    <public type="string" name="exposed_dropdown_menu_content_description" id="0x7f1100a5" />
    <public type="string" name="external_storage_permissions" id="0x7f1100a6" />
    <public type="string" name="fab_transformation_scrim_behavior" id="0x7f1100a7" />
    <public type="string" name="fab_transformation_sheet_behavior" id="0x7f1100a8" />
    <public type="string" name="failed_connect_to_server" id="0x7f1100a9" />
    <public type="string" name="failed_to_write" id="0x7f1100aa" />
    <public type="string" name="fast_forward" id="0x7f1100ab" />
    <public type="string" name="feed_back" id="0x7f1100ac" />
    <public type="string" name="feedbackShows" id="0x7f1100ad" />
    <public type="string" name="feedback_desc_1" id="0x7f1100ae" />
    <public type="string" name="feedback_desc_2" id="0x7f1100af" />
    <public type="string" name="feedback_desc_3" id="0x7f1100b0" />
    <public type="string" name="file_downloading" id="0x7f1100b1" />
    <public type="string" name="file_error" id="0x7f1100b2" />
    <public type="string" name="file_exists" id="0x7f1100b3" />
    <public type="string" name="file_info_error" id="0x7f1100b4" />
    <public type="string" name="file_not_download" id="0x7f1100b5" />
    <public type="string" name="file_parsed_fialed" id="0x7f1100b6" />
    <public type="string" name="file_path_error" id="0x7f1100b7" />
    <public type="string" name="file_permiss_failed" id="0x7f1100b8" />
    <public type="string" name="filing_link" id="0x7f1100b9" />
    <public type="string" name="filing_number" id="0x7f1100ba" />
    <public type="string" name="fill_in_answer" id="0x7f1100bb" />
    <public type="string" name="format_cannot_parsed" id="0x7f1100bc" />
    <public type="string" name="fps" id="0x7f1100bd" />
    <public type="string" name="get_new_code" id="0x7f1100be" />
    <public type="string" name="get_verify_code" id="0x7f1100bf" />
    <public type="string" name="gkfs_text" id="0x7f1100c0" />
    <public type="string" name="has_new_version" id="0x7f1100c1" />
    <public type="string" name="helper_doc_url" id="0x7f1100c2" />
    <public type="string" name="hide_bottom_view_on_scroll_behavior" id="0x7f1100c3" />
    <public type="string" name="hide_control_view" id="0x7f1100c4" />
    <public type="string" name="http_data_explain_error" id="0x7f1100c5" />
    <public type="string" name="http_loading" id="0x7f1100c6" />
    <public type="string" name="http_network_error" id="0x7f1100c7" />
    <public type="string" name="http_request_cancel" id="0x7f1100c8" />
    <public type="string" name="http_response_error" id="0x7f1100c9" />
    <public type="string" name="http_response_md5_error" id="0x7f1100ca" />
    <public type="string" name="http_response_null_body" id="0x7f1100cb" />
    <public type="string" name="http_server_error" id="0x7f1100cc" />
    <public type="string" name="http_server_out_time" id="0x7f1100cd" />
    <public type="string" name="http_token_error" id="0x7f1100ce" />
    <public type="string" name="http_unknown_error" id="0x7f1100cf" />
    <public type="string" name="icon_content_description" id="0x7f1100d0" />
    <public type="string" name="ijkplayer_dummy" id="0x7f1100d1" />
    <public type="string" name="indicator_color_error" id="0x7f1100d2" />
    <public type="string" name="input_account" id="0x7f1100d3" />
    <public type="string" name="input_contact" id="0x7f1100d4" />
    <public type="string" name="input_email" id="0x7f1100d5" />
    <public type="string" name="input_phone_number" id="0x7f1100d6" />
    <public type="string" name="input_pwd" id="0x7f1100d7" />
    <public type="string" name="input_size_limited" id="0x7f1100d8" />
    <public type="string" name="input_verify_code" id="0x7f1100d9" />
    <public type="string" name="input_video_name" id="0x7f1100da" />
    <public type="string" name="invalid_code" id="0x7f1100db" />
    <public type="string" name="is_last_version" id="0x7f1100dc" />
    <public type="string" name="jump_ad" id="0x7f1100dd" />
    <public type="string" name="language" id="0x7f1100de" />
    <public type="string" name="last_video" id="0x7f1100df" />
    <public type="string" name="link_url" id="0x7f1100e0" />
    <public type="string" name="list_course" id="0x7f1100e1" />
    <public type="string" name="load_cost" id="0x7f1100e2" />
    <public type="string" name="load_error" id="0x7f1100e3" />
    <public type="string" name="load_file_error" id="0x7f1100e4" />
    <public type="string" name="loading" id="0x7f1100e5" />
    <public type="string" name="loading_1" id="0x7f1100e6" />
    <public type="string" name="loading_continue" id="0x7f1100e7" />
    <public type="string" name="loading_continue2" id="0x7f1100e8" />
    <public type="string" name="loading_count" id="0x7f1100e9" />
    <public type="string" name="loading_net_low" id="0x7f1100ea" />
    <public type="string" name="loading_text" id="0x7f1100eb" />
    <public type="string" name="logging_in" id="0x7f1100ec" />
    <public type="string" name="login" id="0x7f1100ed" />
    <public type="string" name="login_account" id="0x7f1100ee" />
    <public type="string" name="login_check" id="0x7f1100ef" />
    <public type="string" name="login_first" id="0x7f1100f0" />
    <public type="string" name="login_title_name" id="0x7f1100f1" />
    <public type="string" name="long_touch_speed" id="0x7f1100f2" />
    <public type="string" name="loop_time_too_short" id="0x7f1100f3" />
    <public type="string" name="low_cache_size" id="0x7f1100f4" />
    <public type="string" name="low_free_space" id="0x7f1100f5" />
    <public type="string" name="max_queue" id="0x7f1100f6" />
    <public type="string" name="media_information" id="0x7f1100f7" />
    <public type="string" name="message_authentication" id="0x7f1100f8" />
    <public type="string" name="mi__selected_audio_track" id="0x7f1100f9" />
    <public type="string" name="mi__selected_subtitle_track" id="0x7f1100fa" />
    <public type="string" name="mi__selected_video_track" id="0x7f1100fb" />
    <public type="string" name="mi_bit_rate" id="0x7f1100fc" />
    <public type="string" name="mi_channels" id="0x7f1100fd" />
    <public type="string" name="mi_codec" id="0x7f1100fe" />
    <public type="string" name="mi_frame_rate" id="0x7f1100ff" />
    <public type="string" name="mi_language" id="0x7f110100" />
    <public type="string" name="mi_length" id="0x7f110101" />
    <public type="string" name="mi_media" id="0x7f110102" />
    <public type="string" name="mi_pixel_format" id="0x7f110103" />
    <public type="string" name="mi_player" id="0x7f110104" />
    <public type="string" name="mi_profile_level" id="0x7f110105" />
    <public type="string" name="mi_resolution" id="0x7f110106" />
    <public type="string" name="mi_sample_rate" id="0x7f110107" />
    <public type="string" name="mi_stream_fmt1" id="0x7f110108" />
    <public type="string" name="mi_type" id="0x7f110109" />
    <public type="string" name="msg_out_of_limit" id="0x7f11010a" />
    <public type="string" name="mtrl_badge_numberless_content_description" id="0x7f11010b" />
    <public type="string" name="mtrl_chip_close_icon_content_description" id="0x7f11010c" />
    <public type="string" name="mtrl_exceed_max_badge_number_suffix" id="0x7f11010d" />
    <public type="string" name="mtrl_picker_a11y_next_month" id="0x7f11010e" />
    <public type="string" name="mtrl_picker_a11y_prev_month" id="0x7f11010f" />
    <public type="string" name="mtrl_picker_announce_current_selection" id="0x7f110110" />
    <public type="string" name="mtrl_picker_cancel" id="0x7f110111" />
    <public type="string" name="mtrl_picker_confirm" id="0x7f110112" />
    <public type="string" name="mtrl_picker_date_header_selected" id="0x7f110113" />
    <public type="string" name="mtrl_picker_date_header_title" id="0x7f110114" />
    <public type="string" name="mtrl_picker_date_header_unselected" id="0x7f110115" />
    <public type="string" name="mtrl_picker_day_of_week_column_header" id="0x7f110116" />
    <public type="string" name="mtrl_picker_invalid_format" id="0x7f110117" />
    <public type="string" name="mtrl_picker_invalid_format_example" id="0x7f110118" />
    <public type="string" name="mtrl_picker_invalid_format_use" id="0x7f110119" />
    <public type="string" name="mtrl_picker_invalid_range" id="0x7f11011a" />
    <public type="string" name="mtrl_picker_navigate_to_year_description" id="0x7f11011b" />
    <public type="string" name="mtrl_picker_out_of_range" id="0x7f11011c" />
    <public type="string" name="mtrl_picker_range_header_only_end_selected" id="0x7f11011d" />
    <public type="string" name="mtrl_picker_range_header_only_start_selected" id="0x7f11011e" />
    <public type="string" name="mtrl_picker_range_header_selected" id="0x7f11011f" />
    <public type="string" name="mtrl_picker_range_header_title" id="0x7f110120" />
    <public type="string" name="mtrl_picker_range_header_unselected" id="0x7f110121" />
    <public type="string" name="mtrl_picker_save" id="0x7f110122" />
    <public type="string" name="mtrl_picker_text_input_date_hint" id="0x7f110123" />
    <public type="string" name="mtrl_picker_text_input_date_range_end_hint" id="0x7f110124" />
    <public type="string" name="mtrl_picker_text_input_date_range_start_hint" id="0x7f110125" />
    <public type="string" name="mtrl_picker_text_input_day_abbr" id="0x7f110126" />
    <public type="string" name="mtrl_picker_text_input_month_abbr" id="0x7f110127" />
    <public type="string" name="mtrl_picker_text_input_year_abbr" id="0x7f110128" />
    <public type="string" name="mtrl_picker_toggle_to_calendar_input_mode" id="0x7f110129" />
    <public type="string" name="mtrl_picker_toggle_to_day_selection" id="0x7f11012a" />
    <public type="string" name="mtrl_picker_toggle_to_text_input_mode" id="0x7f11012b" />
    <public type="string" name="mtrl_picker_toggle_to_year_selection" id="0x7f11012c" />
    <public type="string" name="needMoreWords" id="0x7f11012d" />
    <public type="string" name="net_state_error" id="0x7f11012e" />
    <public type="string" name="network_anomalies" id="0x7f11012f" />
    <public type="string" name="network_anomaly" id="0x7f110130" />
    <public type="string" name="network_helpless" id="0x7f110131" />
    <public type="string" name="network_instability" id="0x7f110132" />
    <public type="string" name="network_lose" id="0x7f110133" />
    <public type="string" name="network_lose2" id="0x7f110134" />
    <public type="string" name="network_low" id="0x7f110135" />
    <public type="string" name="network_request_timeout" id="0x7f110136" />
    <public type="string" name="network_security_authentication_fails" id="0x7f110137" />
    <public type="string" name="no_agree" id="0x7f110138" />
    <public type="string" name="no_file_res" id="0x7f110139" />
    <public type="string" name="no_float_permission" id="0x7f11013a" />
    <public type="string" name="no_more_remind" id="0x7f11013b" />
    <public type="string" name="no_net" id="0x7f11013c" />
    <public type="string" name="no_search_result" id="0x7f11013d" />
    <public type="string" name="no_url" id="0x7f11013e" />
    <public type="string" name="non_course" id="0x7f11013f" />
    <public type="string" name="non_course_list" id="0x7f110140" />
    <public type="string" name="non_network" id="0x7f110141" />
    <public type="string" name="non_new_version_detected" id="0x7f110142" />
    <public type="string" name="non_playable_found" id="0x7f110143" />
    <public type="string" name="non_stroage_del_failed" id="0x7f110144" />
    <public type="string" name="non_stroage_save_failed" id="0x7f110145" />
    <public type="string" name="not_set" id="0x7f110146" />
    <public type="string" name="nothing" id="0x7f110147" />
    <public type="string" name="open_download_back" id="0x7f110148" />
    <public type="string" name="open_local_error" id="0x7f110149" />
    <public type="string" name="open_net_file_error" id="0x7f11014a" />
    <public type="string" name="param_error" id="0x7f11014b" />
    <public type="string" name="password_toggle_content_description" id="0x7f11014c" />
    <public type="string" name="path_password_eye" id="0x7f11014d" />
    <public type="string" name="path_password_eye_mask_strike_through" id="0x7f11014e" />
    <public type="string" name="path_password_eye_mask_visible" id="0x7f11014f" />
    <public type="string" name="path_password_strike_through" id="0x7f110150" />
    <public type="string" name="pause_text" id="0x7f110151" />
    <public type="string" name="permission_request" id="0x7f110152" />
    <public type="string" name="permission_required" id="0x7f110153" />
    <public type="string" name="personal_info_browse_and_download" id="0x7f110154" />
    <public type="string" name="personal_info_collection_list" id="0x7f110155" />
    <public type="string" name="personal_information_and_permissions" id="0x7f110156" />
    <public type="string" name="phone_number" id="0x7f110157" />
    <public type="string" name="phone_number_error" id="0x7f110158" />
    <public type="string" name="play_download" id="0x7f110159" />
    <public type="string" name="play_online" id="0x7f11015a" />
    <public type="string" name="play_url_eror" id="0x7f11015b" />
    <public type="string" name="play_url_err" id="0x7f11015c" />
    <public type="string" name="pref_key_enable_background_play" id="0x7f11015d" />
    <public type="string" name="pref_key_enable_detached_surface_texture" id="0x7f11015e" />
    <public type="string" name="pref_key_enable_no_view" id="0x7f11015f" />
    <public type="string" name="pref_key_enable_surface_view" id="0x7f110160" />
    <public type="string" name="pref_key_enable_texture_view" id="0x7f110161" />
    <public type="string" name="pref_key_last_directory" id="0x7f110162" />
    <public type="string" name="pref_key_media_codec_handle_resolution_change" id="0x7f110163" />
    <public type="string" name="pref_key_pixel_format" id="0x7f110164" />
    <public type="string" name="pref_key_player" id="0x7f110165" />
    <public type="string" name="pref_key_using_android_player" id="0x7f110166" />
    <public type="string" name="pref_key_using_media_codec" id="0x7f110167" />
    <public type="string" name="pref_key_using_media_codec_auto_rotate" id="0x7f110168" />
    <public type="string" name="pref_key_using_mediadatasource" id="0x7f110169" />
    <public type="string" name="pref_key_using_opensl_es" id="0x7f11016a" />
    <public type="string" name="pref_summary_enable_background_play" id="0x7f11016b" />
    <public type="string" name="pref_summary_enable_detached_surface_texture" id="0x7f11016c" />
    <public type="string" name="pref_summary_enable_no_view" id="0x7f11016d" />
    <public type="string" name="pref_summary_enable_surface_view" id="0x7f11016e" />
    <public type="string" name="pref_summary_enable_texture_view" id="0x7f11016f" />
    <public type="string" name="pref_summary_media_codec_handle_resolution_change" id="0x7f110170" />
    <public type="string" name="pref_summary_using_android_player" id="0x7f110171" />
    <public type="string" name="pref_summary_using_media_codec" id="0x7f110172" />
    <public type="string" name="pref_summary_using_media_codec_auto_rotate" id="0x7f110173" />
    <public type="string" name="pref_summary_using_mediadatasource" id="0x7f110174" />
    <public type="string" name="pref_summary_using_opensl_es" id="0x7f110175" />
    <public type="string" name="pref_title_enable_background_play" id="0x7f110176" />
    <public type="string" name="pref_title_enable_detached_surface_texture" id="0x7f110177" />
    <public type="string" name="pref_title_enable_no_view" id="0x7f110178" />
    <public type="string" name="pref_title_enable_surface_view" id="0x7f110179" />
    <public type="string" name="pref_title_enable_texture_view" id="0x7f11017a" />
    <public type="string" name="pref_title_general" id="0x7f11017b" />
    <public type="string" name="pref_title_ijkplayer_audio" id="0x7f11017c" />
    <public type="string" name="pref_title_ijkplayer_video" id="0x7f11017d" />
    <public type="string" name="pref_title_media_codec_handle_resolution_change" id="0x7f11017e" />
    <public type="string" name="pref_title_misc" id="0x7f11017f" />
    <public type="string" name="pref_title_pixel_format" id="0x7f110180" />
    <public type="string" name="pref_title_player" id="0x7f110181" />
    <public type="string" name="pref_title_render_view" id="0x7f110182" />
    <public type="string" name="pref_title_using_android_player" id="0x7f110183" />
    <public type="string" name="pref_title_using_media_codec" id="0x7f110184" />
    <public type="string" name="pref_title_using_media_codec_auto_rotate" id="0x7f110185" />
    <public type="string" name="pref_title_using_mediadatasource" id="0x7f110186" />
    <public type="string" name="pref_title_using_opensl_es" id="0x7f110187" />
    <public type="string" name="preference_copied" id="0x7f110188" />
    <public type="string" name="privacy_and_security" id="0x7f110189" />
    <public type="string" name="privacy_management" id="0x7f11018a" />
    <public type="string" name="qq" id="0x7f11018b" />
    <public type="string" name="qq_name" id="0x7f11018c" />
    <public type="string" name="question_and_answer" id="0x7f11018d" />
    <public type="string" name="read_agree_notice" id="0x7f11018e" />
    <public type="string" name="read_and_agree" id="0x7f11018f" />
    <public type="string" name="read_local_file_error" id="0x7f110190" />
    <public type="string" name="read_with_agree" id="0x7f110191" />
    <public type="string" name="reading_playback_address" id="0x7f110192" />
    <public type="string" name="recent" id="0x7f110193" />
    <public type="string" name="reduction_screen" id="0x7f110194" />
    <public type="string" name="refresh_pull" id="0x7f110195" />
    <public type="string" name="rename_failed" id="0x7f110196" />
    <public type="string" name="request_manual" id="0x7f110197" />
    <public type="string" name="restart_later" id="0x7f110198" />
    <public type="string" name="restart_now" id="0x7f110199" />
    <public type="string" name="restart_to_set_language" id="0x7f11019a" />
    <public type="string" name="rxcrashtool" id="0x7f11019b" />
    <public type="string" name="sample" id="0x7f11019c" />
    <public type="string" name="scale_gestrue" id="0x7f11019d" />
    <public type="string" name="scale_gestrue_desc" id="0x7f11019e" />
    <public type="string" name="screenshot" id="0x7f11019f" />
    <public type="string" name="sdk_share_list" id="0x7f1101a0" />
    <public type="string" name="search_menu_title" id="0x7f1101a1" />
    <public type="string" name="search_result_error" id="0x7f1101a2" />
    <public type="string" name="seek_cost" id="0x7f1101a3" />
    <public type="string" name="seek_load_cost" id="0x7f1101a4" />
    <public type="string" name="server_info_error" id="0x7f1101a5" />
    <public type="string" name="set_failed_retry" id="0x7f1101a6" />
    <public type="string" name="setting_title_name" id="0x7f1101a7" />
    <public type="string" name="settings" id="0x7f1101a8" />
    <public type="string" name="share_log" id="0x7f1101a9" />
    <public type="string" name="short_failed_null" id="0x7f1101aa" />
    <public type="string" name="short_save_failed" id="0x7f1101ab" />
    <public type="string" name="short_sucess" id="0x7f1101ac" />
    <public type="string" name="show_info" id="0x7f1101ad" />
    <public type="string" name="speed_normal" id="0x7f1101ae" />
    <public type="string" name="splash_content" id="0x7f1101af" />
    <public type="string" name="splash_content_2" id="0x7f1101b0" />
    <public type="string" name="splash_time" id="0x7f1101b1" />
    <public type="string" name="start_now" id="0x7f1101b2" />
    <public type="string" name="status_bar_notification_info_overflow" id="0x7f1101b3" />
    <public type="string" name="storage_permission" id="0x7f1101b4" />
    <public type="string" name="submmit" id="0x7f1101b5" />
    <public type="string" name="submmit_success" id="0x7f1101b6" />
    <public type="string" name="subtitle_off" id="0x7f1101b7" />
    <public type="string" name="subtitle_on" id="0x7f1101b8" />
    <public type="string" name="summary_collapsed_preference_list" id="0x7f1101b9" />
    <public type="string" name="switch_acount" id="0x7f1101ba" />
    <public type="string" name="system_permission_management" id="0x7f1101bb" />
    <public type="string" name="tcp_speed" id="0x7f1101bc" />
    <public type="string" name="tech" id="0x7f1101bd" />
    <public type="string" name="telphone" id="0x7f1101be" />
    <public type="string" name="temporarily_not_enabled" id="0x7f1101bf" />
    <public type="string" name="text_account_info_saved" id="0x7f1101c0" />
    <public type="string" name="text_agreement" id="0x7f1101c1" />
    <public type="string" name="text_agreement_2" id="0x7f1101c2" />
    <public type="string" name="text_author_title" id="0x7f1101c3" />
    <public type="string" name="text_back_play" id="0x7f1101c4" />
    <public type="string" name="text_cache_set" id="0x7f1101c5" />
    <public type="string" name="text_cancel" id="0x7f1101c6" />
    <public type="string" name="text_clear" id="0x7f1101c7" />
    <public type="string" name="text_empty" id="0x7f1101c8" />
    <public type="string" name="text_exit" id="0x7f1101c9" />
    <public type="string" name="text_float_permission" id="0x7f1101ca" />
    <public type="string" name="text_float_view" id="0x7f1101cb" />
    <public type="string" name="text_jump" id="0x7f1101cc" />
    <public type="string" name="text_know" id="0x7f1101cd" />
    <public type="string" name="text_later" id="0x7f1101ce" />
    <public type="string" name="text_loop_a" id="0x7f1101cf" />
    <public type="string" name="text_loop_b" id="0x7f1101d0" />
    <public type="string" name="text_ok" id="0x7f1101d1" />
    <public type="string" name="text_policy" id="0x7f1101d2" />
    <public type="string" name="text_policy_2" id="0x7f1101d3" />
    <public type="string" name="text_privacy_policy" id="0x7f1101d4" />
    <public type="string" name="text_record" id="0x7f1101d5" />
    <public type="string" name="text_repeat" id="0x7f1101d6" />
    <public type="string" name="text_rights" id="0x7f1101d7" />
    <public type="string" name="text_running" id="0x7f1101d8" />
    <public type="string" name="text_search" id="0x7f1101d9" />
    <public type="string" name="text_service_agreement" id="0x7f1101da" />
    <public type="string" name="text_speed" id="0x7f1101db" />
    <public type="string" name="text_speed_long" id="0x7f1101dc" />
    <public type="string" name="text_storage" id="0x7f1101dd" />
    <public type="string" name="text_storage_desc" id="0x7f1101de" />
    <public type="string" name="text_sure" id="0x7f1101df" />
    <public type="string" name="text_tips" id="0x7f1101e0" />
    <public type="string" name="text_tips_desc" id="0x7f1101e1" />
    <public type="string" name="text_update" id="0x7f1101e2" />
    <public type="string" name="three_times_per_minute" id="0x7f1101e3" />
    <public type="string" name="tips" id="0x7f1101e4" />
    <public type="string" name="tips_access_file_storage_permissions" id="0x7f1101e5" />
    <public type="string" name="tips_not_wifi" id="0x7f1101e6" />
    <public type="string" name="tips_not_wifi_cancel" id="0x7f1101e7" />
    <public type="string" name="tips_not_wifi_confirm" id="0x7f1101e8" />
    <public type="string" name="title_activity_test" id="0x7f1101e9" />
    <public type="string" name="to_setting" id="0x7f1101ea" />
    <public type="string" name="toggle_player" id="0x7f1101eb" />
    <public type="string" name="toggle_ratio" id="0x7f1101ec" />
    <public type="string" name="toggle_render" id="0x7f1101ed" />
    <public type="string" name="tracks" id="0x7f1101ee" />
    <public type="string" name="tv_agree" id="0x7f1101ef" />
    <public type="string" name="type" id="0x7f1101f0" />
    <public type="string" name="unrecognized_file_format" id="0x7f1101f1" />
    <public type="string" name="unsupported_format" id="0x7f1101f2" />
    <public type="string" name="update_immediately" id="0x7f1101f3" />
    <public type="string" name="url_personal_info_collection" id="0x7f1101f4" />
    <public type="string" name="url_sdk_share_list" id="0x7f1101f5" />
    <public type="string" name="url_user_agreement" id="0x7f1101f6" />
    <public type="string" name="url_user_notice" id="0x7f1101f7" />
    <public type="string" name="url_user_policy" id="0x7f1101f8" />
    <public type="string" name="user_notice" id="0x7f1101f9" />
    <public type="string" name="v7_preference_off" id="0x7f1101fa" />
    <public type="string" name="v7_preference_on" id="0x7f1101fb" />
    <public type="string" name="v_cache" id="0x7f1101fc" />
    <public type="string" name="vdec" id="0x7f1101fd" />
    <public type="string" name="verification_code_error" id="0x7f1101fe" />
    <public type="string" name="video_del_failed" id="0x7f1101ff" />
    <public type="string" name="video_del_success" id="0x7f110200" />
    <public type="string" name="video_not_exists" id="0x7f110201" />
    <public type="string" name="video_palye_error" id="0x7f110202" />
    <public type="string" name="videoview_null_msg" id="0x7f110203" />
    <public type="string" name="wait_text" id="0x7f110204" />
    <public type="string" name="website" id="0x7f110205" />
    <public type="string" name="wechat" id="0x7f110206" />
    <public type="string" name="wechat_name" id="0x7f110207" />
    <public type="string" name="wxts_text" id="0x7f110208" />
    <public type="string" name="yjfk_title_name" id="0x7f110209" />
    <public type="style" name="AlertDialog.AppCompat" id="0x7f120000" />
    <public type="style" name="AlertDialog.AppCompat.Light" id="0x7f120001" />
    <public type="style" name="Animation.AppCompat.Dialog" id="0x7f120002" />
    <public type="style" name="Animation.AppCompat.DropDownUp" id="0x7f120003" />
    <public type="style" name="Animation.AppCompat.Tooltip" id="0x7f120004" />
    <public type="style" name="Animation.Design.BottomSheetDialog" id="0x7f120005" />
    <public type="style" name="Animation.MaterialComponents.BottomSheetDialog" id="0x7f120006" />
    <public type="style" name="AppTheme" id="0x7f120007" />
    <public type="style" name="AppTheme.AppBarOverlay" id="0x7f120008" />
    <public type="style" name="AppTheme.PopActivity" id="0x7f120009" />
    <public type="style" name="AppTheme.PopupOverlay" id="0x7f12000a" />
    <public type="style" name="AppTheme.Splash" id="0x7f12000b" />
    <public type="style" name="BackImageStyle" id="0x7f12000c" />
    <public type="style" name="Base.AlertDialog.AppCompat" id="0x7f12000d" />
    <public type="style" name="Base.AlertDialog.AppCompat.Light" id="0x7f12000e" />
    <public type="style" name="Base.Animation.AppCompat.Dialog" id="0x7f12000f" />
    <public type="style" name="Base.Animation.AppCompat.DropDownUp" id="0x7f120010" />
    <public type="style" name="Base.Animation.AppCompat.Tooltip" id="0x7f120011" />
    <public type="style" name="Base.CardView" id="0x7f120012" />
    <public type="style" name="Base.DialogWindowTitle.AppCompat" id="0x7f120013" />
    <public type="style" name="Base.DialogWindowTitleBackground.AppCompat" id="0x7f120014" />
    <public type="style" name="Base.MaterialAlertDialog.MaterialComponents.Title.Icon" id="0x7f120015" />
    <public type="style" name="Base.MaterialAlertDialog.MaterialComponents.Title.Panel" id="0x7f120016" />
    <public type="style" name="Base.MaterialAlertDialog.MaterialComponents.Title.Text" id="0x7f120017" />
    <public type="style" name="Base.TextAppearance.AppCompat" id="0x7f120018" />
    <public type="style" name="Base.TextAppearance.AppCompat.Body1" id="0x7f120019" />
    <public type="style" name="Base.TextAppearance.AppCompat.Body2" id="0x7f12001a" />
    <public type="style" name="Base.TextAppearance.AppCompat.Button" id="0x7f12001b" />
    <public type="style" name="Base.TextAppearance.AppCompat.Caption" id="0x7f12001c" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display1" id="0x7f12001d" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display2" id="0x7f12001e" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display3" id="0x7f12001f" />
    <public type="style" name="Base.TextAppearance.AppCompat.Display4" id="0x7f120020" />
    <public type="style" name="Base.TextAppearance.AppCompat.Headline" id="0x7f120021" />
    <public type="style" name="Base.TextAppearance.AppCompat.Inverse" id="0x7f120022" />
    <public type="style" name="Base.TextAppearance.AppCompat.Large" id="0x7f120023" />
    <public type="style" name="Base.TextAppearance.AppCompat.Large.Inverse" id="0x7f120024" />
    <public type="style" name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" id="0x7f120025" />
    <public type="style" name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" id="0x7f120026" />
    <public type="style" name="Base.TextAppearance.AppCompat.Medium" id="0x7f120027" />
    <public type="style" name="Base.TextAppearance.AppCompat.Medium.Inverse" id="0x7f120028" />
    <public type="style" name="Base.TextAppearance.AppCompat.Menu" id="0x7f120029" />
    <public type="style" name="Base.TextAppearance.AppCompat.SearchResult" id="0x7f12002a" />
    <public type="style" name="Base.TextAppearance.AppCompat.SearchResult.Subtitle" id="0x7f12002b" />
    <public type="style" name="Base.TextAppearance.AppCompat.SearchResult.Title" id="0x7f12002c" />
    <public type="style" name="Base.TextAppearance.AppCompat.Small" id="0x7f12002d" />
    <public type="style" name="Base.TextAppearance.AppCompat.Small.Inverse" id="0x7f12002e" />
    <public type="style" name="Base.TextAppearance.AppCompat.Subhead" id="0x7f12002f" />
    <public type="style" name="Base.TextAppearance.AppCompat.Subhead.Inverse" id="0x7f120030" />
    <public type="style" name="Base.TextAppearance.AppCompat.Title" id="0x7f120031" />
    <public type="style" name="Base.TextAppearance.AppCompat.Title.Inverse" id="0x7f120032" />
    <public type="style" name="Base.TextAppearance.AppCompat.Tooltip" id="0x7f120033" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" id="0x7f120034" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" id="0x7f120035" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" id="0x7f120036" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" id="0x7f120037" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" id="0x7f120038" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" id="0x7f120039" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" id="0x7f12003a" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button" id="0x7f12003b" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored" id="0x7f12003c" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Colored" id="0x7f12003d" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Button.Inverse" id="0x7f12003e" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.DropDownItem" id="0x7f12003f" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header" id="0x7f120040" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" id="0x7f120041" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" id="0x7f120042" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.Switch" id="0x7f120043" />
    <public type="style" name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" id="0x7f120044" />
    <public type="style" name="Base.TextAppearance.MaterialComponents.Badge" id="0x7f120045" />
    <public type="style" name="Base.TextAppearance.MaterialComponents.Button" id="0x7f120046" />
    <public type="style" name="Base.TextAppearance.MaterialComponents.Headline6" id="0x7f120047" />
    <public type="style" name="Base.TextAppearance.MaterialComponents.Subtitle2" id="0x7f120048" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item" id="0x7f120049" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" id="0x7f12004a" />
    <public type="style" name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" id="0x7f12004b" />
    <public type="style" name="Base.Theme.AppCompat" id="0x7f12004c" />
    <public type="style" name="Base.Theme.AppCompat.CompactMenu" id="0x7f12004d" />
    <public type="style" name="Base.Theme.AppCompat.Dialog" id="0x7f12004e" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.Alert" id="0x7f12004f" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.FixedSize" id="0x7f120050" />
    <public type="style" name="Base.Theme.AppCompat.Dialog.MinWidth" id="0x7f120051" />
    <public type="style" name="Base.Theme.AppCompat.DialogWhenLarge" id="0x7f120052" />
    <public type="style" name="Base.Theme.AppCompat.Light" id="0x7f120053" />
    <public type="style" name="Base.Theme.AppCompat.Light.DarkActionBar" id="0x7f120054" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog" id="0x7f120055" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.Alert" id="0x7f120056" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.FixedSize" id="0x7f120057" />
    <public type="style" name="Base.Theme.AppCompat.Light.Dialog.MinWidth" id="0x7f120058" />
    <public type="style" name="Base.Theme.AppCompat.Light.DialogWhenLarge" id="0x7f120059" />
    <public type="style" name="Base.Theme.MaterialComponents" id="0x7f12005a" />
    <public type="style" name="Base.Theme.MaterialComponents.Bridge" id="0x7f12005b" />
    <public type="style" name="Base.Theme.MaterialComponents.CompactMenu" id="0x7f12005c" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog" id="0x7f12005d" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.Alert" id="0x7f12005e" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.Bridge" id="0x7f12005f" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.FixedSize" id="0x7f120060" />
    <public type="style" name="Base.Theme.MaterialComponents.Dialog.MinWidth" id="0x7f120061" />
    <public type="style" name="Base.Theme.MaterialComponents.DialogWhenLarge" id="0x7f120062" />
    <public type="style" name="Base.Theme.MaterialComponents.Light" id="0x7f120063" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Bridge" id="0x7f120064" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.DarkActionBar" id="0x7f120065" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge" id="0x7f120066" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog" id="0x7f120067" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.Alert" id="0x7f120068" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.Bridge" id="0x7f120069" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.FixedSize" id="0x7f12006a" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.Dialog.MinWidth" id="0x7f12006b" />
    <public type="style" name="Base.Theme.MaterialComponents.Light.DialogWhenLarge" id="0x7f12006c" />
    <public type="style" name="Base.ThemeOverlay.AppCompat" id="0x7f12006d" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.ActionBar" id="0x7f12006e" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dark" id="0x7f12006f" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dark.ActionBar" id="0x7f120070" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dialog" id="0x7f120071" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Dialog.Alert" id="0x7f120072" />
    <public type="style" name="Base.ThemeOverlay.AppCompat.Light" id="0x7f120073" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.Dialog" id="0x7f120074" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.Dialog.Alert" id="0x7f120075" />
    <public type="style" name="Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog" id="0x7f120076" />
    <public type="style" name="Base.V14.Theme.MaterialComponents" id="0x7f120077" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Bridge" id="0x7f120078" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Dialog" id="0x7f120079" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Dialog.Bridge" id="0x7f12007a" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light" id="0x7f12007b" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.Bridge" id="0x7f12007c" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge" id="0x7f12007d" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.Dialog" id="0x7f12007e" />
    <public type="style" name="Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge" id="0x7f12007f" />
    <public type="style" name="Base.V14.ThemeOverlay.MaterialComponents.Dialog" id="0x7f120080" />
    <public type="style" name="Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert" id="0x7f120081" />
    <public type="style" name="Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog" id="0x7f120082" />
    <public type="style" name="Base.V21.Theme.AppCompat" id="0x7f120083" />
    <public type="style" name="Base.V21.Theme.AppCompat.Dialog" id="0x7f120084" />
    <public type="style" name="Base.V21.Theme.AppCompat.Light" id="0x7f120085" />
    <public type="style" name="Base.V21.Theme.AppCompat.Light.Dialog" id="0x7f120086" />
    <public type="style" name="Base.V21.ThemeOverlay.AppCompat.Dialog" id="0x7f120087" />
    <public type="style" name="Base.V22.Theme.AppCompat" id="0x7f120088" />
    <public type="style" name="Base.V22.Theme.AppCompat.Light" id="0x7f120089" />
    <public type="style" name="Base.V23.Theme.AppCompat" id="0x7f12008a" />
    <public type="style" name="Base.V23.Theme.AppCompat.Light" id="0x7f12008b" />
    <public type="style" name="Base.V26.Theme.AppCompat" id="0x7f12008c" />
    <public type="style" name="Base.V26.Theme.AppCompat.Light" id="0x7f12008d" />
    <public type="style" name="Base.V26.Widget.AppCompat.Toolbar" id="0x7f12008e" />
    <public type="style" name="Base.V28.Theme.AppCompat" id="0x7f12008f" />
    <public type="style" name="Base.V28.Theme.AppCompat.Light" id="0x7f120090" />
    <public type="style" name="Base.V7.Theme.AppCompat" id="0x7f120091" />
    <public type="style" name="Base.V7.Theme.AppCompat.Dialog" id="0x7f120092" />
    <public type="style" name="Base.V7.Theme.AppCompat.Light" id="0x7f120093" />
    <public type="style" name="Base.V7.Theme.AppCompat.Light.Dialog" id="0x7f120094" />
    <public type="style" name="Base.V7.ThemeOverlay.AppCompat.Dialog" id="0x7f120095" />
    <public type="style" name="Base.V7.Widget.AppCompat.AutoCompleteTextView" id="0x7f120096" />
    <public type="style" name="Base.V7.Widget.AppCompat.EditText" id="0x7f120097" />
    <public type="style" name="Base.V7.Widget.AppCompat.Toolbar" id="0x7f120098" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar" id="0x7f120099" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.Solid" id="0x7f12009a" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabBar" id="0x7f12009b" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabText" id="0x7f12009c" />
    <public type="style" name="Base.Widget.AppCompat.ActionBar.TabView" id="0x7f12009d" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton" id="0x7f12009e" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton.CloseMode" id="0x7f12009f" />
    <public type="style" name="Base.Widget.AppCompat.ActionButton.Overflow" id="0x7f1200a0" />
    <public type="style" name="Base.Widget.AppCompat.ActionMode" id="0x7f1200a1" />
    <public type="style" name="Base.Widget.AppCompat.ActivityChooserView" id="0x7f1200a2" />
    <public type="style" name="Base.Widget.AppCompat.AutoCompleteTextView" id="0x7f1200a3" />
    <public type="style" name="Base.Widget.AppCompat.Button" id="0x7f1200a4" />
    <public type="style" name="Base.Widget.AppCompat.Button.Borderless" id="0x7f1200a5" />
    <public type="style" name="Base.Widget.AppCompat.Button.Borderless.Colored" id="0x7f1200a6" />
    <public type="style" name="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog" id="0x7f1200a7" />
    <public type="style" name="Base.Widget.AppCompat.Button.Colored" id="0x7f1200a8" />
    <public type="style" name="Base.Widget.AppCompat.Button.Small" id="0x7f1200a9" />
    <public type="style" name="Base.Widget.AppCompat.ButtonBar" id="0x7f1200aa" />
    <public type="style" name="Base.Widget.AppCompat.ButtonBar.AlertDialog" id="0x7f1200ab" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.CheckBox" id="0x7f1200ac" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.RadioButton" id="0x7f1200ad" />
    <public type="style" name="Base.Widget.AppCompat.CompoundButton.Switch" id="0x7f1200ae" />
    <public type="style" name="Base.Widget.AppCompat.DrawerArrowToggle" id="0x7f1200af" />
    <public type="style" name="Base.Widget.AppCompat.DrawerArrowToggle.Common" id="0x7f1200b0" />
    <public type="style" name="Base.Widget.AppCompat.DropDownItem.Spinner" id="0x7f1200b1" />
    <public type="style" name="Base.Widget.AppCompat.EditText" id="0x7f1200b2" />
    <public type="style" name="Base.Widget.AppCompat.ImageButton" id="0x7f1200b3" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar" id="0x7f1200b4" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.Solid" id="0x7f1200b5" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabBar" id="0x7f1200b6" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabText" id="0x7f1200b7" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" id="0x7f1200b8" />
    <public type="style" name="Base.Widget.AppCompat.Light.ActionBar.TabView" id="0x7f1200b9" />
    <public type="style" name="Base.Widget.AppCompat.Light.PopupMenu" id="0x7f1200ba" />
    <public type="style" name="Base.Widget.AppCompat.Light.PopupMenu.Overflow" id="0x7f1200bb" />
    <public type="style" name="Base.Widget.AppCompat.ListMenuView" id="0x7f1200bc" />
    <public type="style" name="Base.Widget.AppCompat.ListPopupWindow" id="0x7f1200bd" />
    <public type="style" name="Base.Widget.AppCompat.ListView" id="0x7f1200be" />
    <public type="style" name="Base.Widget.AppCompat.ListView.DropDown" id="0x7f1200bf" />
    <public type="style" name="Base.Widget.AppCompat.ListView.Menu" id="0x7f1200c0" />
    <public type="style" name="Base.Widget.AppCompat.PopupMenu" id="0x7f1200c1" />
    <public type="style" name="Base.Widget.AppCompat.PopupMenu.Overflow" id="0x7f1200c2" />
    <public type="style" name="Base.Widget.AppCompat.PopupWindow" id="0x7f1200c3" />
    <public type="style" name="Base.Widget.AppCompat.ProgressBar" id="0x7f1200c4" />
    <public type="style" name="Base.Widget.AppCompat.ProgressBar.Horizontal" id="0x7f1200c5" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar" id="0x7f1200c6" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar.Indicator" id="0x7f1200c7" />
    <public type="style" name="Base.Widget.AppCompat.RatingBar.Small" id="0x7f1200c8" />
    <public type="style" name="Base.Widget.AppCompat.SearchView" id="0x7f1200c9" />
    <public type="style" name="Base.Widget.AppCompat.SearchView.ActionBar" id="0x7f1200ca" />
    <public type="style" name="Base.Widget.AppCompat.SeekBar" id="0x7f1200cb" />
    <public type="style" name="Base.Widget.AppCompat.SeekBar.Discrete" id="0x7f1200cc" />
    <public type="style" name="Base.Widget.AppCompat.Spinner" id="0x7f1200cd" />
    <public type="style" name="Base.Widget.AppCompat.Spinner.Underlined" id="0x7f1200ce" />
    <public type="style" name="Base.Widget.AppCompat.TextView" id="0x7f1200cf" />
    <public type="style" name="Base.Widget.AppCompat.TextView.SpinnerItem" id="0x7f1200d0" />
    <public type="style" name="Base.Widget.AppCompat.Toolbar" id="0x7f1200d1" />
    <public type="style" name="Base.Widget.AppCompat.Toolbar.Button.Navigation" id="0x7f1200d2" />
    <public type="style" name="Base.Widget.Design.TabLayout" id="0x7f1200d3" />
    <public type="style" name="Base.Widget.MaterialComponents.AutoCompleteTextView" id="0x7f1200d4" />
    <public type="style" name="Base.Widget.MaterialComponents.CheckedTextView" id="0x7f1200d5" />
    <public type="style" name="Base.Widget.MaterialComponents.Chip" id="0x7f1200d6" />
    <public type="style" name="Base.Widget.MaterialComponents.PopupMenu" id="0x7f1200d7" />
    <public type="style" name="Base.Widget.MaterialComponents.PopupMenu.ContextMenu" id="0x7f1200d8" />
    <public type="style" name="Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow" id="0x7f1200d9" />
    <public type="style" name="Base.Widget.MaterialComponents.PopupMenu.Overflow" id="0x7f1200da" />
    <public type="style" name="Base.Widget.MaterialComponents.TextInputEditText" id="0x7f1200db" />
    <public type="style" name="Base.Widget.MaterialComponents.TextInputLayout" id="0x7f1200dc" />
    <public type="style" name="Base.Widget.MaterialComponents.TextView" id="0x7f1200dd" />
    <public type="style" name="Base_Theme_AppCompat_Empty" id="0x7f1200de" />
    <public type="style" name="BottomSheetDialog" id="0x7f1200df" />
    <public type="style" name="BottomSheetDialogWrapper" id="0x7f1200e0" />
    <public type="style" name="CardView" id="0x7f1200e1" />
    <public type="style" name="CardView.Dark" id="0x7f1200e2" />
    <public type="style" name="CardView.Light" id="0x7f1200e3" />
    <public type="style" name="EmptyTheme" id="0x7f1200e4" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents" id="0x7f1200e5" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Body.Text" id="0x7f1200e6" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar" id="0x7f1200e7" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner" id="0x7f1200e8" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Icon" id="0x7f1200e9" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked" id="0x7f1200ea" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Panel" id="0x7f1200eb" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked" id="0x7f1200ec" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Text" id="0x7f1200ed" />
    <public type="style" name="MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked" id="0x7f1200ee" />
    <public type="style" name="MediaButtonCompat" id="0x7f1200ef" />
    <public type="style" name="MediaButtonCompat.Ffwd" id="0x7f1200f0" />
    <public type="style" name="MediaButtonCompat.Next" id="0x7f1200f1" />
    <public type="style" name="MediaButtonCompat.Pause" id="0x7f1200f2" />
    <public type="style" name="MediaButtonCompat.Play" id="0x7f1200f3" />
    <public type="style" name="MediaButtonCompat.Previous" id="0x7f1200f4" />
    <public type="style" name="MediaButtonCompat.Rew" id="0x7f1200f5" />
    <public type="style" name="MyEditText" id="0x7f1200f6" />
    <public type="style" name="NormalBottomSheetDialog" id="0x7f1200f7" />
    <public type="style" name="Platform.AppCompat" id="0x7f1200f8" />
    <public type="style" name="Platform.AppCompat.Light" id="0x7f1200f9" />
    <public type="style" name="Platform.MaterialComponents" id="0x7f1200fa" />
    <public type="style" name="Platform.MaterialComponents.Dialog" id="0x7f1200fb" />
    <public type="style" name="Platform.MaterialComponents.Light" id="0x7f1200fc" />
    <public type="style" name="Platform.MaterialComponents.Light.Dialog" id="0x7f1200fd" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat" id="0x7f1200fe" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat.Dark" id="0x7f1200ff" />
    <public type="style" name="Platform.ThemeOverlay.AppCompat.Light" id="0x7f120100" />
    <public type="style" name="Platform.V21.AppCompat" id="0x7f120101" />
    <public type="style" name="Platform.V21.AppCompat.Light" id="0x7f120102" />
    <public type="style" name="Platform.V25.AppCompat" id="0x7f120103" />
    <public type="style" name="Platform.V25.AppCompat.Light" id="0x7f120104" />
    <public type="style" name="Platform.Widget.AppCompat.Spinner" id="0x7f120105" />
    <public type="style" name="Preference" id="0x7f120106" />
    <public type="style" name="Preference.Category" id="0x7f120107" />
    <public type="style" name="Preference.Category.Material" id="0x7f120108" />
    <public type="style" name="Preference.CheckBoxPreference" id="0x7f120109" />
    <public type="style" name="Preference.CheckBoxPreference.Material" id="0x7f12010a" />
    <public type="style" name="Preference.DialogPreference" id="0x7f12010b" />
    <public type="style" name="Preference.DialogPreference.EditTextPreference" id="0x7f12010c" />
    <public type="style" name="Preference.DialogPreference.EditTextPreference.Material" id="0x7f12010d" />
    <public type="style" name="Preference.DialogPreference.Material" id="0x7f12010e" />
    <public type="style" name="Preference.DropDown" id="0x7f12010f" />
    <public type="style" name="Preference.DropDown.Material" id="0x7f120110" />
    <public type="style" name="Preference.Information" id="0x7f120111" />
    <public type="style" name="Preference.Information.Material" id="0x7f120112" />
    <public type="style" name="Preference.Material" id="0x7f120113" />
    <public type="style" name="Preference.PreferenceScreen" id="0x7f120114" />
    <public type="style" name="Preference.PreferenceScreen.Material" id="0x7f120115" />
    <public type="style" name="Preference.SeekBarPreference" id="0x7f120116" />
    <public type="style" name="Preference.SeekBarPreference.Material" id="0x7f120117" />
    <public type="style" name="Preference.SwitchPreference" id="0x7f120118" />
    <public type="style" name="Preference.SwitchPreference.Material" id="0x7f120119" />
    <public type="style" name="Preference.SwitchPreferenceCompat" id="0x7f12011a" />
    <public type="style" name="Preference.SwitchPreferenceCompat.Material" id="0x7f12011b" />
    <public type="style" name="PreferenceCategoryTitleTextStyle" id="0x7f12011c" />
    <public type="style" name="PreferenceFragment" id="0x7f12011d" />
    <public type="style" name="PreferenceFragment.Material" id="0x7f12011e" />
    <public type="style" name="PreferenceFragmentList" id="0x7f12011f" />
    <public type="style" name="PreferenceFragmentList.Material" id="0x7f120120" />
    <public type="style" name="PreferenceSummaryTextStyle" id="0x7f120121" />
    <public type="style" name="PreferenceThemeOverlay" id="0x7f120122" />
    <public type="style" name="PreferenceThemeOverlay.v14" id="0x7f120123" />
    <public type="style" name="PreferenceThemeOverlay.v14.Material" id="0x7f120124" />
    <public type="style" name="ProgressDialog" id="0x7f120125" />
    <public type="style" name="RtlOverlay.DialogWindowTitle.AppCompat" id="0x7f120126" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem" id="0x7f120127" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.DialogTitle.Icon" id="0x7f120128" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem" id="0x7f120129" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup" id="0x7f12012a" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut" id="0x7f12012b" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow" id="0x7f12012c" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" id="0x7f12012d" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Title" id="0x7f12012e" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown" id="0x7f12012f" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" id="0x7f120130" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" id="0x7f120131" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query" id="0x7f120132" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text" id="0x7f120133" />
    <public type="style" name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon" id="0x7f120134" />
    <public type="style" name="RtlUnderlay.Widget.AppCompat.ActionButton" id="0x7f120135" />
    <public type="style" name="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow" id="0x7f120136" />
    <public type="style" name="ShapeAppearance.MaterialComponents" id="0x7f120137" />
    <public type="style" name="ShapeAppearance.MaterialComponents.LargeComponent" id="0x7f120138" />
    <public type="style" name="ShapeAppearance.MaterialComponents.MediumComponent" id="0x7f120139" />
    <public type="style" name="ShapeAppearance.MaterialComponents.SmallComponent" id="0x7f12013a" />
    <public type="style" name="ShapeAppearance.MaterialComponents.Test" id="0x7f12013b" />
    <public type="style" name="ShapeAppearanceOverlay" id="0x7f12013c" />
    <public type="style" name="ShapeAppearanceOverlay.BottomLeftDifferentCornerSize" id="0x7f12013d" />
    <public type="style" name="ShapeAppearanceOverlay.BottomRightCut" id="0x7f12013e" />
    <public type="style" name="ShapeAppearanceOverlay.Cut" id="0x7f12013f" />
    <public type="style" name="ShapeAppearanceOverlay.DifferentCornerSize" id="0x7f120140" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.BottomSheet" id="0x7f120141" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.Chip" id="0x7f120142" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton" id="0x7f120143" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton" id="0x7f120144" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day" id="0x7f120145" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen" id="0x7f120146" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year" id="0x7f120147" />
    <public type="style" name="ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox" id="0x7f120148" />
    <public type="style" name="ShapeAppearanceOverlay.TopLeftCut" id="0x7f120149" />
    <public type="style" name="ShapeAppearanceOverlay.TopRightDifferentCornerSize" id="0x7f12014a" />
    <public type="style" name="Test.ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day" id="0x7f12014b" />
    <public type="style" name="Test.Theme.MaterialComponents.MaterialCalendar" id="0x7f12014c" />
    <public type="style" name="Test.Widget.MaterialComponents.MaterialCalendar" id="0x7f12014d" />
    <public type="style" name="Test.Widget.MaterialComponents.MaterialCalendar.Day" id="0x7f12014e" />
    <public type="style" name="Test.Widget.MaterialComponents.MaterialCalendar.Day.Selected" id="0x7f12014f" />
    <public type="style" name="TestStyleWithLineHeight" id="0x7f120150" />
    <public type="style" name="TestStyleWithLineHeightAppearance" id="0x7f120151" />
    <public type="style" name="TestStyleWithThemeLineHeightAttribute" id="0x7f120152" />
    <public type="style" name="TestStyleWithoutLineHeight" id="0x7f120153" />
    <public type="style" name="TestThemeWithLineHeight" id="0x7f120154" />
    <public type="style" name="TestThemeWithLineHeightDisabled" id="0x7f120155" />
    <public type="style" name="TextAppearance.AppCompat" id="0x7f120156" />
    <public type="style" name="TextAppearance.AppCompat.Body1" id="0x7f120157" />
    <public type="style" name="TextAppearance.AppCompat.Body2" id="0x7f120158" />
    <public type="style" name="TextAppearance.AppCompat.Button" id="0x7f120159" />
    <public type="style" name="TextAppearance.AppCompat.Caption" id="0x7f12015a" />
    <public type="style" name="TextAppearance.AppCompat.Display1" id="0x7f12015b" />
    <public type="style" name="TextAppearance.AppCompat.Display2" id="0x7f12015c" />
    <public type="style" name="TextAppearance.AppCompat.Display3" id="0x7f12015d" />
    <public type="style" name="TextAppearance.AppCompat.Display4" id="0x7f12015e" />
    <public type="style" name="TextAppearance.AppCompat.Headline" id="0x7f12015f" />
    <public type="style" name="TextAppearance.AppCompat.Inverse" id="0x7f120160" />
    <public type="style" name="TextAppearance.AppCompat.Large" id="0x7f120161" />
    <public type="style" name="TextAppearance.AppCompat.Large.Inverse" id="0x7f120162" />
    <public type="style" name="TextAppearance.AppCompat.Light.SearchResult.Subtitle" id="0x7f120163" />
    <public type="style" name="TextAppearance.AppCompat.Light.SearchResult.Title" id="0x7f120164" />
    <public type="style" name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" id="0x7f120165" />
    <public type="style" name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" id="0x7f120166" />
    <public type="style" name="TextAppearance.AppCompat.Medium" id="0x7f120167" />
    <public type="style" name="TextAppearance.AppCompat.Medium.Inverse" id="0x7f120168" />
    <public type="style" name="TextAppearance.AppCompat.Menu" id="0x7f120169" />
    <public type="style" name="TextAppearance.AppCompat.SearchResult.Subtitle" id="0x7f12016a" />
    <public type="style" name="TextAppearance.AppCompat.SearchResult.Title" id="0x7f12016b" />
    <public type="style" name="TextAppearance.AppCompat.Small" id="0x7f12016c" />
    <public type="style" name="TextAppearance.AppCompat.Small.Inverse" id="0x7f12016d" />
    <public type="style" name="TextAppearance.AppCompat.Subhead" id="0x7f12016e" />
    <public type="style" name="TextAppearance.AppCompat.Subhead.Inverse" id="0x7f12016f" />
    <public type="style" name="TextAppearance.AppCompat.Title" id="0x7f120170" />
    <public type="style" name="TextAppearance.AppCompat.Title.Inverse" id="0x7f120171" />
    <public type="style" name="TextAppearance.AppCompat.Tooltip" id="0x7f120172" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Menu" id="0x7f120173" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle" id="0x7f120174" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" id="0x7f120175" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Title" id="0x7f120176" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" id="0x7f120177" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle" id="0x7f120178" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse" id="0x7f120179" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Title" id="0x7f12017a" />
    <public type="style" name="TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse" id="0x7f12017b" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button" id="0x7f12017c" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Borderless.Colored" id="0x7f12017d" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Colored" id="0x7f12017e" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Button.Inverse" id="0x7f12017f" />
    <public type="style" name="TextAppearance.AppCompat.Widget.DropDownItem" id="0x7f120180" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Header" id="0x7f120181" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Large" id="0x7f120182" />
    <public type="style" name="TextAppearance.AppCompat.Widget.PopupMenu.Small" id="0x7f120183" />
    <public type="style" name="TextAppearance.AppCompat.Widget.Switch" id="0x7f120184" />
    <public type="style" name="TextAppearance.AppCompat.Widget.TextView.SpinnerItem" id="0x7f120185" />
    <public type="style" name="TextAppearance.Compat.Notification" id="0x7f120186" />
    <public type="style" name="TextAppearance.Compat.Notification.Info" id="0x7f120187" />
    <public type="style" name="TextAppearance.Compat.Notification.Info.Media" id="0x7f120188" />
    <public type="style" name="TextAppearance.Compat.Notification.Line2" id="0x7f120189" />
    <public type="style" name="TextAppearance.Compat.Notification.Line2.Media" id="0x7f12018a" />
    <public type="style" name="TextAppearance.Compat.Notification.Media" id="0x7f12018b" />
    <public type="style" name="TextAppearance.Compat.Notification.Time" id="0x7f12018c" />
    <public type="style" name="TextAppearance.Compat.Notification.Time.Media" id="0x7f12018d" />
    <public type="style" name="TextAppearance.Compat.Notification.Title" id="0x7f12018e" />
    <public type="style" name="TextAppearance.Compat.Notification.Title.Media" id="0x7f12018f" />
    <public type="style" name="TextAppearance.Design.CollapsingToolbar.Expanded" id="0x7f120190" />
    <public type="style" name="TextAppearance.Design.Counter" id="0x7f120191" />
    <public type="style" name="TextAppearance.Design.Counter.Overflow" id="0x7f120192" />
    <public type="style" name="TextAppearance.Design.Error" id="0x7f120193" />
    <public type="style" name="TextAppearance.Design.HelperText" id="0x7f120194" />
    <public type="style" name="TextAppearance.Design.Hint" id="0x7f120195" />
    <public type="style" name="TextAppearance.Design.Snackbar.Message" id="0x7f120196" />
    <public type="style" name="TextAppearance.Design.Tab" id="0x7f120197" />
    <public type="style" name="TextAppearance.MaterialComponents.Badge" id="0x7f120198" />
    <public type="style" name="TextAppearance.MaterialComponents.Body1" id="0x7f120199" />
    <public type="style" name="TextAppearance.MaterialComponents.Body2" id="0x7f12019a" />
    <public type="style" name="TextAppearance.MaterialComponents.Button" id="0x7f12019b" />
    <public type="style" name="TextAppearance.MaterialComponents.Caption" id="0x7f12019c" />
    <public type="style" name="TextAppearance.MaterialComponents.Chip" id="0x7f12019d" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline1" id="0x7f12019e" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline2" id="0x7f12019f" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline3" id="0x7f1201a0" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline4" id="0x7f1201a1" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline5" id="0x7f1201a2" />
    <public type="style" name="TextAppearance.MaterialComponents.Headline6" id="0x7f1201a3" />
    <public type="style" name="TextAppearance.MaterialComponents.Overline" id="0x7f1201a4" />
    <public type="style" name="TextAppearance.MaterialComponents.Subtitle1" id="0x7f1201a5" />
    <public type="style" name="TextAppearance.MaterialComponents.Subtitle2" id="0x7f1201a6" />
    <public type="style" name="TextAppearance.Widget.AppCompat.ExpandedMenu.Item" id="0x7f1201a7" />
    <public type="style" name="TextAppearance.Widget.AppCompat.Toolbar.Subtitle" id="0x7f1201a8" />
    <public type="style" name="TextAppearance.Widget.AppCompat.Toolbar.Title" id="0x7f1201a9" />
    <public type="style" name="Theme.AppCompat" id="0x7f1201aa" />
    <public type="style" name="Theme.AppCompat.CompactMenu" id="0x7f1201ab" />
    <public type="style" name="Theme.AppCompat.DayNight" id="0x7f1201ac" />
    <public type="style" name="Theme.AppCompat.DayNight.DarkActionBar" id="0x7f1201ad" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog" id="0x7f1201ae" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog.Alert" id="0x7f1201af" />
    <public type="style" name="Theme.AppCompat.DayNight.Dialog.MinWidth" id="0x7f1201b0" />
    <public type="style" name="Theme.AppCompat.DayNight.DialogWhenLarge" id="0x7f1201b1" />
    <public type="style" name="Theme.AppCompat.DayNight.NoActionBar" id="0x7f1201b2" />
    <public type="style" name="Theme.AppCompat.Dialog" id="0x7f1201b3" />
    <public type="style" name="Theme.AppCompat.Dialog.Alert" id="0x7f1201b4" />
    <public type="style" name="Theme.AppCompat.Dialog.MinWidth" id="0x7f1201b5" />
    <public type="style" name="Theme.AppCompat.DialogWhenLarge" id="0x7f1201b6" />
    <public type="style" name="Theme.AppCompat.Empty" id="0x7f1201b7" />
    <public type="style" name="Theme.AppCompat.Light" id="0x7f1201b8" />
    <public type="style" name="Theme.AppCompat.Light.DarkActionBar" id="0x7f1201b9" />
    <public type="style" name="Theme.AppCompat.Light.Dialog" id="0x7f1201ba" />
    <public type="style" name="Theme.AppCompat.Light.Dialog.Alert" id="0x7f1201bb" />
    <public type="style" name="Theme.AppCompat.Light.Dialog.MinWidth" id="0x7f1201bc" />
    <public type="style" name="Theme.AppCompat.Light.DialogWhenLarge" id="0x7f1201bd" />
    <public type="style" name="Theme.AppCompat.Light.NoActionBar" id="0x7f1201be" />
    <public type="style" name="Theme.AppCompat.NoActionBar" id="0x7f1201bf" />
    <public type="style" name="Theme.Design" id="0x7f1201c0" />
    <public type="style" name="Theme.Design.BottomSheetDialog" id="0x7f1201c1" />
    <public type="style" name="Theme.Design.Light" id="0x7f1201c2" />
    <public type="style" name="Theme.Design.Light.BottomSheetDialog" id="0x7f1201c3" />
    <public type="style" name="Theme.Design.Light.NoActionBar" id="0x7f1201c4" />
    <public type="style" name="Theme.Design.NoActionBar" id="0x7f1201c5" />
    <public type="style" name="Theme.MaterialComponents" id="0x7f1201c6" />
    <public type="style" name="Theme.MaterialComponents.BottomSheetDialog" id="0x7f1201c7" />
    <public type="style" name="Theme.MaterialComponents.Bridge" id="0x7f1201c8" />
    <public type="style" name="Theme.MaterialComponents.CompactMenu" id="0x7f1201c9" />
    <public type="style" name="Theme.MaterialComponents.DayNight" id="0x7f1201ca" />
    <public type="style" name="Theme.MaterialComponents.DayNight.BottomSheetDialog" id="0x7f1201cb" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Bridge" id="0x7f1201cc" />
    <public type="style" name="Theme.MaterialComponents.DayNight.DarkActionBar" id="0x7f1201cd" />
    <public type="style" name="Theme.MaterialComponents.DayNight.DarkActionBar.Bridge" id="0x7f1201ce" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog" id="0x7f1201cf" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.Alert" id="0x7f1201d0" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge" id="0x7f1201d1" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.Bridge" id="0x7f1201d2" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.FixedSize" id="0x7f1201d3" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge" id="0x7f1201d4" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.MinWidth" id="0x7f1201d5" />
    <public type="style" name="Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge" id="0x7f1201d6" />
    <public type="style" name="Theme.MaterialComponents.DayNight.DialogWhenLarge" id="0x7f1201d7" />
    <public type="style" name="Theme.MaterialComponents.DayNight.NoActionBar" id="0x7f1201d8" />
    <public type="style" name="Theme.MaterialComponents.DayNight.NoActionBar.Bridge" id="0x7f1201d9" />
    <public type="style" name="Theme.MaterialComponents.Dialog" id="0x7f1201da" />
    <public type="style" name="Theme.MaterialComponents.Dialog.Alert" id="0x7f1201db" />
    <public type="style" name="Theme.MaterialComponents.Dialog.Alert.Bridge" id="0x7f1201dc" />
    <public type="style" name="Theme.MaterialComponents.Dialog.Bridge" id="0x7f1201dd" />
    <public type="style" name="Theme.MaterialComponents.Dialog.FixedSize" id="0x7f1201de" />
    <public type="style" name="Theme.MaterialComponents.Dialog.FixedSize.Bridge" id="0x7f1201df" />
    <public type="style" name="Theme.MaterialComponents.Dialog.MinWidth" id="0x7f1201e0" />
    <public type="style" name="Theme.MaterialComponents.Dialog.MinWidth.Bridge" id="0x7f1201e1" />
    <public type="style" name="Theme.MaterialComponents.DialogWhenLarge" id="0x7f1201e2" />
    <public type="style" name="Theme.MaterialComponents.Light" id="0x7f1201e3" />
    <public type="style" name="Theme.MaterialComponents.Light.BarSize" id="0x7f1201e4" />
    <public type="style" name="Theme.MaterialComponents.Light.BottomSheetDialog" id="0x7f1201e5" />
    <public type="style" name="Theme.MaterialComponents.Light.Bridge" id="0x7f1201e6" />
    <public type="style" name="Theme.MaterialComponents.Light.DarkActionBar" id="0x7f1201e7" />
    <public type="style" name="Theme.MaterialComponents.Light.DarkActionBar.Bridge" id="0x7f1201e8" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog" id="0x7f1201e9" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.Alert" id="0x7f1201ea" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.Alert.Bridge" id="0x7f1201eb" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.Bridge" id="0x7f1201ec" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.FixedSize" id="0x7f1201ed" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge" id="0x7f1201ee" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.MinWidth" id="0x7f1201ef" />
    <public type="style" name="Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge" id="0x7f1201f0" />
    <public type="style" name="Theme.MaterialComponents.Light.DialogWhenLarge" id="0x7f1201f1" />
    <public type="style" name="Theme.MaterialComponents.Light.LargeTouch" id="0x7f1201f2" />
    <public type="style" name="Theme.MaterialComponents.Light.NoActionBar" id="0x7f1201f3" />
    <public type="style" name="Theme.MaterialComponents.Light.NoActionBar.Bridge" id="0x7f1201f4" />
    <public type="style" name="Theme.MaterialComponents.NoActionBar" id="0x7f1201f5" />
    <public type="style" name="Theme.MaterialComponents.NoActionBar.Bridge" id="0x7f1201f6" />
    <public type="style" name="Theme.MyApplication" id="0x7f1201f7" />
    <public type="style" name="ThemeFullView" id="0x7f1201f8" />
    <public type="style" name="ThemeOverlay.AppCompat" id="0x7f1201f9" />
    <public type="style" name="ThemeOverlay.AppCompat.ActionBar" id="0x7f1201fa" />
    <public type="style" name="ThemeOverlay.AppCompat.Dark" id="0x7f1201fb" />
    <public type="style" name="ThemeOverlay.AppCompat.Dark.ActionBar" id="0x7f1201fc" />
    <public type="style" name="ThemeOverlay.AppCompat.DayNight" id="0x7f1201fd" />
    <public type="style" name="ThemeOverlay.AppCompat.DayNight.ActionBar" id="0x7f1201fe" />
    <public type="style" name="ThemeOverlay.AppCompat.Dialog" id="0x7f1201ff" />
    <public type="style" name="ThemeOverlay.AppCompat.Dialog.Alert" id="0x7f120200" />
    <public type="style" name="ThemeOverlay.AppCompat.Light" id="0x7f120201" />
    <public type="style" name="ThemeOverlay.Design.TextInputEditText" id="0x7f120202" />
    <public type="style" name="ThemeOverlay.MaterialComponents" id="0x7f120203" />
    <public type="style" name="ThemeOverlay.MaterialComponents.ActionBar" id="0x7f120204" />
    <public type="style" name="ThemeOverlay.MaterialComponents.ActionBar.Primary" id="0x7f120205" />
    <public type="style" name="ThemeOverlay.MaterialComponents.ActionBar.Surface" id="0x7f120206" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView" id="0x7f120207" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox" id="0x7f120208" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense" id="0x7f120209" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox" id="0x7f12020a" />
    <public type="style" name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense" id="0x7f12020b" />
    <public type="style" name="ThemeOverlay.MaterialComponents.BottomAppBar.Primary" id="0x7f12020c" />
    <public type="style" name="ThemeOverlay.MaterialComponents.BottomAppBar.Surface" id="0x7f12020d" />
    <public type="style" name="ThemeOverlay.MaterialComponents.BottomSheetDialog" id="0x7f12020e" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dark" id="0x7f12020f" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dark.ActionBar" id="0x7f120210" />
    <public type="style" name="ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog" id="0x7f120211" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dialog" id="0x7f120212" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Dialog.Alert" id="0x7f120213" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Light" id="0x7f120214" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Light.BottomSheetDialog" id="0x7f120215" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog" id="0x7f120216" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered" id="0x7f120217" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date" id="0x7f120218" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar" id="0x7f120219" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text" id="0x7f12021a" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day" id="0x7f12021b" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner" id="0x7f12021c" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialCalendar" id="0x7f12021d" />
    <public type="style" name="ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen" id="0x7f12021e" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText" id="0x7f12021f" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox" id="0x7f120220" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense" id="0x7f120221" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox" id="0x7f120222" />
    <public type="style" name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense" id="0x7f120223" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Toolbar.Primary" id="0x7f120224" />
    <public type="style" name="ThemeOverlay.MaterialComponents.Toolbar.Surface" id="0x7f120225" />
    <public type="style" name="Widget.AppCompat.ActionBar" id="0x7f120226" />
    <public type="style" name="Widget.AppCompat.ActionBar.Solid" id="0x7f120227" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabBar" id="0x7f120228" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabText" id="0x7f120229" />
    <public type="style" name="Widget.AppCompat.ActionBar.TabView" id="0x7f12022a" />
    <public type="style" name="Widget.AppCompat.ActionButton" id="0x7f12022b" />
    <public type="style" name="Widget.AppCompat.ActionButton.CloseMode" id="0x7f12022c" />
    <public type="style" name="Widget.AppCompat.ActionButton.Overflow" id="0x7f12022d" />
    <public type="style" name="Widget.AppCompat.ActionMode" id="0x7f12022e" />
    <public type="style" name="Widget.AppCompat.ActivityChooserView" id="0x7f12022f" />
    <public type="style" name="Widget.AppCompat.AutoCompleteTextView" id="0x7f120230" />
    <public type="style" name="Widget.AppCompat.Button" id="0x7f120231" />
    <public type="style" name="Widget.AppCompat.Button.Borderless" id="0x7f120232" />
    <public type="style" name="Widget.AppCompat.Button.Borderless.Colored" id="0x7f120233" />
    <public type="style" name="Widget.AppCompat.Button.ButtonBar.AlertDialog" id="0x7f120234" />
    <public type="style" name="Widget.AppCompat.Button.Colored" id="0x7f120235" />
    <public type="style" name="Widget.AppCompat.Button.Small" id="0x7f120236" />
    <public type="style" name="Widget.AppCompat.ButtonBar" id="0x7f120237" />
    <public type="style" name="Widget.AppCompat.ButtonBar.AlertDialog" id="0x7f120238" />
    <public type="style" name="Widget.AppCompat.CompoundButton.CheckBox" id="0x7f120239" />
    <public type="style" name="Widget.AppCompat.CompoundButton.RadioButton" id="0x7f12023a" />
    <public type="style" name="Widget.AppCompat.CompoundButton.Switch" id="0x7f12023b" />
    <public type="style" name="Widget.AppCompat.DrawerArrowToggle" id="0x7f12023c" />
    <public type="style" name="Widget.AppCompat.DropDownItem.Spinner" id="0x7f12023d" />
    <public type="style" name="Widget.AppCompat.EditText" id="0x7f12023e" />
    <public type="style" name="Widget.AppCompat.ImageButton" id="0x7f12023f" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar" id="0x7f120240" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.Solid" id="0x7f120241" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.Solid.Inverse" id="0x7f120242" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabBar" id="0x7f120243" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabBar.Inverse" id="0x7f120244" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabText" id="0x7f120245" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabText.Inverse" id="0x7f120246" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabView" id="0x7f120247" />
    <public type="style" name="Widget.AppCompat.Light.ActionBar.TabView.Inverse" id="0x7f120248" />
    <public type="style" name="Widget.AppCompat.Light.ActionButton" id="0x7f120249" />
    <public type="style" name="Widget.AppCompat.Light.ActionButton.CloseMode" id="0x7f12024a" />
    <public type="style" name="Widget.AppCompat.Light.ActionButton.Overflow" id="0x7f12024b" />
    <public type="style" name="Widget.AppCompat.Light.ActionMode.Inverse" id="0x7f12024c" />
    <public type="style" name="Widget.AppCompat.Light.ActivityChooserView" id="0x7f12024d" />
    <public type="style" name="Widget.AppCompat.Light.AutoCompleteTextView" id="0x7f12024e" />
    <public type="style" name="Widget.AppCompat.Light.DropDownItem.Spinner" id="0x7f12024f" />
    <public type="style" name="Widget.AppCompat.Light.ListPopupWindow" id="0x7f120250" />
    <public type="style" name="Widget.AppCompat.Light.ListView.DropDown" id="0x7f120251" />
    <public type="style" name="Widget.AppCompat.Light.PopupMenu" id="0x7f120252" />
    <public type="style" name="Widget.AppCompat.Light.PopupMenu.Overflow" id="0x7f120253" />
    <public type="style" name="Widget.AppCompat.Light.SearchView" id="0x7f120254" />
    <public type="style" name="Widget.AppCompat.Light.Spinner.DropDown.ActionBar" id="0x7f120255" />
    <public type="style" name="Widget.AppCompat.ListMenuView" id="0x7f120256" />
    <public type="style" name="Widget.AppCompat.ListPopupWindow" id="0x7f120257" />
    <public type="style" name="Widget.AppCompat.ListView" id="0x7f120258" />
    <public type="style" name="Widget.AppCompat.ListView.DropDown" id="0x7f120259" />
    <public type="style" name="Widget.AppCompat.ListView.Menu" id="0x7f12025a" />
    <public type="style" name="Widget.AppCompat.PopupMenu" id="0x7f12025b" />
    <public type="style" name="Widget.AppCompat.PopupMenu.Overflow" id="0x7f12025c" />
    <public type="style" name="Widget.AppCompat.PopupWindow" id="0x7f12025d" />
    <public type="style" name="Widget.AppCompat.ProgressBar" id="0x7f12025e" />
    <public type="style" name="Widget.AppCompat.ProgressBar.Horizontal" id="0x7f12025f" />
    <public type="style" name="Widget.AppCompat.RatingBar" id="0x7f120260" />
    <public type="style" name="Widget.AppCompat.RatingBar.Indicator" id="0x7f120261" />
    <public type="style" name="Widget.AppCompat.RatingBar.Small" id="0x7f120262" />
    <public type="style" name="Widget.AppCompat.SearchView" id="0x7f120263" />
    <public type="style" name="Widget.AppCompat.SearchView.ActionBar" id="0x7f120264" />
    <public type="style" name="Widget.AppCompat.SeekBar" id="0x7f120265" />
    <public type="style" name="Widget.AppCompat.SeekBar.Discrete" id="0x7f120266" />
    <public type="style" name="Widget.AppCompat.Spinner" id="0x7f120267" />
    <public type="style" name="Widget.AppCompat.Spinner.DropDown" id="0x7f120268" />
    <public type="style" name="Widget.AppCompat.Spinner.DropDown.ActionBar" id="0x7f120269" />
    <public type="style" name="Widget.AppCompat.Spinner.Underlined" id="0x7f12026a" />
    <public type="style" name="Widget.AppCompat.TextView" id="0x7f12026b" />
    <public type="style" name="Widget.AppCompat.TextView.SpinnerItem" id="0x7f12026c" />
    <public type="style" name="Widget.AppCompat.Toolbar" id="0x7f12026d" />
    <public type="style" name="Widget.AppCompat.Toolbar.Button.Navigation" id="0x7f12026e" />
    <public type="style" name="Widget.Compat.NotificationActionContainer" id="0x7f12026f" />
    <public type="style" name="Widget.Compat.NotificationActionText" id="0x7f120270" />
    <public type="style" name="Widget.Design.AppBarLayout" id="0x7f120271" />
    <public type="style" name="Widget.Design.BottomNavigationView" id="0x7f120272" />
    <public type="style" name="Widget.Design.BottomSheet.Modal" id="0x7f120273" />
    <public type="style" name="Widget.Design.CollapsingToolbar" id="0x7f120274" />
    <public type="style" name="Widget.Design.FloatingActionButton" id="0x7f120275" />
    <public type="style" name="Widget.Design.NavigationView" id="0x7f120276" />
    <public type="style" name="Widget.Design.ScrimInsetsFrameLayout" id="0x7f120277" />
    <public type="style" name="Widget.Design.Snackbar" id="0x7f120278" />
    <public type="style" name="Widget.Design.TabLayout" id="0x7f120279" />
    <public type="style" name="Widget.Design.TextInputLayout" id="0x7f12027a" />
    <public type="style" name="Widget.MaterialComponents.ActionBar.Primary" id="0x7f12027b" />
    <public type="style" name="Widget.MaterialComponents.ActionBar.PrimarySurface" id="0x7f12027c" />
    <public type="style" name="Widget.MaterialComponents.ActionBar.Solid" id="0x7f12027d" />
    <public type="style" name="Widget.MaterialComponents.ActionBar.Surface" id="0x7f12027e" />
    <public type="style" name="Widget.MaterialComponents.AppBarLayout.Primary" id="0x7f12027f" />
    <public type="style" name="Widget.MaterialComponents.AppBarLayout.PrimarySurface" id="0x7f120280" />
    <public type="style" name="Widget.MaterialComponents.AppBarLayout.Surface" id="0x7f120281" />
    <public type="style" name="Widget.MaterialComponents.AutoCompleteTextView.FilledBox" id="0x7f120282" />
    <public type="style" name="Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense" id="0x7f120283" />
    <public type="style" name="Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox" id="0x7f120284" />
    <public type="style" name="Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense" id="0x7f120285" />
    <public type="style" name="Widget.MaterialComponents.Badge" id="0x7f120286" />
    <public type="style" name="Widget.MaterialComponents.BottomAppBar" id="0x7f120287" />
    <public type="style" name="Widget.MaterialComponents.BottomAppBar.Colored" id="0x7f120288" />
    <public type="style" name="Widget.MaterialComponents.BottomAppBar.PrimarySurface" id="0x7f120289" />
    <public type="style" name="Widget.MaterialComponents.BottomNavigationView" id="0x7f12028a" />
    <public type="style" name="Widget.MaterialComponents.BottomNavigationView.Colored" id="0x7f12028b" />
    <public type="style" name="Widget.MaterialComponents.BottomNavigationView.PrimarySurface" id="0x7f12028c" />
    <public type="style" name="Widget.MaterialComponents.BottomSheet" id="0x7f12028d" />
    <public type="style" name="Widget.MaterialComponents.BottomSheet.Modal" id="0x7f12028e" />
    <public type="style" name="Widget.MaterialComponents.Button" id="0x7f12028f" />
    <public type="style" name="Widget.MaterialComponents.Button.Icon" id="0x7f120290" />
    <public type="style" name="Widget.MaterialComponents.Button.OutlinedButton" id="0x7f120291" />
    <public type="style" name="Widget.MaterialComponents.Button.OutlinedButton.Icon" id="0x7f120292" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton" id="0x7f120293" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Dialog" id="0x7f120294" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Dialog.Flush" id="0x7f120295" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Dialog.Icon" id="0x7f120296" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Icon" id="0x7f120297" />
    <public type="style" name="Widget.MaterialComponents.Button.TextButton.Snackbar" id="0x7f120298" />
    <public type="style" name="Widget.MaterialComponents.Button.UnelevatedButton" id="0x7f120299" />
    <public type="style" name="Widget.MaterialComponents.Button.UnelevatedButton.Icon" id="0x7f12029a" />
    <public type="style" name="Widget.MaterialComponents.CardView" id="0x7f12029b" />
    <public type="style" name="Widget.MaterialComponents.CheckedTextView" id="0x7f12029c" />
    <public type="style" name="Widget.MaterialComponents.Chip.Action" id="0x7f12029d" />
    <public type="style" name="Widget.MaterialComponents.Chip.Choice" id="0x7f12029e" />
    <public type="style" name="Widget.MaterialComponents.Chip.Entry" id="0x7f12029f" />
    <public type="style" name="Widget.MaterialComponents.Chip.Filter" id="0x7f1202a0" />
    <public type="style" name="Widget.MaterialComponents.ChipGroup" id="0x7f1202a1" />
    <public type="style" name="Widget.MaterialComponents.CompoundButton.CheckBox" id="0x7f1202a2" />
    <public type="style" name="Widget.MaterialComponents.CompoundButton.RadioButton" id="0x7f1202a3" />
    <public type="style" name="Widget.MaterialComponents.CompoundButton.Switch" id="0x7f1202a4" />
    <public type="style" name="Widget.MaterialComponents.ExtendedFloatingActionButton" id="0x7f1202a5" />
    <public type="style" name="Widget.MaterialComponents.ExtendedFloatingActionButton.Icon" id="0x7f1202a6" />
    <public type="style" name="Widget.MaterialComponents.FloatingActionButton" id="0x7f1202a7" />
    <public type="style" name="Widget.MaterialComponents.Light.ActionBar.Solid" id="0x7f1202a8" />
    <public type="style" name="Widget.MaterialComponents.MaterialButtonToggleGroup" id="0x7f1202a9" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar" id="0x7f1202aa" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Day" id="0x7f1202ab" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Day.Invalid" id="0x7f1202ac" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Day.Selected" id="0x7f1202ad" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Day.Today" id="0x7f1202ae" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.DayTextView" id="0x7f1202af" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Fullscreen" id="0x7f1202b0" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton" id="0x7f1202b1" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderDivider" id="0x7f1202b2" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderLayout" id="0x7f1202b3" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderSelection" id="0x7f1202b4" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen" id="0x7f1202b5" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderTitle" id="0x7f1202b6" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton" id="0x7f1202b7" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Item" id="0x7f1202b8" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Year" id="0x7f1202b9" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Year.Selected" id="0x7f1202ba" />
    <public type="style" name="Widget.MaterialComponents.MaterialCalendar.Year.Today" id="0x7f1202bb" />
    <public type="style" name="Widget.MaterialComponents.NavigationView" id="0x7f1202bc" />
    <public type="style" name="Widget.MaterialComponents.PopupMenu" id="0x7f1202bd" />
    <public type="style" name="Widget.MaterialComponents.PopupMenu.ContextMenu" id="0x7f1202be" />
    <public type="style" name="Widget.MaterialComponents.PopupMenu.ListPopupWindow" id="0x7f1202bf" />
    <public type="style" name="Widget.MaterialComponents.PopupMenu.Overflow" id="0x7f1202c0" />
    <public type="style" name="Widget.MaterialComponents.Snackbar" id="0x7f1202c1" />
    <public type="style" name="Widget.MaterialComponents.Snackbar.FullWidth" id="0x7f1202c2" />
    <public type="style" name="Widget.MaterialComponents.TabLayout" id="0x7f1202c3" />
    <public type="style" name="Widget.MaterialComponents.TabLayout.Colored" id="0x7f1202c4" />
    <public type="style" name="Widget.MaterialComponents.TabLayout.PrimarySurface" id="0x7f1202c5" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.FilledBox" id="0x7f1202c6" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.FilledBox.Dense" id="0x7f1202c7" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.OutlinedBox" id="0x7f1202c8" />
    <public type="style" name="Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense" id="0x7f1202c9" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox" id="0x7f1202ca" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense" id="0x7f1202cb" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu" id="0x7f1202cc" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu" id="0x7f1202cd" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox" id="0x7f1202ce" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense" id="0x7f1202cf" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu" id="0x7f1202d0" />
    <public type="style" name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu" id="0x7f1202d1" />
    <public type="style" name="Widget.MaterialComponents.TextView" id="0x7f1202d2" />
    <public type="style" name="Widget.MaterialComponents.Toolbar" id="0x7f1202d3" />
    <public type="style" name="Widget.MaterialComponents.Toolbar.Primary" id="0x7f1202d4" />
    <public type="style" name="Widget.MaterialComponents.Toolbar.PrimarySurface" id="0x7f1202d5" />
    <public type="style" name="Widget.MaterialComponents.Toolbar.Surface" id="0x7f1202d6" />
    <public type="style" name="Widget.Support.CoordinatorLayout" id="0x7f1202d7" />
    <public type="style" name="black_text_style" id="0x7f1202d8" />
    <public type="style" name="dialog_style" id="0x7f1202d9" />
    <public type="style" name="dialogstyleTheme" id="0x7f1202da" />
    <public type="style" name="dividing_line_style" id="0x7f1202db" />
    <public type="style" name="grey_text_style" id="0x7f1202dc" />
    <public type="style" name="light_grey_text_style" id="0x7f1202dd" />
    <public type="style" name="middle_black_text_style" id="0x7f1202de" />
    <public type="style" name="middle_grey_text_style" id="0x7f1202df" />
    <public type="style" name="middle_light_grey_text_style" id="0x7f1202e0" />
    <public type="style" name="middle_theme_color_text_style" id="0x7f1202e1" />
    <public type="style" name="middle_white_text_style" id="0x7f1202e2" />
    <public type="style" name="myCheckBox" id="0x7f1202e3" />
    <public type="style" name="small_grey_text_style" id="0x7f1202e4" />
    <public type="style" name="small_light_grey_text_style" id="0x7f1202e5" />
    <public type="style" name="small_theme_color_text_style" id="0x7f1202e6" />
    <public type="style" name="style_radio_button" id="0x7f1202e7" />
    <public type="style" name="theme_color_text_style" id="0x7f1202e8" />
    <public type="style" name="thin_dividing_line_style" id="0x7f1202e9" />
    <public type="style" name="title_black_text_style" id="0x7f1202ea" />
    <public type="style" name="title_white_text_style" id="0x7f1202eb" />
    <public type="style" name="transparentBgDialog" id="0x7f1202ec" />
    <public type="style" name="video_popup_toast_anim" id="0x7f1202ed" />
    <public type="style" name="video_style_dialog_progress" id="0x7f1202ee" />
    <public type="style" name="video_vertical_progressBar" id="0x7f1202ef" />
    <public type="style" name="white_text_style" id="0x7f1202f0" />
    <public type="xml" name="file_paths" id="0x7f140000" />
    <public type="xml" name="filepaths" id="0x7f140001" />
    <public type="xml" name="network_security_config" id="0x7f140002" />
    <public type="xml" name="standalone_badge" id="0x7f140003" />
    <public type="xml" name="standalone_badge_gravity_bottom_end" id="0x7f140004" />
    <public type="xml" name="standalone_badge_gravity_bottom_start" id="0x7f140005" />
    <public type="xml" name="standalone_badge_gravity_top_start" id="0x7f140006" />
</resources>
