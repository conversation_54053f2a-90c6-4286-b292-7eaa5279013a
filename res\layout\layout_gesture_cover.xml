<?xml version="1.0" encoding="utf-8"?>
<FrameLayout android:id="@id/gesture_control" android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout android:gravity="center" android:layout_gravity="center" android:orientation="vertical" android:id="@id/center_container" android:background="#96000000" android:layout_width="@dimen/dp_160" android:layout_height="@dimen/dp_120">
        <ImageView android:id="@id/iv_icon" android:layout_width="@dimen/dp_36" android:layout_height="@dimen/dp_36" android:src="@drawable/video_volumn_bg" android:scaleType="fitCenter" />
        <TextView android:textSize="@dimen/sp_14" android:textColor="@android:color/white" android:id="@id/tv_percent" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_margin="@dimen/dp_10" android:text="100" />
        <ProgressBar android:id="@id/pro_percent" android:layout_width="@dimen/dp_100" android:layout_height="@dimen/dp_3" android:max="100" android:progressDrawable="@drawable/video_dialog_progress" style="@android:style/Widget.ProgressBar.Horizontal" />
    </LinearLayout>
</FrameLayout>
