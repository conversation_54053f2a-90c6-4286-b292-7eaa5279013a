<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" android:compileSdkVersion="33" android:compileSdkVersionCodename="13" package="cn.ieway.evplayer2" platformBuildVersionCode="33" platformBuildVersionName="13">
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"/>
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO"/>
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO"/>
    <uses-permission android:name="android.permission.INTERNET"/>
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW"/>
    <uses-permission android:name="android.permission.WAKE_LOCK"/>
    <supports-screens android:anyDensity="true" android:largeScreens="true" android:normalScreens="true" android:smallScreens="true" android:xlargeScreens="true"/>
    <meta-data android:name="android.max_aspect" android:value="2.4"/>
    <application android:allowBackup="false" android:appComponentFactory="androidx.core.app.CoreComponentFactory" android:debuggable="false" android:icon="@mipmap/ic_launcher" android:label="@string/app_name" android:largeHeap="true" android:name="com.stub.StubApp" android:networkSecurityConfig="@xml/network_security_config" android:requestLegacyExternalStorage="true" android:roundIcon="@mipmap/ic_launcher" android:supportsRtl="true" android:theme="@style/Theme.MyApplication" android:usesCleartextTraffic="true">
        <service android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" android:enabled="true" android:exported="false" android:name="com.example.myapplication.service.GuarderService" android:priority="1000"/>
        <activity android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" android:exported="false" android:launchMode="singleTop" android:name="com.example.myapplication.ui.privacy.PrivacyAndSecurity" android:screenOrientation="behind"/>
        <service android:enabled="true" android:exported="true" android:name="com.example.myapplication.downloader.evsdownload.EvsDownloadService"/>
        <activity android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" android:exported="false" android:name="com.example.myapplication.ui.other.WebViewActivity" android:screenOrientation="behind" android:windowSoftInputMode="stateAlwaysHidden|adjustPan"/>
        <activity android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" android:exported="false" android:name="com.example.myapplication.ui.coursepage.CourseActivity"/>
        <activity android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" android:exported="false" android:launchMode="singleTask" android:name="com.example.myapplication.ui.dialog.QuestionDialog" android:theme="@style/dialogstyleTheme" android:windowSoftInputMode="stateHidden"/>
        <activity android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" android:exported="false" android:launchMode="singleTask" android:name="com.example.myapplication.ui.dialog.SmsVerifyDialog" android:theme="@style/dialogstyleTheme" android:windowSoftInputMode="stateHidden"/>
        <activity android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" android:exported="false" android:launchMode="singleTask" android:name="com.example.myapplication.ui.dialog.StatementActivity" android:theme="@style/ThemeFullView"/>
        <activity android:exported="false" android:launchMode="singleTop" android:name="com.example.myapplication.ui.activity.ShowClauseActivity" android:theme="@style/dialogstyleTheme"/>
        <activity android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" android:exported="false" android:launchMode="singleTask" android:name="com.example.myapplication.ui.coursedetail.CourseDetailActivity" android:screenOrientation="behind"/>
        <activity android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" android:exported="false" android:launchMode="singleTop" android:name="com.example.myapplication.ui.activity.WebViewActivity" android:screenOrientation="behind"/>
        <activity android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" android:exported="false" android:launchMode="singleTop" android:name="com.example.myapplication.ui.aboutus.AboutUsActivity" android:screenOrientation="behind"/>
        <activity android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" android:exported="false" android:launchMode="singleTop" android:name="com.example.myapplication.ui.suggestion.SuggestionActivity" android:screenOrientation="behind" android:windowSoftInputMode="stateHidden"/>
        <activity android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" android:exported="false" android:launchMode="singleTop" android:name="com.example.myapplication.ui.login.LoginActivity" android:screenOrientation="behind" android:windowSoftInputMode="stateHidden"/>
        <activity android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" android:exported="false" android:launchMode="singleTop" android:name="com.example.myapplication.ui.setting.SettingActivity" android:parentActivityName="com.example.myapplication.ui.main.MainActivity" android:screenOrientation="behind"/>
        <activity android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" android:exported="true" android:launchMode="singleTop" android:name="com.example.myapplication.ui.splash.SplashActivity" android:screenOrientation="behind" android:theme="@style/AppTheme.Splash">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <activity android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode" android:exported="false" android:launchMode="singleTask" android:name="com.example.myapplication.ui.main.MainActivity" android:screenOrientation="behind"/>
        <service android:enabled="true" android:exported="true" android:name="com.example.myapplication.downloader.DownLoadService"/>
        <activity android:name="com.example.myapplication.common.crash.ActivityCrash" android:process=":error_activity"/>
        <provider android:authorities="cn.ieway.evplayer2.tcrashprovider" android:exported="false" android:initOrder="101" android:name="com.example.myapplication.common.crash.TCrashProvider"/>
        <provider android:authorities="cn.ieway.evplayer2.fileProvider" android:exported="false" android:grantUriPermissions="true" android:name="androidx.core.content.FileProvider">
            <meta-data android:name="android.support.FILE_PROVIDER_PATHS" android:resource="@xml/file_paths"/>
        </provider>
        <meta-data android:name="UMENG_APPKEY" android:value="62bc317505844627b5cfadf0"/>
        <meta-data android:name="UMENG_CHANNEL" android:value="evplayer2"/>
        <meta-data android:name="ScopedStorage" android:value="true"/>
        <provider android:authorities="cn.ieway.evplayer2.lifecycle-process" android:exported="false" android:multiprocess="true" android:name="androidx.lifecycle.ProcessLifecycleOwnerInitializer"/>
    </application>
</manifest>
