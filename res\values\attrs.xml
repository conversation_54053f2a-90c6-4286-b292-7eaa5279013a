<?xml version="1.0" encoding="utf-8"?>
<resources>
    <attr name="SharedValue" format="integer" />
    <attr name="SharedValueId" format="reference" />
    <attr name="actionBarDivider" format="reference" />
    <attr name="actionBarItemBackground" format="reference" />
    <attr name="actionBarPopupTheme" format="reference" />
    <attr name="actionBarSize" format="dimension">
        <enum name="wrap_content" value="0" />
    </attr>
    <attr name="actionBarSplitStyle" format="reference" />
    <attr name="actionBarStyle" format="reference" />
    <attr name="actionBarTabBarStyle" format="reference" />
    <attr name="actionBarTabStyle" format="reference" />
    <attr name="actionBarTabTextStyle" format="reference" />
    <attr name="actionBarTheme" format="reference" />
    <attr name="actionBarWidgetTheme" format="reference" />
    <attr name="actionButtonStyle" format="reference" />
    <attr name="actionDropDownStyle" format="reference" />
    <attr name="actionLayout" format="reference" />
    <attr name="actionMenuTextAppearance" format="reference" />
    <attr name="actionMenuTextColor" format="reference|color" />
    <attr name="actionModeBackground" format="reference" />
    <attr name="actionModeCloseButtonStyle" format="reference" />
    <attr name="actionModeCloseDrawable" format="reference" />
    <attr name="actionModeCopyDrawable" format="reference" />
    <attr name="actionModeCutDrawable" format="reference" />
    <attr name="actionModeFindDrawable" format="reference" />
    <attr name="actionModePasteDrawable" format="reference" />
    <attr name="actionModePopupWindowStyle" format="reference" />
    <attr name="actionModeSelectAllDrawable" format="reference" />
    <attr name="actionModeShareDrawable" format="reference" />
    <attr name="actionModeSplitBackground" format="reference" />
    <attr name="actionModeStyle" format="reference" />
    <attr name="actionModeWebSearchDrawable" format="reference" />
    <attr name="actionOverflowButtonStyle" format="reference" />
    <attr name="actionOverflowMenuStyle" format="reference" />
    <attr name="actionProviderClass" format="string" />
    <attr name="actionTextColorAlpha" format="float" />
    <attr name="actionViewClass" format="string" />
    <attr name="activityChooserViewStyle" format="reference" />
    <attr name="adjustable" format="boolean" />
    <attr name="alertDialogButtonGroupStyle" format="reference" />
    <attr name="alertDialogCenterButtons" format="boolean" />
    <attr name="alertDialogStyle" format="reference" />
    <attr name="alertDialogTheme" format="reference" />
    <attr name="alignContent">
        <enum name="flex_start" value="0" />
        <enum name="flex_end" value="1" />
        <enum name="center" value="2" />
        <enum name="space_between" value="3" />
        <enum name="space_around" value="4" />
        <enum name="stretch" value="5" />
    </attr>
    <attr name="alignItems">
        <enum name="flex_start" value="0" />
        <enum name="flex_end" value="1" />
        <enum name="center" value="2" />
        <enum name="baseline" value="3" />
        <enum name="stretch" value="4" />
    </attr>
    <attr name="allowDividerAbove" format="boolean" />
    <attr name="allowDividerAfterLastItem" format="boolean" />
    <attr name="allowDividerBelow" format="boolean" />
    <attr name="allowStacking" format="boolean" />
    <attr name="alpha" format="float" />
    <attr name="alphabeticModifiers">
        <flag name="META" value="0x00010000" />
        <flag name="CTRL" value="0x00001000" />
        <flag name="ALT" value="0x00000002" />
        <flag name="SHIFT" value="0x00000001" />
        <flag name="SYM" value="0x00000004" />
        <flag name="FUNCTION" value="0x00000008" />
    </attr>
    <attr name="altSrc" format="reference" />
    <attr name="animateCircleAngleTo">
        <enum name="bestChoice" value="0" />
        <enum name="closest" value="1" />
        <enum name="clockwise" value="2" />
        <enum name="antiClockwise" value="3" />
        <enum name="constraint" value="4" />
    </attr>
    <attr name="animateRelativeTo" format="reference" />
    <attr name="animationMode">
        <enum name="slide" value="0" />
        <enum name="fade" value="1" />
    </attr>
    <attr name="appBarLayoutStyle" format="reference|string|integer|boolean|color|float|dimension|fraction" />
    <attr name="applyMotionScene" format="boolean" />
    <attr name="arcMode">
        <enum name="startVertical" value="0" />
        <enum name="startHorizontal" value="1" />
        <enum name="flip" value="2" />
    </attr>
    <attr name="arrowHeadLength" format="dimension" />
    <attr name="arrowShaftLength" format="dimension" />
    <attr name="attributeName" format="string" />
    <attr name="autoCompleteMode">
        <enum name="continuousVelocity" value="0" />
        <enum name="spring" value="1" />
    </attr>
    <attr name="autoCompleteTextViewStyle" format="reference" />
    <attr name="autoSizeMaxTextSize" format="dimension" />
    <attr name="autoSizeMinTextSize" format="dimension" />
    <attr name="autoSizePresetSizes" format="reference" />
    <attr name="autoSizeStepGranularity" format="dimension" />
    <attr name="autoSizeTextType">
        <enum name="none" value="0" />
        <enum name="uniform" value="1" />
    </attr>
    <attr name="autoTransition">
        <enum name="none" value="0" />
        <enum name="jumpToStart" value="1" />
        <enum name="jumpToEnd" value="2" />
        <enum name="animateToStart" value="3" />
        <enum name="animateToEnd" value="4" />
    </attr>
    <attr name="background" format="reference" />
    <attr name="backgroundColor" format="color" />
    <attr name="backgroundInsetBottom" format="dimension" />
    <attr name="backgroundInsetEnd" format="dimension" />
    <attr name="backgroundInsetStart" format="dimension" />
    <attr name="backgroundInsetTop" format="dimension" />
    <attr name="backgroundOverlayColorAlpha" format="float" />
    <attr name="backgroundSplit" format="reference|color" />
    <attr name="backgroundStacked" format="reference|color" />
    <attr name="backgroundTint" format="color" />
    <attr name="backgroundTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="backgroundWidth" format="dimension" />
    <attr name="badgeGravity">
        <enum name="TOP_END" value="8388661" />
        <enum name="TOP_START" value="8388659" />
        <enum name="BOTTOM_END" value="8388693" />
        <enum name="BOTTOM_START" value="8388691" />
    </attr>
    <attr name="badgeStyle" format="reference" />
    <attr name="badgeTextColor" format="color" />
    <attr name="banner_auto_loop" format="boolean" />
    <attr name="banner_indicator_gravity">
        <enum name="left" value="0" />
        <enum name="center" value="1" />
        <enum name="right" value="2" />
    </attr>
    <attr name="banner_indicator_height" format="dimension" />
    <attr name="banner_indicator_margin" format="dimension" />
    <attr name="banner_indicator_marginBottom" format="dimension" />
    <attr name="banner_indicator_marginLeft" format="dimension" />
    <attr name="banner_indicator_marginRight" format="dimension" />
    <attr name="banner_indicator_marginTop" format="dimension" />
    <attr name="banner_indicator_normal_color" format="reference|color" />
    <attr name="banner_indicator_normal_width" format="dimension" />
    <attr name="banner_indicator_radius" format="dimension" />
    <attr name="banner_indicator_selected_color" format="reference|color" />
    <attr name="banner_indicator_selected_width" format="dimension" />
    <attr name="banner_indicator_space" format="dimension" />
    <attr name="banner_infinite_loop" format="boolean" />
    <attr name="banner_loop_time" format="integer" />
    <attr name="banner_orientation">
        <enum name="horizontal" value="0" />
        <enum name="vertical" value="1" />
    </attr>
    <attr name="banner_radius" format="dimension" />
    <attr name="banner_round_bottom_left" format="boolean" />
    <attr name="banner_round_bottom_right" format="boolean" />
    <attr name="banner_round_top_left" format="boolean" />
    <attr name="banner_round_top_right" format="boolean" />
    <attr name="barLength" format="dimension" />
    <attr name="barrierAllowsGoneWidgets" format="boolean" />
    <attr name="barrierDirection">
        <enum name="left" value="0" />
        <enum name="right" value="1" />
        <enum name="top" value="2" />
        <enum name="bottom" value="3" />
        <enum name="start" value="5" />
        <enum name="end" value="6" />
    </attr>
    <attr name="barrierMargin" format="dimension" />
    <attr name="behavior_autoHide" format="boolean" />
    <attr name="behavior_autoShrink" format="boolean" />
    <attr name="behavior_expandedOffset" format="reference|integer" />
    <attr name="behavior_fitToContents" format="boolean" />
    <attr name="behavior_halfExpandedRatio" format="reference|float" />
    <attr name="behavior_hideable" format="boolean" />
    <attr name="behavior_overlapTop" format="dimension" />
    <attr name="behavior_peekHeight" format="dimension">
        <enum name="auto" value="-1" />
    </attr>
    <attr name="behavior_saveFlags">
        <flag name="peekHeight" value="0x00000001" />
        <flag name="fitToContents" value="0x00000002" />
        <flag name="hideable" value="0x00000004" />
        <flag name="skipCollapsed" value="0x00000008" />
        <flag name="all" value="0xffffffff" />
        <flag name="none" value="0x00000000" />
    </attr>
    <attr name="behavior_skipCollapsed" format="boolean" />
    <attr name="blendSrc" format="reference" />
    <attr name="borderRound" format="dimension" />
    <attr name="borderRoundPercent" format="float" />
    <attr name="borderWidth" format="dimension" />
    <attr name="borderlessButtonStyle" format="reference" />
    <attr name="bottomAppBarStyle" format="reference" />
    <attr name="bottomNavigationStyle" format="reference" />
    <attr name="bottomSheetDialogTheme" format="reference" />
    <attr name="bottomSheetStyle" format="reference" />
    <attr name="boxBackgroundColor" format="color" />
    <attr name="boxBackgroundMode">
        <enum name="none" value="0" />
        <enum name="filled" value="1" />
        <enum name="outline" value="2" />
    </attr>
    <attr name="boxCollapsedPaddingTop" format="dimension" />
    <attr name="boxCornerRadiusBottomEnd" format="dimension" />
    <attr name="boxCornerRadiusBottomStart" format="dimension" />
    <attr name="boxCornerRadiusTopEnd" format="dimension" />
    <attr name="boxCornerRadiusTopStart" format="dimension" />
    <attr name="boxStrokeColor" format="color" />
    <attr name="boxStrokeWidth" format="dimension" />
    <attr name="boxStrokeWidthFocused" format="dimension" />
    <attr name="brightness" format="float" />
    <attr name="buttonBarButtonStyle" format="reference" />
    <attr name="buttonBarNegativeButtonStyle" format="reference" />
    <attr name="buttonBarNeutralButtonStyle" format="reference" />
    <attr name="buttonBarPositiveButtonStyle" format="reference" />
    <attr name="buttonBarStyle" format="reference" />
    <attr name="buttonCompat" format="reference" />
    <attr name="buttonGravity">
        <flag name="center_vertical" value="0x00000010" />
        <flag name="top" value="0x00000030" />
        <flag name="bottom" value="0x00000050" />
    </attr>
    <attr name="buttonIconDimen" format="dimension" />
    <attr name="buttonPanelSideLayout" format="reference" />
    <attr name="buttonStyle" format="reference" />
    <attr name="buttonStyleSmall" format="reference" />
    <attr name="buttonTint" format="color" />
    <attr name="buttonTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="cardBackgroundColor" format="color" />
    <attr name="cardCornerRadius" format="dimension" />
    <attr name="cardElevation" format="dimension" />
    <attr name="cardForegroundColor" format="color" />
    <attr name="cardMaxElevation" format="dimension" />
    <attr name="cardPreventCornerOverlap" format="boolean" />
    <attr name="cardUseCompatPadding" format="boolean" />
    <attr name="cardViewStyle" format="reference" />
    <attr name="carousel_backwardTransition" format="reference" />
    <attr name="carousel_emptyViewsBehavior">
        <enum name="invisible" value="4" />
        <enum name="gone" value="8" />
    </attr>
    <attr name="carousel_firstView" format="reference" />
    <attr name="carousel_forwardTransition" format="reference" />
    <attr name="carousel_infinite" format="boolean" />
    <attr name="carousel_nextState" format="reference" />
    <attr name="carousel_previousState" format="reference" />
    <attr name="carousel_touchUpMode">
        <enum name="immediateStop" value="1" />
        <enum name="carryVelocity" value="2" />
    </attr>
    <attr name="carousel_touchUp_dampeningFactor" format="float" />
    <attr name="carousel_touchUp_velocityThreshold" format="float" />
    <attr name="chainUseRtl" format="boolean" />
    <attr name="checkBoxPreferenceStyle" format="reference" />
    <attr name="checkboxStyle" format="reference" />
    <attr name="checkedButton" format="reference" />
    <attr name="checkedChip" format="reference" />
    <attr name="checkedIcon" format="reference" />
    <attr name="checkedIconEnabled" format="boolean" />
    <attr name="checkedIconTint" format="color" />
    <attr name="checkedIconVisible" format="boolean" />
    <attr name="checkedTextViewStyle" format="reference" />
    <attr name="chipBackgroundColor" format="color" />
    <attr name="chipCornerRadius" format="dimension" />
    <attr name="chipEndPadding" format="dimension" />
    <attr name="chipGroupStyle" format="reference" />
    <attr name="chipIcon" format="reference" />
    <attr name="chipIconEnabled" format="boolean" />
    <attr name="chipIconSize" format="dimension" />
    <attr name="chipIconTint" format="color" />
    <attr name="chipIconVisible" format="boolean" />
    <attr name="chipMinHeight" format="dimension" />
    <attr name="chipMinTouchTargetSize" format="dimension" />
    <attr name="chipSpacing" format="dimension" />
    <attr name="chipSpacingHorizontal" format="dimension" />
    <attr name="chipSpacingVertical" format="dimension" />
    <attr name="chipStandaloneStyle" format="reference" />
    <attr name="chipStartPadding" format="dimension" />
    <attr name="chipStrokeColor" format="color" />
    <attr name="chipStrokeWidth" format="dimension" />
    <attr name="chipStyle" format="reference" />
    <attr name="chipSurfaceColor" format="color" />
    <attr name="circleRadius" format="dimension" />
    <attr name="circularflow_angles" format="string" />
    <attr name="circularflow_defaultAngle" format="float" />
    <attr name="circularflow_defaultRadius" format="dimension" />
    <attr name="circularflow_radiusInDP" format="string" />
    <attr name="circularflow_viewCenter" format="reference" />
    <attr name="clearsTag" format="reference" />
    <attr name="clickAction">
        <flag name="toggle" value="0x00000011" />
        <flag name="transitionToEnd" value="0x00000001" />
        <flag name="transitionToStart" value="0x00000010" />
        <flag name="jumpToEnd" value="0x00000100" />
        <flag name="jumpToStart" value="0x00001000" />
    </attr>
    <attr name="closeIcon" format="reference" />
    <attr name="closeIconEnabled" format="boolean" />
    <attr name="closeIconEndPadding" format="dimension" />
    <attr name="closeIconSize" format="dimension" />
    <attr name="closeIconStartPadding" format="dimension" />
    <attr name="closeIconTint" format="color" />
    <attr name="closeIconVisible" format="boolean" />
    <attr name="closeItemLayout" format="reference" />
    <attr name="collapseContentDescription" format="string" />
    <attr name="collapseIcon" format="reference" />
    <attr name="collapsedTitleGravity">
        <flag name="top" value="0x00000030" />
        <flag name="bottom" value="0x00000050" />
        <flag name="left" value="0x00000003" />
        <flag name="right" value="0x00000005" />
        <flag name="center_vertical" value="0x00000010" />
        <flag name="fill_vertical" value="0x00000070" />
        <flag name="center_horizontal" value="0x00000001" />
        <flag name="center" value="0x00000011" />
        <flag name="start" value="0x00800003" />
        <flag name="end" value="0x00800005" />
    </attr>
    <attr name="collapsedTitleTextAppearance" format="reference" />
    <attr name="color" format="color" />
    <attr name="colorAccent" format="color" />
    <attr name="colorBackgroundFloating" format="color" />
    <attr name="colorButtonNormal" format="color" />
    <attr name="colorControlActivated" format="color" />
    <attr name="colorControlHighlight" format="color" />
    <attr name="colorControlNormal" format="color" />
    <attr name="colorError" format="reference|color" />
    <attr name="colorOnBackground" format="reference|string|integer|boolean|color|float|dimension|fraction" />
    <attr name="colorOnError" format="color" />
    <attr name="colorOnPrimary" format="color" />
    <attr name="colorOnPrimarySurface" format="color" />
    <attr name="colorOnSecondary" format="color" />
    <attr name="colorOnSurface" format="color" />
    <attr name="colorPrimary" format="color" />
    <attr name="colorPrimaryDark" format="color" />
    <attr name="colorPrimarySurface" format="color" />
    <attr name="colorPrimaryVariant" format="color" />
    <attr name="colorSecondary" format="color" />
    <attr name="colorSecondaryVariant" format="color" />
    <attr name="colorSurface" format="color" />
    <attr name="colorSwitchThumbNormal" format="color" />
    <attr name="commitIcon" format="reference" />
    <attr name="constraintRotate">
        <enum name="none" value="0" />
        <enum name="right" value="1" />
        <enum name="left" value="2" />
        <enum name="x_right" value="3" />
        <enum name="x_left" value="4" />
    </attr>
    <attr name="constraintSet" format="reference" />
    <attr name="constraintSetEnd" format="reference" />
    <attr name="constraintSetStart" format="reference" />
    <attr name="constraint_referenced_ids" format="string" />
    <attr name="constraint_referenced_tags" format="string" />
    <attr name="constraints" format="reference" />
    <attr name="content" format="reference" />
    <attr name="contentDescription" format="string" />
    <attr name="contentInsetEnd" format="dimension" />
    <attr name="contentInsetEndWithActions" format="dimension" />
    <attr name="contentInsetLeft" format="dimension" />
    <attr name="contentInsetRight" format="dimension" />
    <attr name="contentInsetStart" format="dimension" />
    <attr name="contentInsetStartWithNavigation" format="dimension" />
    <attr name="contentPadding" format="dimension" />
    <attr name="contentPaddingBottom" format="dimension" />
    <attr name="contentPaddingLeft" format="dimension" />
    <attr name="contentPaddingRight" format="dimension" />
    <attr name="contentPaddingTop" format="dimension" />
    <attr name="contentScrim" format="color" />
    <attr name="contrast" format="float" />
    <attr name="controlBackground" format="reference" />
    <attr name="coordinatorLayoutStyle" format="reference" />
    <attr name="cornerFamily">
        <enum name="rounded" value="0" />
        <enum name="cut" value="1" />
    </attr>
    <attr name="cornerFamilyBottomLeft">
        <enum name="rounded" value="0" />
        <enum name="cut" value="1" />
    </attr>
    <attr name="cornerFamilyBottomRight">
        <enum name="rounded" value="0" />
        <enum name="cut" value="1" />
    </attr>
    <attr name="cornerFamilyTopLeft">
        <enum name="rounded" value="0" />
        <enum name="cut" value="1" />
    </attr>
    <attr name="cornerFamilyTopRight">
        <enum name="rounded" value="0" />
        <enum name="cut" value="1" />
    </attr>
    <attr name="cornerRadius" format="dimension" />
    <attr name="cornerSize" format="dimension|fraction" />
    <attr name="cornerSizeBottomLeft" format="dimension|fraction" />
    <attr name="cornerSizeBottomRight" format="dimension|fraction" />
    <attr name="cornerSizeTopLeft" format="dimension|fraction" />
    <attr name="cornerSizeTopRight" format="dimension|fraction" />
    <attr name="counterEnabled" format="boolean" />
    <attr name="counterMaxLength" format="integer" />
    <attr name="counterOverflowTextAppearance" format="reference" />
    <attr name="counterOverflowTextColor" format="reference" />
    <attr name="counterTextAppearance" format="reference" />
    <attr name="counterTextColor" format="reference" />
    <attr name="crossfade" format="float" />
    <attr name="currentState" format="reference" />
    <attr name="curveFit">
        <enum name="spline" value="0" />
        <enum name="linear" value="1" />
    </attr>
    <attr name="customBoolean" format="boolean" />
    <attr name="customColorDrawableValue" format="color" />
    <attr name="customColorValue" format="color" />
    <attr name="customDimension" format="dimension" />
    <attr name="customFloatValue" format="float" />
    <attr name="customIntegerValue" format="integer" />
    <attr name="customNavigationLayout" format="reference" />
    <attr name="customPixelDimension" format="dimension" />
    <attr name="customReference" format="reference" />
    <attr name="customStringValue" format="string" />
    <attr name="dayInvalidStyle" format="reference" />
    <attr name="daySelectedStyle" format="reference" />
    <attr name="dayStyle" format="reference" />
    <attr name="dayTodayStyle" format="reference" />
    <attr name="defaultDuration" format="integer" />
    <attr name="defaultQueryHint" format="string" />
    <attr name="defaultState" format="reference" />
    <attr name="defaultValue" format="reference|string|integer|boolean|float" />
    <attr name="deltaPolarAngle" format="float" />
    <attr name="deltaPolarRadius" format="float" />
    <attr name="dependency" format="string" />
    <attr name="deriveConstraintsFrom" format="reference" />
    <attr name="dialogCornerRadius" format="dimension" />
    <attr name="dialogIcon" format="reference" />
    <attr name="dialogLayout" format="reference" />
    <attr name="dialogMessage" format="string" />
    <attr name="dialogPreferenceStyle" format="reference" />
    <attr name="dialogPreferredPadding" format="dimension" />
    <attr name="dialogTheme" format="reference" />
    <attr name="dialogTitle" format="string" />
    <attr name="disableDependentsState" format="boolean" />
    <attr name="displayOptions">
        <flag name="none" value="0x00000000" />
        <flag name="useLogo" value="0x00000001" />
        <flag name="showHome" value="0x00000002" />
        <flag name="homeAsUp" value="0x00000004" />
        <flag name="showTitle" value="0x00000008" />
        <flag name="showCustom" value="0x00000010" />
        <flag name="disableHome" value="0x00000020" />
    </attr>
    <attr name="divider" format="reference" />
    <attr name="dividerDrawable" format="reference" />
    <attr name="dividerDrawableHorizontal" format="reference" />
    <attr name="dividerDrawableVertical" format="reference" />
    <attr name="dividerHorizontal" format="reference" />
    <attr name="dividerPadding" format="dimension" />
    <attr name="dividerVertical" format="reference" />
    <attr name="dragDirection">
        <enum name="dragUp" value="0" />
        <enum name="dragDown" value="1" />
        <enum name="dragLeft" value="2" />
        <enum name="dragRight" value="3" />
        <enum name="dragStart" value="4" />
        <enum name="dragEnd" value="5" />
        <enum name="dragClockwise" value="6" />
        <enum name="dragAnticlockwise" value="7" />
    </attr>
    <attr name="dragScale" format="float" />
    <attr name="dragThreshold" format="float" />
    <attr name="drawPath">
        <enum name="none" value="0" />
        <enum name="path" value="1" />
        <enum name="pathRelative" value="2" />
        <enum name="deltaRelative" value="3" />
        <enum name="asConfigured" value="4" />
        <enum name="rectangles" value="5" />
    </attr>
    <attr name="drawableBottomCompat" format="reference" />
    <attr name="drawableEndCompat" format="reference" />
    <attr name="drawableLeftCompat" format="reference" />
    <attr name="drawableRightCompat" format="reference" />
    <attr name="drawableSize" format="dimension" />
    <attr name="drawableStartCompat" format="reference" />
    <attr name="drawableTint" format="color" />
    <attr name="drawableTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="drawableTopCompat" format="reference" />
    <attr name="drawerArrowStyle" format="reference" />
    <attr name="dropDownListViewStyle" format="reference" />
    <attr name="dropdownListPreferredItemHeight" format="dimension" />
    <attr name="dropdownPreferenceStyle" format="reference" />
    <attr name="duration" format="integer" />
    <attr name="editTextBackground" format="reference" />
    <attr name="editTextColor" format="reference|color" />
    <attr name="editTextPreferenceStyle" format="reference" />
    <attr name="editTextStyle" format="reference" />
    <attr name="elevation" format="dimension" />
    <attr name="elevationOverlayColor" format="color" />
    <attr name="elevationOverlayEnabled" format="boolean" />
    <attr name="enableCopying" format="boolean" />
    <attr name="enabled" format="boolean" />
    <attr name="endIconCheckable" format="boolean" />
    <attr name="endIconContentDescription" format="string" />
    <attr name="endIconDrawable" format="reference" />
    <attr name="endIconMode">
        <enum name="custom" value="-1" />
        <enum name="none" value="0" />
        <enum name="password_toggle" value="1" />
        <enum name="clear_text" value="2" />
        <enum name="dropdown_menu" value="3" />
    </attr>
    <attr name="endIconTint" format="color" />
    <attr name="endIconTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
    </attr>
    <attr name="enforceMaterialTheme" format="boolean" />
    <attr name="enforceTextAppearance" format="boolean" />
    <attr name="ensureMinTouchTargetSize" format="boolean" />
    <attr name="entries" format="reference" />
    <attr name="entrySummaries" format="reference" />
    <attr name="entryValues" format="reference" />
    <attr name="errorEnabled" format="boolean" />
    <attr name="errorIconDrawable" format="reference" />
    <attr name="errorIconTint" format="reference" />
    <attr name="errorIconTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
    </attr>
    <attr name="errorTextAppearance" format="reference" />
    <attr name="errorTextColor" format="color" />
    <attr name="expandActivityOverflowButtonDrawable" format="reference" />
    <attr name="expanded" format="boolean" />
    <attr name="expandedTitleGravity">
        <flag name="top" value="0x00000030" />
        <flag name="bottom" value="0x00000050" />
        <flag name="left" value="0x00000003" />
        <flag name="right" value="0x00000005" />
        <flag name="center_vertical" value="0x00000010" />
        <flag name="fill_vertical" value="0x00000070" />
        <flag name="center_horizontal" value="0x00000001" />
        <flag name="center" value="0x00000011" />
        <flag name="start" value="0x00800003" />
        <flag name="end" value="0x00800005" />
    </attr>
    <attr name="expandedTitleMargin" format="dimension" />
    <attr name="expandedTitleMarginBottom" format="dimension" />
    <attr name="expandedTitleMarginEnd" format="dimension" />
    <attr name="expandedTitleMarginStart" format="dimension" />
    <attr name="expandedTitleMarginTop" format="dimension" />
    <attr name="expandedTitleTextAppearance" format="reference" />
    <attr name="extendMotionSpec" format="reference" />
    <attr name="extendedFloatingActionButtonStyle" format="reference|string|integer|boolean|color|float|dimension|fraction" />
    <attr name="fabAlignmentMode">
        <enum name="center" value="0" />
        <enum name="end" value="1" />
    </attr>
    <attr name="fabAnimationMode">
        <enum name="scale" value="0" />
        <enum name="slide" value="1" />
    </attr>
    <attr name="fabCradleMargin" format="dimension" />
    <attr name="fabCradleRoundedCornerRadius" format="dimension" />
    <attr name="fabCradleVerticalOffset" format="dimension" />
    <attr name="fabCustomSize" format="dimension" />
    <attr name="fabSize">
        <enum name="auto" value="-1" />
        <enum name="normal" value="0" />
        <enum name="mini" value="1" />
    </attr>
    <attr name="fastScrollEnabled" format="boolean" />
    <attr name="fastScrollHorizontalThumbDrawable" format="reference" />
    <attr name="fastScrollHorizontalTrackDrawable" format="reference" />
    <attr name="fastScrollVerticalThumbDrawable" format="reference" />
    <attr name="fastScrollVerticalTrackDrawable" format="reference" />
    <attr name="firstBaselineToTopHeight" format="dimension" />
    <attr name="flexDirection">
        <enum name="row" value="0" />
        <enum name="row_reverse" value="1" />
        <enum name="column" value="2" />
        <enum name="column_reverse" value="3" />
    </attr>
    <attr name="flexWrap">
        <enum name="nowrap" value="0" />
        <enum name="wrap" value="1" />
        <enum name="wrap_reverse" value="2" />
    </attr>
    <attr name="floatingActionButtonStyle" format="reference|string|integer|boolean|color|float|dimension|fraction" />
    <attr name="flow_firstHorizontalBias" format="float" />
    <attr name="flow_firstHorizontalStyle">
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
        <enum name="packed" value="2" />
    </attr>
    <attr name="flow_firstVerticalBias" format="float" />
    <attr name="flow_firstVerticalStyle">
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
        <enum name="packed" value="2" />
    </attr>
    <attr name="flow_horizontalAlign">
        <enum name="start" value="0" />
        <enum name="end" value="1" />
        <enum name="center" value="2" />
    </attr>
    <attr name="flow_horizontalBias" format="float" />
    <attr name="flow_horizontalGap" format="dimension" />
    <attr name="flow_horizontalStyle">
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
        <enum name="packed" value="2" />
    </attr>
    <attr name="flow_lastHorizontalBias" format="float" />
    <attr name="flow_lastHorizontalStyle">
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
        <enum name="packed" value="2" />
    </attr>
    <attr name="flow_lastVerticalBias" format="float" />
    <attr name="flow_lastVerticalStyle">
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
        <enum name="packed" value="2" />
    </attr>
    <attr name="flow_maxElementsWrap" format="integer" />
    <attr name="flow_padding" format="dimension" />
    <attr name="flow_verticalAlign">
        <enum name="top" value="0" />
        <enum name="bottom" value="1" />
        <enum name="center" value="2" />
        <enum name="baseline" value="3" />
    </attr>
    <attr name="flow_verticalBias" format="float" />
    <attr name="flow_verticalGap" format="dimension" />
    <attr name="flow_verticalStyle">
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
        <enum name="packed" value="2" />
    </attr>
    <attr name="flow_wrapMode">
        <enum name="none" value="0" />
        <enum name="chain" value="1" />
        <enum name="aligned" value="2" />
        <enum name="chain2" value="3" />
    </attr>
    <attr name="font" format="reference" />
    <attr name="fontFamily" format="string" />
    <attr name="fontProviderAuthority" format="string" />
    <attr name="fontProviderCerts" format="reference" />
    <attr name="fontProviderFetchStrategy">
        <enum name="blocking" value="0" />
        <enum name="async" value="1" />
    </attr>
    <attr name="fontProviderFetchTimeout" format="integer">
        <enum name="forever" value="-1" />
    </attr>
    <attr name="fontProviderPackage" format="string" />
    <attr name="fontProviderQuery" format="string" />
    <attr name="fontProviderSystemFontFamily" format="string" />
    <attr name="fontStyle">
        <enum name="normal" value="0" />
        <enum name="italic" value="1" />
    </attr>
    <attr name="fontVariationSettings" format="string" />
    <attr name="fontWeight" format="integer" />
    <attr name="foregroundInsidePadding" format="boolean" />
    <attr name="fragment" format="string" />
    <attr name="framePosition" format="integer" />
    <attr name="gapBetweenBars" format="dimension" />
    <attr name="goIcon" format="reference" />
    <attr name="guidelineUseRtl" format="boolean" />
    <attr name="headerLayout" format="reference" />
    <attr name="height" format="dimension" />
    <attr name="helperText" format="string" />
    <attr name="helperTextEnabled" format="boolean" />
    <attr name="helperTextTextAppearance" format="reference" />
    <attr name="helperTextTextColor" format="color" />
    <attr name="hideMotionSpec" format="reference" />
    <attr name="hideOnContentScroll" format="boolean" />
    <attr name="hideOnScroll" format="boolean" />
    <attr name="hintAnimationEnabled" format="boolean" />
    <attr name="hintEnabled" format="boolean" />
    <attr name="hintTextAppearance" format="reference" />
    <attr name="hintTextColor" format="color" />
    <attr name="homeAsUpIndicator" format="reference" />
    <attr name="homeLayout" format="reference" />
    <attr name="hoveredFocusedTranslationZ" format="dimension" />
    <attr name="icon" format="reference" />
    <attr name="iconEndPadding" format="dimension" />
    <attr name="iconGravity">
        <flag name="start" value="0x00000001" />
        <flag name="textStart" value="0x00000002" />
        <flag name="end" value="0x00000003" />
        <flag name="textEnd" value="0x00000004" />
    </attr>
    <attr name="iconPadding" format="dimension" />
    <attr name="iconSize" format="dimension" />
    <attr name="iconSpaceReserved" format="boolean" />
    <attr name="iconStartPadding" format="dimension" />
    <attr name="iconTint" format="color" />
    <attr name="iconTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="iconifiedByDefault" format="boolean" />
    <attr name="ifTagNotSet" format="reference" />
    <attr name="ifTagSet" format="reference" />
    <attr name="imageButtonStyle" format="reference" />
    <attr name="imagePanX" format="float" />
    <attr name="imagePanY" format="float" />
    <attr name="imageRotate" format="float" />
    <attr name="imageZoom" format="float" />
    <attr name="indeterminateProgressStyle" format="reference" />
    <attr name="initialActivityCount" format="string" />
    <attr name="initialExpandedChildrenCount" format="integer" />
    <attr name="insetForeground" format="reference|color" />
    <attr name="isLightTheme" format="boolean" />
    <attr name="isMaterialTheme" format="boolean" />
    <attr name="isPreferenceVisible" format="boolean" />
    <attr name="itemBackground" format="reference" />
    <attr name="itemFillColor" format="color" />
    <attr name="itemHorizontalPadding" format="dimension" />
    <attr name="itemHorizontalTranslationEnabled" format="boolean" />
    <attr name="itemIconPadding" format="dimension" />
    <attr name="itemIconSize" format="dimension" />
    <attr name="itemIconTint" format="color" />
    <attr name="itemMaxLines" format="integer" min="1" />
    <attr name="itemPadding" format="dimension" />
    <attr name="itemRippleColor" format="color" />
    <attr name="itemShapeAppearance" format="reference" />
    <attr name="itemShapeAppearanceOverlay" format="reference" />
    <attr name="itemShapeFillColor" format="color" />
    <attr name="itemShapeInsetBottom" format="dimension" />
    <attr name="itemShapeInsetEnd" format="dimension" />
    <attr name="itemShapeInsetStart" format="dimension" />
    <attr name="itemShapeInsetTop" format="dimension" />
    <attr name="itemSpacing" format="dimension" />
    <attr name="itemStrokeColor" format="color" />
    <attr name="itemStrokeWidth" format="dimension" />
    <attr name="itemTextAppearance" format="reference" />
    <attr name="itemTextAppearanceActive" format="reference" />
    <attr name="itemTextAppearanceInactive" format="reference" />
    <attr name="itemTextColor" format="color" />
    <attr name="justifyContent">
        <enum name="flex_start" value="0" />
        <enum name="flex_end" value="1" />
        <enum name="center" value="2" />
        <enum name="space_between" value="3" />
        <enum name="space_around" value="4" />
        <enum name="space_evenly" value="5" />
    </attr>
    <attr name="key" format="string" />
    <attr name="keyPositionType">
        <enum name="deltaRelative" value="0" />
        <enum name="pathRelative" value="1" />
        <enum name="parentRelative" value="2" />
    </attr>
    <attr name="keylines" format="reference" />
    <attr name="lStar" format="float" />
    <attr name="labelVisibilityMode">
        <enum name="auto" value="-1" />
        <enum name="selected" value="0" />
        <enum name="labeled" value="1" />
        <enum name="unlabeled" value="2" />
    </attr>
    <attr name="lastBaselineToBottomHeight" format="dimension" />
    <attr name="layout" format="reference" />
    <attr name="layoutDescription" format="reference" />
    <attr name="layoutDuringTransition">
        <enum name="ignoreRequest" value="0" />
        <enum name="honorRequest" value="1" />
        <enum name="callMeasure" value="2" />
    </attr>
    <attr name="layoutManager" format="string" />
    <attr name="layout_alignSelf">
        <enum name="auto" value="-1" />
        <enum name="flex_start" value="0" />
        <enum name="flex_end" value="1" />
        <enum name="center" value="2" />
        <enum name="baseline" value="3" />
        <enum name="stretch" value="4" />
    </attr>
    <attr name="layout_anchor" format="reference" />
    <attr name="layout_anchorGravity">
        <flag name="top" value="0x00000030" />
        <flag name="bottom" value="0x00000050" />
        <flag name="left" value="0x00000003" />
        <flag name="right" value="0x00000005" />
        <flag name="center_vertical" value="0x00000010" />
        <flag name="fill_vertical" value="0x00000070" />
        <flag name="center_horizontal" value="0x00000001" />
        <flag name="fill_horizontal" value="0x00000007" />
        <flag name="center" value="0x00000011" />
        <flag name="fill" value="0x00000077" />
        <flag name="clip_vertical" value="0x00000080" />
        <flag name="clip_horizontal" value="0x00000008" />
        <flag name="start" value="0x00800003" />
        <flag name="end" value="0x00800005" />
    </attr>
    <attr name="layout_behavior" format="string" />
    <attr name="layout_collapseMode">
        <enum name="none" value="0" />
        <enum name="pin" value="1" />
        <enum name="parallax" value="2" />
    </attr>
    <attr name="layout_collapseParallaxMultiplier" format="float" />
    <attr name="layout_constrainedHeight" format="boolean" />
    <attr name="layout_constrainedWidth" format="boolean" />
    <attr name="layout_constraintBaseline_creator" format="integer" />
    <attr name="layout_constraintBaseline_toBaselineOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintBaseline_toBottomOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintBaseline_toTopOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintBottom_creator" format="integer" />
    <attr name="layout_constraintBottom_toBottomOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintBottom_toTopOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintCircle" format="reference" />
    <attr name="layout_constraintCircleAngle" format="float" />
    <attr name="layout_constraintCircleRadius" format="dimension" />
    <attr name="layout_constraintDimensionRatio" format="string" />
    <attr name="layout_constraintEnd_toEndOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintEnd_toStartOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintGuide_begin" format="dimension" />
    <attr name="layout_constraintGuide_end" format="dimension" />
    <attr name="layout_constraintGuide_percent" format="float" />
    <attr name="layout_constraintHeight" format="string|dimension">
        <enum name="match_parent" value="-1" />
        <enum name="wrap_content" value="-2" />
        <enum name="match_constraint" value="-3" />
        <enum name="wrap_content_constrained" value="-4" />
    </attr>
    <attr name="layout_constraintHeight_default">
        <enum name="spread" value="0" />
        <enum name="wrap" value="1" />
        <enum name="percent" value="2" />
    </attr>
    <attr name="layout_constraintHeight_max" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintHeight_min" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintHeight_percent" format="float" />
    <attr name="layout_constraintHorizontal_bias" format="float" />
    <attr name="layout_constraintHorizontal_chainStyle">
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
        <enum name="packed" value="2" />
    </attr>
    <attr name="layout_constraintHorizontal_weight" format="float" />
    <attr name="layout_constraintLeft_creator" format="integer" />
    <attr name="layout_constraintLeft_toLeftOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintLeft_toRightOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintRight_creator" format="integer" />
    <attr name="layout_constraintRight_toLeftOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintRight_toRightOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintStart_toEndOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintStart_toStartOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintTag" format="string" />
    <attr name="layout_constraintTop_creator" format="integer" />
    <attr name="layout_constraintTop_toBottomOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintTop_toTopOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintVertical_bias" format="float" />
    <attr name="layout_constraintVertical_chainStyle">
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
        <enum name="packed" value="2" />
    </attr>
    <attr name="layout_constraintVertical_weight" format="float" />
    <attr name="layout_constraintWidth" format="string|dimension">
        <enum name="match_parent" value="-1" />
        <enum name="wrap_content" value="-2" />
        <enum name="match_constraint" value="-3" />
        <enum name="wrap_content_constrained" value="-4" />
    </attr>
    <attr name="layout_constraintWidth_default">
        <enum name="spread" value="0" />
        <enum name="wrap" value="1" />
        <enum name="percent" value="2" />
    </attr>
    <attr name="layout_constraintWidth_max" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintWidth_min" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintWidth_percent" format="float" />
    <attr name="layout_dodgeInsetEdges">
        <flag name="none" value="0x00000000" />
        <flag name="top" value="0x00000030" />
        <flag name="bottom" value="0x00000050" />
        <flag name="left" value="0x00000003" />
        <flag name="right" value="0x00000005" />
        <flag name="start" value="0x00800003" />
        <flag name="end" value="0x00800005" />
        <flag name="all" value="0x00000077" />
    </attr>
    <attr name="layout_editor_absoluteX" format="dimension" />
    <attr name="layout_editor_absoluteY" format="dimension" />
    <attr name="layout_flexBasisPercent" format="fraction" />
    <attr name="layout_flexGrow" format="float" />
    <attr name="layout_flexShrink" format="float" />
    <attr name="layout_goneMarginBaseline" format="dimension" />
    <attr name="layout_goneMarginBottom" format="dimension" />
    <attr name="layout_goneMarginEnd" format="dimension" />
    <attr name="layout_goneMarginLeft" format="dimension" />
    <attr name="layout_goneMarginRight" format="dimension" />
    <attr name="layout_goneMarginStart" format="dimension" />
    <attr name="layout_goneMarginTop" format="dimension" />
    <attr name="layout_insetEdge">
        <enum name="none" value="0" />
        <enum name="top" value="48" />
        <enum name="bottom" value="80" />
        <enum name="left" value="3" />
        <enum name="right" value="5" />
        <enum name="start" value="8388611" />
        <enum name="end" value="8388613" />
    </attr>
    <attr name="layout_keyline" format="integer" />
    <attr name="layout_marginBaseline" format="dimension" />
    <attr name="layout_maxHeight" format="dimension" />
    <attr name="layout_maxWidth" format="dimension" />
    <attr name="layout_minHeight" format="dimension" />
    <attr name="layout_minWidth" format="dimension" />
    <attr name="layout_optimizationLevel">
        <flag name="none" value="0x00000000" />
        <flag name="legacy" value="0x00000000" />
        <flag name="standard" value="0x00000101" />
        <flag name="direct" value="0x00000001" />
        <flag name="barrier" value="0x00000002" />
        <flag name="chains" value="0x00000004" />
        <flag name="dimensions" value="0x00000008" />
        <flag name="ratio" value="0x00000010" />
        <flag name="groups" value="0x00000020" />
        <flag name="graph" value="0x00000040" />
        <flag name="graph_wrap" value="0x00000080" />
        <flag name="cache_measures" value="0x00000100" />
        <flag name="dependency_ordering" value="0x00000200" />
        <flag name="grouping" value="0x00000400" />
    </attr>
    <attr name="layout_order" format="integer" />
    <attr name="layout_scrollFlags">
        <flag name="noScroll" value="0x00000000" />
        <flag name="scroll" value="0x00000001" />
        <flag name="exitUntilCollapsed" value="0x00000002" />
        <flag name="enterAlways" value="0x00000004" />
        <flag name="enterAlwaysCollapsed" value="0x00000008" />
        <flag name="snap" value="0x00000010" />
        <flag name="snapMargins" value="0x00000020" />
    </attr>
    <attr name="layout_scrollInterpolator" format="reference" />
    <attr name="layout_wrapBefore" format="boolean" />
    <attr name="layout_wrapBehaviorInParent">
        <enum name="included" value="0" />
        <enum name="horizontal_only" value="1" />
        <enum name="vertical_only" value="2" />
        <enum name="skipped" value="3" />
    </attr>
    <attr name="liftOnScroll" format="boolean" />
    <attr name="liftOnScrollTargetViewId" format="reference" />
    <attr name="limitBoundsTo" format="reference" />
    <attr name="lineHeight" format="dimension" />
    <attr name="lineSpacing" format="dimension" />
    <attr name="listChoiceBackgroundIndicator" format="reference" />
    <attr name="listChoiceIndicatorMultipleAnimated" format="reference" />
    <attr name="listChoiceIndicatorSingleAnimated" format="reference" />
    <attr name="listDividerAlertDialog" format="reference" />
    <attr name="listItemLayout" format="reference" />
    <attr name="listLayout" format="reference" />
    <attr name="listMenuViewStyle" format="reference" />
    <attr name="listPopupWindowStyle" format="reference" />
    <attr name="listPreferredItemHeight" format="dimension" />
    <attr name="listPreferredItemHeightLarge" format="dimension" />
    <attr name="listPreferredItemHeightSmall" format="dimension" />
    <attr name="listPreferredItemPaddingEnd" format="dimension" />
    <attr name="listPreferredItemPaddingLeft" format="dimension" />
    <attr name="listPreferredItemPaddingRight" format="dimension" />
    <attr name="listPreferredItemPaddingStart" format="dimension" />
    <attr name="logo" format="reference" />
    <attr name="logoDescription" format="string" />
    <attr name="materialAlertDialogBodyTextStyle" format="reference" />
    <attr name="materialAlertDialogTheme" format="reference" />
    <attr name="materialAlertDialogTitleIconStyle" format="reference" />
    <attr name="materialAlertDialogTitlePanelStyle" format="reference" />
    <attr name="materialAlertDialogTitleTextStyle" format="reference" />
    <attr name="materialButtonOutlinedStyle" format="reference" />
    <attr name="materialButtonStyle" format="reference" />
    <attr name="materialButtonToggleGroupStyle" format="reference" />
    <attr name="materialCalendarDay" format="reference" />
    <attr name="materialCalendarFullscreenTheme" format="reference" />
    <attr name="materialCalendarHeaderConfirmButton" format="reference" />
    <attr name="materialCalendarHeaderDivider" format="reference" />
    <attr name="materialCalendarHeaderLayout" format="reference" />
    <attr name="materialCalendarHeaderSelection" format="reference" />
    <attr name="materialCalendarHeaderTitle" format="reference" />
    <attr name="materialCalendarHeaderToggleButton" format="reference" />
    <attr name="materialCalendarStyle" format="reference" />
    <attr name="materialCalendarTheme" format="reference" />
    <attr name="materialCardViewStyle" format="reference" />
    <attr name="materialThemeOverlay" format="reference" />
    <attr name="maxAcceleration" format="float" />
    <attr name="maxActionInlineWidth" format="dimension" />
    <attr name="maxButtonHeight" format="dimension" />
    <attr name="maxCharacterCount" format="integer" />
    <attr name="maxHeight" format="dimension" />
    <attr name="maxImageSize" format="dimension" />
    <attr name="maxLine" format="integer" />
    <attr name="maxValue" format="float" />
    <attr name="maxVelocity" format="float" />
    <attr name="maxWidth" format="dimension" />
    <attr name="measureHeight" format="boolean" />
    <attr name="measureWithLargestChild" format="boolean" />
    <attr name="menu" format="reference" />
    <attr name="methodName" format="string" />
    <attr name="min" format="integer" />
    <attr name="minHeight" format="dimension" />
    <attr name="minTouchTargetSize" format="dimension" />
    <attr name="minWidth" format="dimension" />
    <attr name="mock_diagonalsColor" format="color" />
    <attr name="mock_label" format="string" />
    <attr name="mock_labelBackgroundColor" format="color" />
    <attr name="mock_labelColor" format="color" />
    <attr name="mock_showDiagonals" format="boolean" />
    <attr name="mock_showLabel" format="boolean" />
    <attr name="motionDebug">
        <enum name="NO_DEBUG" value="0" />
        <enum name="SHOW_PROGRESS" value="1" />
        <enum name="SHOW_PATH" value="2" />
        <enum name="SHOW_ALL" value="3" />
    </attr>
    <attr name="motionEffect_alpha" format="float" />
    <attr name="motionEffect_end" format="integer" />
    <attr name="motionEffect_move">
        <enum name="auto" value="-1" />
        <enum name="north" value="0" />
        <enum name="south" value="1" />
        <enum name="east" value="2" />
        <enum name="west" value="3" />
    </attr>
    <attr name="motionEffect_start" format="integer" />
    <attr name="motionEffect_strict" format="boolean" />
    <attr name="motionEffect_translationX" format="dimension" />
    <attr name="motionEffect_translationY" format="dimension" />
    <attr name="motionEffect_viewTransition" format="reference" />
    <attr name="motionInterpolator" format="reference|string">
        <enum name="easeInOut" value="0" />
        <enum name="easeIn" value="1" />
        <enum name="easeOut" value="2" />
        <enum name="linear" value="3" />
        <enum name="bounce" value="4" />
        <enum name="overshoot" value="5" />
        <enum name="anticipate" value="6" />
    </attr>
    <attr name="motionPathRotate" format="float" />
    <attr name="motionProgress" format="float" />
    <attr name="motionStagger" format="float" />
    <attr name="motionTarget" format="reference|string" />
    <attr name="motion_postLayoutCollision" format="boolean" />
    <attr name="motion_triggerOnCollision" format="reference" />
    <attr name="moveWhenScrollAtTop" format="boolean" />
    <attr name="multiChoiceItemLayout" format="reference" />
    <attr name="navigationContentDescription" format="string" />
    <attr name="navigationIcon" format="reference" />
    <attr name="navigationMode">
        <enum name="normal" value="0" />
        <enum name="listMode" value="1" />
        <enum name="tabMode" value="2" />
    </attr>
    <attr name="navigationViewStyle" format="reference" />
    <attr name="negativeButtonText" format="string" />
    <attr name="nestedScrollFlags">
        <flag name="none" value="0x00000000" />
        <flag name="disablePostScroll" value="0x00000001" />
        <flag name="disableScroll" value="0x00000002" />
        <flag name="supportScrollUp" value="0x00000004" />
    </attr>
    <attr name="nestedScrollViewStyle" format="reference" />
    <attr name="normal_drawable" format="reference" />
    <attr name="number" format="integer" />
    <attr name="numericModifiers">
        <flag name="META" value="0x00010000" />
        <flag name="CTRL" value="0x00001000" />
        <flag name="ALT" value="0x00000002" />
        <flag name="SHIFT" value="0x00000001" />
        <flag name="SYM" value="0x00000004" />
        <flag name="FUNCTION" value="0x00000008" />
    </attr>
    <attr name="onCross" format="string" />
    <attr name="onHide" format="boolean" />
    <attr name="onNegativeCross" format="string" />
    <attr name="onPositiveCross" format="string" />
    <attr name="onShow" format="boolean" />
    <attr name="onStateTransition">
        <enum name="actionDown" value="1" />
        <enum name="actionUp" value="2" />
        <enum name="actionDownUp" value="3" />
        <enum name="sharedValueSet" value="4" />
        <enum name="sharedValueUnset" value="5" />
    </attr>
    <attr name="onTouchUp">
        <enum name="autoComplete" value="0" />
        <enum name="autoCompleteToStart" value="1" />
        <enum name="autoCompleteToEnd" value="2" />
        <enum name="stop" value="3" />
        <enum name="decelerate" value="4" />
        <enum name="decelerateAndComplete" value="5" />
        <enum name="neverCompleteToStart" value="6" />
        <enum name="neverCompleteToEnd" value="7" />
    </attr>
    <attr name="order" format="integer" />
    <attr name="orderingFromXml" format="boolean" />
    <attr name="outlineColor" format="color" />
    <attr name="outlineWidth" format="float" />
    <attr name="overlapAnchor" format="boolean" />
    <attr name="overlay" format="boolean" />
    <attr name="paddingBottomNoButtons" format="dimension" />
    <attr name="paddingEnd" format="dimension" />
    <attr name="paddingStart" format="dimension" />
    <attr name="paddingTopNoTitle" format="dimension" />
    <attr name="panelBackground" format="reference" />
    <attr name="panelMenuListTheme" format="reference" />
    <attr name="panelMenuListWidth" format="dimension" />
    <attr name="passwordToggleContentDescription" format="string" />
    <attr name="passwordToggleDrawable" format="reference" />
    <attr name="passwordToggleEnabled" format="boolean" />
    <attr name="passwordToggleTint" format="color" />
    <attr name="passwordToggleTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
    </attr>
    <attr name="pathMotionArc">
        <enum name="none" value="0" />
        <enum name="startVertical" value="1" />
        <enum name="startHorizontal" value="2" />
        <enum name="flip" value="3" />
    </attr>
    <attr name="path_percent" format="float" />
    <attr name="percentHeight" format="float" />
    <attr name="percentWidth" format="float" />
    <attr name="percentX" format="float" />
    <attr name="percentY" format="float" />
    <attr name="perpendicularPath_percent" format="float" />
    <attr name="persistent" format="boolean" />
    <attr name="pivotAnchor" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="placeholder_emptyVisibility">
        <enum name="visible" value="0" />
        <enum name="invisible" value="4" />
        <enum name="gone" value="8" />
    </attr>
    <attr name="polarRelativeTo" format="reference" />
    <attr name="popupMenuBackground" format="reference" />
    <attr name="popupMenuStyle" format="reference" />
    <attr name="popupTheme" format="reference" />
    <attr name="popupWindowStyle" format="reference" />
    <attr name="positiveButtonText" format="string" />
    <attr name="preferenceCategoryStyle" format="reference" />
    <attr name="preferenceCategoryTitleTextAppearance" format="reference" />
    <attr name="preferenceFragmentCompatStyle" format="reference" />
    <attr name="preferenceFragmentListStyle" format="reference" />
    <attr name="preferenceFragmentStyle" format="reference" />
    <attr name="preferenceInformationStyle" format="reference" />
    <attr name="preferenceScreenStyle" format="reference" />
    <attr name="preferenceStyle" format="reference" />
    <attr name="preferenceTheme" format="reference" />
    <attr name="prefix" format="string" />
    <attr name="preserveIconSpacing" format="boolean" />
    <attr name="pressedTranslationZ" format="dimension" />
    <attr name="progressBarPadding" format="dimension" />
    <attr name="progressBarStyle" format="reference" />
    <attr name="progressColor" format="color" />
    <attr name="progressText" format="string" />
    <attr name="progressTextColor" format="color" />
    <attr name="quantizeMotionInterpolator" format="reference|string">
        <enum name="easeInOut" value="0" />
        <enum name="easeIn" value="1" />
        <enum name="easeOut" value="2" />
        <enum name="linear" value="3" />
        <enum name="bounce" value="4" />
        <enum name="overshoot" value="5" />
    </attr>
    <attr name="quantizeMotionPhase" format="float" />
    <attr name="quantizeMotionSteps" format="integer" />
    <attr name="queryBackground" format="reference" />
    <attr name="queryHint" format="string" />
    <attr name="queryPatterns" format="reference" />
    <attr name="radioButtonStyle" format="reference" />
    <attr name="rangeFillColor" format="color" />
    <attr name="ratingBarStyle" format="reference" />
    <attr name="ratingBarStyleIndicator" format="reference" />
    <attr name="ratingBarStyleSmall" format="reference" />
    <attr name="reactiveGuide_animateChange" format="boolean" />
    <attr name="reactiveGuide_applyToAllConstraintSets" format="boolean" />
    <attr name="reactiveGuide_applyToConstraintSet" format="reference" />
    <attr name="reactiveGuide_valueId" format="reference" />
    <attr name="recyclerViewStyle" format="reference" />
    <attr name="region_heightLessThan" format="dimension" />
    <attr name="region_heightMoreThan" format="dimension" />
    <attr name="region_widthLessThan" format="dimension" />
    <attr name="region_widthMoreThan" format="dimension" />
    <attr name="reverseLayout" format="boolean" />
    <attr name="rippleColor" format="color" />
    <attr name="rotationCenterId" format="reference" />
    <attr name="round" format="dimension" />
    <attr name="roundPercent" format="float" />
    <attr name="roundedCorners" format="boolean" />
    <attr name="saturation" format="float" />
    <attr name="scaleFromTextSize" format="dimension" />
    <attr name="scrimAnimationDuration" format="integer" />
    <attr name="scrimBackground" format="reference|color" />
    <attr name="scrimVisibleHeightTrigger" format="dimension" />
    <attr name="searchHintIcon" format="reference" />
    <attr name="searchIcon" format="reference" />
    <attr name="searchViewStyle" format="reference" />
    <attr name="seekBarIncrement" format="integer" />
    <attr name="seekBarPreferenceStyle" format="reference" />
    <attr name="seekBarStyle" format="reference" />
    <attr name="selectable" format="boolean" />
    <attr name="selectableItemBackground" format="reference" />
    <attr name="selectableItemBackgroundBorderless" format="reference" />
    <attr name="selected_drawable" format="reference" />
    <attr name="setsTag" format="reference" />
    <attr name="shapeAppearance" format="reference" />
    <attr name="shapeAppearanceLargeComponent" format="reference" />
    <attr name="shapeAppearanceMediumComponent" format="reference" />
    <attr name="shapeAppearanceOverlay" format="reference" />
    <attr name="shapeAppearanceSmallComponent" format="reference" />
    <attr name="shortcutMatchRequired" format="boolean" />
    <attr name="shouldDisableView" format="boolean" />
    <attr name="showAsAction">
        <flag name="never" value="0x00000000" />
        <flag name="ifRoom" value="0x00000001" />
        <flag name="always" value="0x00000002" />
        <flag name="withText" value="0x00000004" />
        <flag name="collapseActionView" value="0x00000008" />
    </attr>
    <attr name="showDivider">
        <flag name="none" value="0x00000000" />
        <flag name="beginning" value="0x00000001" />
        <flag name="middle" value="0x00000002" />
        <flag name="end" value="0x00000004" />
    </attr>
    <attr name="showDividerHorizontal">
        <flag name="none" value="0x00000000" />
        <flag name="beginning" value="0x00000001" />
        <flag name="middle" value="0x00000002" />
        <flag name="end" value="0x00000004" />
    </attr>
    <attr name="showDividerVertical">
        <flag name="none" value="0x00000000" />
        <flag name="beginning" value="0x00000001" />
        <flag name="middle" value="0x00000002" />
        <flag name="end" value="0x00000004" />
    </attr>
    <attr name="showDividers">
        <flag name="none" value="0x00000000" />
        <flag name="beginning" value="0x00000001" />
        <flag name="middle" value="0x00000002" />
        <flag name="end" value="0x00000004" />
    </attr>
    <attr name="showMotionSpec" format="reference" />
    <attr name="showPaths" format="boolean" />
    <attr name="showSeekBarValue" format="boolean" />
    <attr name="showText" format="boolean" />
    <attr name="showTitle" format="boolean" />
    <attr name="shrinkMotionSpec" format="reference" />
    <attr name="singleChoiceItemLayout" format="reference" />
    <attr name="singleLine" format="boolean" />
    <attr name="singleLineTitle" format="boolean" />
    <attr name="singleSelection" format="boolean" />
    <attr name="sizePercent" format="float" />
    <attr name="snackbarButtonStyle" format="reference" />
    <attr name="snackbarStyle" format="reference" />
    <attr name="spanCount" format="integer" />
    <attr name="spinBars" format="boolean" />
    <attr name="spinnerDropDownItemStyle" format="reference" />
    <attr name="spinnerStyle" format="reference" />
    <attr name="splitTrack" format="boolean" />
    <attr name="springBoundary">
        <flag name="overshoot" value="0x00000000" />
        <flag name="bounceStart" value="0x00000001" />
        <flag name="bounceEnd" value="0x00000002" />
        <flag name="bounceBoth" value="0x00000003" />
    </attr>
    <attr name="springDamping" format="float" />
    <attr name="springMass" format="float" />
    <attr name="springStiffness" format="float" />
    <attr name="springStopThreshold" format="float" />
    <attr name="srcCompat" format="reference" />
    <attr name="stackFromEnd" format="boolean" />
    <attr name="staggered" format="float" />
    <attr name="startIconCheckable" format="boolean" />
    <attr name="startIconContentDescription" format="string" />
    <attr name="startIconDrawable" format="reference" />
    <attr name="startIconTint" format="color" />
    <attr name="startIconTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
    </attr>
    <attr name="state_above_anchor" format="boolean" />
    <attr name="state_collapsed" format="boolean" />
    <attr name="state_collapsible" format="boolean" />
    <attr name="state_dragged" format="boolean" />
    <attr name="state_liftable" format="boolean" />
    <attr name="state_lifted" format="boolean" />
    <attr name="statusBarBackground" format="reference|color" />
    <attr name="statusBarForeground" format="color" />
    <attr name="statusBarScrim" format="color" />
    <attr name="strokeColor" format="color" />
    <attr name="strokeWidth" format="dimension" />
    <attr name="strokeWidthDimension" format="dimension" />
    <attr name="subMenuArrow" format="reference" />
    <attr name="submitBackground" format="reference" />
    <attr name="subtitle" format="string" />
    <attr name="subtitleTextAppearance" format="reference" />
    <attr name="subtitleTextColor" format="color" />
    <attr name="subtitleTextStyle" format="reference" />
    <attr name="suffix" format="string" />
    <attr name="suggestionRowLayout" format="reference" />
    <attr name="summary" format="string" />
    <attr name="summaryOff" format="string" />
    <attr name="summaryOn" format="string" />
    <attr name="swipeRefreshLayoutProgressSpinnerBackgroundColor" format="color" />
    <attr name="switchMinWidth" format="dimension" />
    <attr name="switchPadding" format="dimension" />
    <attr name="switchPreferenceCompatStyle" format="reference" />
    <attr name="switchPreferenceStyle" format="reference" />
    <attr name="switchStyle" format="reference" />
    <attr name="switchTextAppearance" format="reference" />
    <attr name="switchTextOff" format="string" />
    <attr name="switchTextOn" format="string" />
    <attr name="tabBackground" format="reference" />
    <attr name="tabContentStart" format="dimension" />
    <attr name="tabGravity">
        <enum name="fill" value="0" />
        <enum name="center" value="1" />
    </attr>
    <attr name="tabIconTint" format="color" />
    <attr name="tabIconTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="tabIndicator" format="reference" />
    <attr name="tabIndicatorAnimationDuration" format="integer" />
    <attr name="tabIndicatorColor" format="color" />
    <attr name="tabIndicatorFullWidth" format="boolean" />
    <attr name="tabIndicatorGravity">
        <enum name="bottom" value="0" />
        <enum name="center" value="1" />
        <enum name="top" value="2" />
        <enum name="stretch" value="3" />
    </attr>
    <attr name="tabIndicatorHeight" format="dimension" />
    <attr name="tabInlineLabel" format="boolean" />
    <attr name="tabMaxWidth" format="dimension" />
    <attr name="tabMinWidth" format="dimension" />
    <attr name="tabMode">
        <enum name="scrollable" value="0" />
        <enum name="fixed" value="1" />
        <enum name="auto" value="2" />
    </attr>
    <attr name="tabPadding" format="dimension" />
    <attr name="tabPaddingBottom" format="dimension" />
    <attr name="tabPaddingEnd" format="dimension" />
    <attr name="tabPaddingStart" format="dimension" />
    <attr name="tabPaddingTop" format="dimension" />
    <attr name="tabRippleColor" format="color" />
    <attr name="tabSelectedTextColor" format="color" />
    <attr name="tabStyle" format="reference" />
    <attr name="tabTextAppearance" format="reference" />
    <attr name="tabTextColor" format="color" />
    <attr name="tabUnboundedRipple" format="boolean" />
    <attr name="targetId" format="reference" />
    <attr name="telltales_tailColor" format="color" />
    <attr name="telltales_tailScale" format="float" />
    <attr name="telltales_velocityMode">
        <enum name="layout" value="0" />
        <enum name="postLayout" value="1" />
        <enum name="staticPostLayout" value="2" />
        <enum name="staticLayout" value="3" />
    </attr>
    <attr name="textAllCaps" format="reference|boolean" />
    <attr name="textAppearanceBody1" format="reference" />
    <attr name="textAppearanceBody2" format="reference" />
    <attr name="textAppearanceButton" format="reference" />
    <attr name="textAppearanceCaption" format="reference" />
    <attr name="textAppearanceHeadline1" format="reference" />
    <attr name="textAppearanceHeadline2" format="reference" />
    <attr name="textAppearanceHeadline3" format="reference" />
    <attr name="textAppearanceHeadline4" format="reference" />
    <attr name="textAppearanceHeadline5" format="reference" />
    <attr name="textAppearanceHeadline6" format="reference" />
    <attr name="textAppearanceLargePopupMenu" format="reference" />
    <attr name="textAppearanceLineHeightEnabled" format="boolean" />
    <attr name="textAppearanceListItem" format="reference" />
    <attr name="textAppearanceListItemSecondary" format="reference" />
    <attr name="textAppearanceListItemSmall" format="reference" />
    <attr name="textAppearanceOverline" format="reference" />
    <attr name="textAppearancePopupMenuHeader" format="reference" />
    <attr name="textAppearanceSearchResultSubtitle" format="reference" />
    <attr name="textAppearanceSearchResultTitle" format="reference" />
    <attr name="textAppearanceSmallPopupMenu" format="reference" />
    <attr name="textAppearanceSubtitle1" format="reference" />
    <attr name="textAppearanceSubtitle2" format="reference" />
    <attr name="textBackground" format="reference" />
    <attr name="textBackgroundPanX" format="float" />
    <attr name="textBackgroundPanY" format="float" />
    <attr name="textBackgroundRotate" format="float" />
    <attr name="textBackgroundZoom" format="float" />
    <attr name="textColorAlertDialogListItem" format="reference|color" />
    <attr name="textColorSearchUrl" format="reference|color" />
    <attr name="textEndPadding" format="dimension" />
    <attr name="textFillColor" format="color" />
    <attr name="textInputStyle" format="reference" />
    <attr name="textLocale" format="string" />
    <attr name="textOutlineColor" format="color" />
    <attr name="textOutlineThickness" format="dimension" />
    <attr name="textPanX" format="float" />
    <attr name="textPanY" format="float" />
    <attr name="textSize" format="dimension" />
    <attr name="textStartPadding" format="dimension" />
    <attr name="textureBlurFactor" format="integer" />
    <attr name="textureEffect">
        <enum name="none" value="0" />
        <enum name="frost" value="1" />
    </attr>
    <attr name="textureHeight" format="dimension" />
    <attr name="textureWidth" format="dimension" />
    <attr name="theme" format="reference" />
    <attr name="themeLineHeight" format="dimension" />
    <attr name="thickness" format="dimension" />
    <attr name="thumbTextPadding" format="dimension" />
    <attr name="thumbTint" format="color" />
    <attr name="thumbTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="tickMark" format="reference" />
    <attr name="tickMarkTint" format="color" />
    <attr name="tickMarkTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="tint" format="color" />
    <attr name="tintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="title" format="string" />
    <attr name="titleEnabled" format="boolean" />
    <attr name="titleMargin" format="dimension" />
    <attr name="titleMarginBottom" format="dimension" />
    <attr name="titleMarginEnd" format="dimension" />
    <attr name="titleMarginStart" format="dimension" />
    <attr name="titleMarginTop" format="dimension" />
    <attr name="titleMargins" format="dimension" />
    <attr name="titleTextAppearance" format="reference" />
    <attr name="titleTextColor" format="color" />
    <attr name="titleTextStyle" format="reference" />
    <attr name="toolbarId" format="reference" />
    <attr name="toolbarNavigationButtonStyle" format="reference" />
    <attr name="toolbarStyle" format="reference" />
    <attr name="tooltipForegroundColor" format="reference|color" />
    <attr name="tooltipFrameBackground" format="reference" />
    <attr name="tooltipText" format="string" />
    <attr name="touchAnchorId" format="reference" />
    <attr name="touchAnchorSide">
        <enum name="top" value="0" />
        <enum name="left" value="1" />
        <enum name="right" value="2" />
        <enum name="bottom" value="3" />
        <enum name="middle" value="4" />
        <enum name="start" value="5" />
        <enum name="end" value="6" />
    </attr>
    <attr name="touchRegionId" format="reference" />
    <attr name="track" format="reference" />
    <attr name="trackTint" format="color" />
    <attr name="trackTintMode">
        <enum name="src_over" value="3" />
        <enum name="src_in" value="5" />
        <enum name="src_atop" value="9" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="add" value="16" />
    </attr>
    <attr name="transformPivotTarget" format="reference" />
    <attr name="transitionDisable" format="boolean" />
    <attr name="transitionEasing" format="string">
        <enum name="standard" value="0" />
        <enum name="accelerate" value="1" />
        <enum name="decelerate" value="2" />
        <enum name="linear" value="3" />
    </attr>
    <attr name="transitionFlags">
        <flag name="none" value="0x00000000" />
        <flag name="beginOnFirstDraw" value="0x00000001" />
        <flag name="disableIntraAutoTransition" value="0x00000002" />
        <flag name="onInterceptTouchReturnSwipe" value="0x00000004" />
    </attr>
    <attr name="transitionPathRotate" format="float" />
    <attr name="triggerId" format="reference" />
    <attr name="triggerReceiver" format="reference" />
    <attr name="triggerSlack" format="float" />
    <attr name="ttcIndex" format="integer" />
    <attr name="upDuration" format="integer" />
    <attr name="updatesContinuously" format="boolean" />
    <attr name="useCompatPadding" format="boolean" />
    <attr name="useMaterialThemeColors" format="boolean" />
    <attr name="useSimpleSummaryProvider" format="boolean" />
    <attr name="viewInflaterClass" format="string" />
    <attr name="viewTransitionMode">
        <enum name="currentState" value="0" />
        <enum name="allStates" value="1" />
        <enum name="noState" value="2" />
    </attr>
    <attr name="viewTransitionOnCross" format="reference" />
    <attr name="viewTransitionOnNegativeCross" format="reference" />
    <attr name="viewTransitionOnPositiveCross" format="reference" />
    <attr name="visibilityMode">
        <enum name="normal" value="0" />
        <enum name="ignore" value="1" />
    </attr>
    <attr name="voiceIcon" format="reference" />
    <attr name="warmth" format="float" />
    <attr name="waveDecay" format="integer" />
    <attr name="waveOffset" format="float|dimension" />
    <attr name="wavePeriod" format="float" />
    <attr name="wavePhase" format="float" />
    <attr name="waveShape" format="string">
        <enum name="sin" value="0" />
        <enum name="square" value="1" />
        <enum name="triangle" value="2" />
        <enum name="sawtooth" value="3" />
        <enum name="reverseSawtooth" value="4" />
        <enum name="cos" value="5" />
        <enum name="bounce" value="6" />
    </attr>
    <attr name="waveVariesBy">
        <enum name="position" value="0" />
        <enum name="path" value="1" />
    </attr>
    <attr name="widgetLayout" format="reference" />
    <attr name="windowActionBar" format="boolean" />
    <attr name="windowActionBarOverlay" format="boolean" />
    <attr name="windowActionModeOverlay" format="boolean" />
    <attr name="windowFixedHeightMajor" format="dimension|fraction" />
    <attr name="windowFixedHeightMinor" format="dimension|fraction" />
    <attr name="windowFixedWidthMajor" format="dimension|fraction" />
    <attr name="windowFixedWidthMinor" format="dimension|fraction" />
    <attr name="windowMinWidthMajor" format="dimension|fraction" />
    <attr name="windowMinWidthMinor" format="dimension|fraction" />
    <attr name="windowNoTitle" format="boolean" />
    <attr name="yearSelectedStyle" format="reference" />
    <attr name="yearStyle" format="reference" />
    <attr name="yearTodayStyle" format="reference" />
</resources>
