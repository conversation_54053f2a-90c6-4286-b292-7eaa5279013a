<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout android:orientation="vertical" android:id="@id/container" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/margin_parent" android:layout_marginRight="@dimen/margin_parent" android:minHeight="@dimen/list_item_height" android:layout_marginHorizontal="@dimen/margin_parent">
            <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginEnd="@dimen/dp_10">
                <ImageView android:id="@id/iv" android:layout_width="@dimen/dp_24" android:layout_height="@dimen/dp_24" android:src="@mipmap/course" android:scaleType="fitXY" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
                <ImageView android:id="@id/imgTips" android:visibility="gone" android:layout_width="@dimen/dp_6" android:layout_height="@dimen/dp_6" android:src="@drawable/shape_red_round_bg" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
            <TextView android:id="@id/nameTv" android:layout_width="fill_parent" android:layout_height="wrap_content" style="@style/middle_black_text_style" />
        </LinearLayout>
        <View android:paddingEnd="@dimen/dp_15" style="@style/thin_dividing_line_style" />
    </LinearLayout>
</LinearLayout>
