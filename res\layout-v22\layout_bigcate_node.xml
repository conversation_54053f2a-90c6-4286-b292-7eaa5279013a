<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:orientation="vertical" android:id="@id/container" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <LinearLayout android:gravity="center_vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/margin_parent" android:layout_marginRight="@dimen/margin_parent" android:minHeight="@dimen/list_item_height" android:layout_marginHorizontal="@dimen/margin_parent">
            <ImageView android:id="@id/iv" android:layout_width="@dimen/dp_24" android:layout_height="@dimen/dp_24" android:src="@mipmap/main_folder" android:scaleType="fitXY" android:layout_marginEnd="@dimen/dp_10" />
            <TextView android:id="@id/nameTv" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_weight="1.0" style="@style/middle_black_text_style" />
            <ImageView android:id="@id/arrowIv" android:layout_width="@dimen/arrow_folder_size" android:layout_height="@dimen/arrow_folder_size" android:src="@mipmap/retract" android:scaleType="fitXY" />
        </LinearLayout>
        <View android:layout_marginLeft="@dimen/margin_parent" style="@style/thin_dividing_line_style" />
    </LinearLayout>
</LinearLayout>
