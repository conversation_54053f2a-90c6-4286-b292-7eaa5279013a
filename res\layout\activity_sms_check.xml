<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:layout_gravity="center" android:background="@drawable/shape_white_round_bg" android:paddingLeft="@dimen/dp_20" android:paddingTop="@dimen/dp_20" android:paddingRight="@dimen/dp_20" android:paddingBottom="@dimen/dp_20" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/ly_title" android:layout_width="0.0dip" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <TextView android:textSize="@dimen/sp_16" android:textColor="@color/textColorBlack" android:gravity="center" android:id="@id/sms_title" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="@string/message_authentication" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <ImageView android:id="@id/iv_close" android:layout_width="@dimen/dp_30" android:layout_height="@dimen/dp_30" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" app:srcCompat="@drawable/icon_close" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <LinearLayout android:orientation="vertical" android:paddingTop="@dimen/dp_20" android:paddingBottom="@dimen/dp_20" android:layout_width="0.0dip" android:layout_height="wrap_content" app:layout_constraintBottom_toTopOf="@id/btnSmsVerify" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/ly_title">
        <EditText android:theme="@style/MyEditText" android:textSize="@dimen/sp_14" android:textColor="@color/textColorBlack" android:textColorHint="@color/textLightGray" android:id="@id/editTextPhone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:hint="@string/phone_number" android:ems="10" android:inputType="phone" />
        <TextView android:textSize="@dimen/sp_12" android:textColor="@color/colorMediumRed" android:id="@id/textPhoneError" android:paddingLeft="@dimen/dp_3" android:paddingRight="@dimen/dp_3" android:visibility="invisible" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/phone_number_error" />
        <LinearLayout android:gravity="center" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_10">
            <EditText android:theme="@style/MyEditText" android:textSize="@dimen/sp_14" android:textColor="@color/textColorBlack" android:textColorHint="@color/textLightGray" android:id="@id/editVerifyCode" android:layout_width="0.0dip" android:layout_height="fill_parent" android:hint="@string/input_verify_code" android:ems="10" android:layout_weight="1.0" android:inputType="numberSigned" />
            <TextView android:textSize="@dimen/sp_14" android:textColor="@color/colorThemBlue" android:gravity="center" android:id="@id/btnVerifyCode" android:background="@drawable/selector_btn_border_bg" android:paddingLeft="@dimen/dp_10" android:paddingTop="@dimen/dp_5" android:paddingRight="@dimen/dp_10" android:paddingBottom="@dimen/dp_5" android:focusable="true" android:clickable="true" android:layout_width="wrap_content" android:layout_height="fill_parent" android:layout_marginTop="10.0dip" android:layout_marginBottom="10.0dip" android:minWidth="@dimen/dp_100" android:text="@string/get_verify_code" android:layout_marginStart="@dimen/dp_4" />
        </LinearLayout>
        <TextView android:textSize="@dimen/sp_12" android:textColor="@color/colorMediumRed" android:id="@id/textVerifyError" android:paddingLeft="@dimen/dp_5" android:paddingRight="@dimen/dp_5" android:visibility="invisible" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/verification_code_error" />
    </LinearLayout>
    <TextView android:textSize="@dimen/sp_16" android:textColor="@color/white" android:gravity="center" android:id="@id/btnSmsVerify" android:background="@drawable/selector_btn_comme_bg" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_15" android:layout_marginBottom="@dimen/dp_15" android:minHeight="@dimen/dp_45" android:text="@string/login_check" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
