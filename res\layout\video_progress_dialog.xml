<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:gravity="center" android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <RelativeLayout android:gravity="center" android:layout_width="@dimen/dp_152" android:layout_height="@dimen/dp_152">
        <LinearLayout android:orientation="vertical" android:id="@id/content" android:background="@drawable/video_dialog_progress_bg" android:layout_width="@dimen/dp_152" android:layout_height="wrap_content">
            <ImageView android:layout_gravity="center_horizontal" android:id="@id/duration_image_tip" android:layout_width="@dimen/dp_36" android:layout_height="@dimen/dp_27" android:layout_marginTop="@dimen/dp_16" />
            <LinearLayout android:gravity="center_horizontal" android:layout_gravity="center_horizontal" android:orientation="horizontal" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_20">
                <TextView android:textSize="@dimen/sp_14" android:textColor="@color/blue" android:id="@id/tv_current" android:layout_width="wrap_content" android:layout_height="wrap_content" />
                <TextView android:textSize="@dimen/sp_14" android:textColor="#ffffffff" android:id="@id/tv_duration" android:layout_width="wrap_content" android:layout_height="wrap_content" />
            </LinearLayout>
            <ProgressBar android:layout_gravity="center_horizontal" android:id="@id/duration_progressbar" android:layout_width="fill_parent" android:layout_height="4.0dip" android:layout_marginLeft="@dimen/dp_16" android:layout_marginTop="@dimen/dp_8" android:layout_marginRight="@dimen/dp_16" android:layout_marginBottom="@dimen/dp_16" android:max="100" android:progressDrawable="@drawable/video_dialog_progress" style="@android:style/Widget.ProgressBar.Horizontal" />
        </LinearLayout>
    </RelativeLayout>
</RelativeLayout>
