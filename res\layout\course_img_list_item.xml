<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/dp_14" android:layout_marginTop="@dimen/dp_8" android:layout_marginRight="@dimen/dp_14" android:layout_marginBottom="@dimen/dp_8"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <FrameLayout android:layout_width="wrap_content" android:layout_height="wrap_content">
        <ImageView android:id="@id/courseImg" android:layout_width="@dimen/course_img_width" android:layout_height="@dimen/course_img_height" android:src="@mipmap/default_img" android:scaleType="fitXY" android:layout_marginEnd="@dimen/dp_10" />
        <TextView android:textSize="@dimen/sp_10" android:textColor="@color/white" android:id="@id/tvUpdate" android:background="@drawable/shape_red_corner_bg" android:paddingLeft="@dimen/dp_3" android:paddingRight="@dimen/dp_3" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/text_update" />
    </FrameLayout>
    <LinearLayout android:layout_gravity="center_vertical" android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <TextView android:ellipsize="end" android:id="@id/titleTv" android:layout_width="fill_parent" android:layout_height="wrap_content" android:lines="1" style="@style/middle_black_text_style" />
        <TextView android:ellipsize="end" android:id="@id/detailTv" android:layout_width="fill_parent" android:layout_height="wrap_content" android:lines="2" style="@style/small_light_grey_text_style" />
        <TextView android:ellipsize="end" android:id="@id/tvEndTime" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="wrap_content" android:lines="1" style="@style/small_light_grey_text_style" />
    </LinearLayout>
</LinearLayout>
