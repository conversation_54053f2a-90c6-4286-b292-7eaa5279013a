<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:clickable="true" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <RelativeLayout android:background="?android:selectableItemBackground" android:paddingLeft="@dimen/dp_15" android:paddingTop="@dimen/dp_10" android:paddingRight="@dimen/dp_15" android:paddingBottom="@dimen/dp_10" android:layout_width="fill_parent" android:layout_height="@dimen/dp_45" android:paddingHorizontal="@dimen/dp_15" android:paddingVertical="@dimen/dp_10">
        <ImageView android:id="@id/iv" android:layout_width="@dimen/account_img_size" android:layout_height="@dimen/account_img_size" android:src="@mipmap/account" android:scaleType="fitCenter" android:layout_centerVertical="true" />
        <TextView android:gravity="center_vertical" android:id="@id/stateIv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_centerVertical="true" android:layout_marginStart="@dimen/padding_2" android:layout_alignParentEnd="true" style="@style/middle_light_grey_text_style" />
        <TextView android:gravity="center_vertical" android:id="@id/nameTv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_centerVertical="true" android:layout_toStartOf="@id/stateIv" android:layout_toEndOf="@id/iv" style="@style/middle_black_text_style" />
    </RelativeLayout>
    <TextView android:gravity="center" android:id="@id/deleteTv" android:background="@color/red" android:layout_width="@dimen/dp_90" android:layout_height="fill_parent" android:text="@string/delete" android:paddingStart="@dimen/padding_16" android:paddingEnd="@dimen/padding_16" style="@style/middle_white_text_style" />
</LinearLayout>
