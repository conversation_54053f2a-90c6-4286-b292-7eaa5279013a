<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/rootView" android:layout_width="fill_parent" android:layout_height="?actionBarSize" android:paddingStart="@dimen/margin_parent" android:paddingEnd="@dimen/dp_6"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <TextView android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/list_course" android:layout_weight="1.0" style="@style/black_text_style" />
    <ImageView android:id="@id/ivSearch" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:visibility="gone" android:layout_width="@dimen/dp_40" android:layout_height="fill_parent" android:src="@mipmap/iv_search" android:layout_marginEnd="@dimen/dp_6" android:paddingHorizontal="@dimen/dp_10" />
    <ImageView android:id="@id/listIv" android:paddingLeft="@dimen/dp_5" android:paddingRight="@dimen/dp_5" android:layout_width="@dimen/dp_40" android:layout_height="fill_parent" android:src="@mipmap/list" android:layout_marginEnd="@dimen/dp_6" android:paddingHorizontal="@dimen/dp_5" />
    <ImageView android:id="@id/tileiV" android:paddingLeft="@dimen/dp_5" android:paddingRight="@dimen/dp_5" android:layout_width="@dimen/dp_40" android:layout_height="fill_parent" android:src="@mipmap/tile_active" android:paddingHorizontal="@dimen/dp_5" />
</LinearLayout>
