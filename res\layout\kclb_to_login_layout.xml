<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center" android:orientation="vertical" android:id="@id/add_user_line" android:background="@color/white" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <ImageView android:layout_width="@dimen/empty_image_size" android:layout_height="@dimen/empty_image_size" android:src="@mipmap/empty_1" android:scaleType="fitXY" />
    <TextView android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_margin="@dimen/margin" android:text="@string/add_account" style="@style/middle_light_grey_text_style" />
    <LinearLayout android:gravity="center" android:orientation="horizontal" android:id="@id/addUser" android:background="@drawable/adduser_selector" android:padding="@dimen/dp_6" android:layout_width="wrap_content" android:layout_height="@dimen/dp_50" android:layout_marginTop="@dimen/tjsqzh_margin_top">
        <ImageView android:id="@id/addImg" android:padding="@dimen/dp_2" android:layout_width="@dimen/add_image_size" android:layout_height="@dimen/add_image_size" android:layout_marginLeft="@dimen/tjsqzh_padding_right" android:src="@drawable/add_selector" android:scaleType="fitXY" />
        <TextView android:textColor="@drawable/button_font_style" android:paddingRight="@dimen/tjsqzh_padding_right" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/add_auth_account" style="@style/middle_theme_color_text_style" />
    </LinearLayout>
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/layoutEmptyLine" android:visibility="invisible" android:layout_width="fill_parent" android:layout_height="?actionBarSize" />
    <LinearLayout android:orientation="horizontal" android:visibility="invisible" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingStart="@dimen/dp_14" android:paddingEnd="@dimen/dp_6">
        <TextView android:layout_width="wrap_content" android:layout_height="wrap_content" style="@style/black_text_style" />
        <ImageView android:paddingLeft="@dimen/dp_6" android:paddingRight="@dimen/dp_6" android:layout_width="@dimen/tile_image_size" android:layout_height="@dimen/tile_image_size" android:src="@mipmap/list" />
    </LinearLayout>
</LinearLayout>
