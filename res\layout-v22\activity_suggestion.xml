<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:background="#fff8f8f8" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <include layout="@layout/common_title_layout" />
    <ScrollView android:layout_width="fill_parent" android:layout_height="fill_parent">
        <LinearLayout android:orientation="vertical" android:paddingLeft="@dimen/dp_15" android:paddingRight="@dimen/dp_15" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingHorizontal="@dimen/dp_15">
            <EditText android:gravity="start|center|top" android:id="@id/contentEt" android:background="@drawable/shape_white_round_bg" android:paddingLeft="@dimen/dp_10" android:paddingTop="@dimen/dp_5" android:paddingRight="@dimen/dp_10" android:paddingBottom="@dimen/dp_5" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_10" android:layout_marginBottom="@dimen/dp_10" android:hint="@string/needMoreWords" android:lines="10" android:layout_marginVertical="@dimen/dp_10" android:paddingHorizontal="@dimen/dp_10" android:paddingVertical="@dimen/dp_5" style="@style/small_grey_text_style" />
            <EditText android:gravity="start|center" android:layout_gravity="center_vertical" android:id="@id/contactEt" android:background="@drawable/shape_white_round_bg" android:paddingLeft="@dimen/dp_10" android:paddingTop="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:paddingBottom="@dimen/dp_10" android:layout_width="fill_parent" android:layout_height="@dimen/dp_50" android:hint="@string/input_email" android:lines="1" android:paddingHorizontal="@dimen/dp_10" android:paddingVertical="@dimen/dp_10" style="@style/small_grey_text_style" />
            <TextView android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_10" android:text="@string/feedbackShows" style="@style/small_light_grey_text_style" />
            <TextView android:autoLink="email" android:paddingTop="@dimen/dp_10" android:paddingBottom="@dimen/dp_10" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/feedback_desc_1" android:paddingVertical="@dimen/dp_10" style="@style/small_light_grey_text_style" />
            <TextView android:paddingTop="@dimen/dp_10" android:paddingBottom="@dimen/dp_10" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/feedback_desc_2" android:paddingVertical="@dimen/dp_10" style="@style/small_light_grey_text_style" />
            <TextView android:paddingTop="@dimen/dp_10" android:paddingBottom="@dimen/dp_10" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/feedback_desc_3" android:paddingVertical="@dimen/dp_10" style="@style/small_light_grey_text_style" />
        </LinearLayout>
    </ScrollView>
</LinearLayout>
