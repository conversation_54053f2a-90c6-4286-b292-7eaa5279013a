<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:background="#ff000000" android:padding="@dimen/dp_15" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout android:orientation="vertical" android:id="@id/headLayout" android:paddingBottom="@dimen/dp_5" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <FrameLayout android:layout_gravity="end" android:padding="@dimen/dp_5" android:layout_width="wrap_content" android:layout_height="@dimen/dp_40">
            <ImageView android:id="@id/ivStClose" android:layout_width="fill_parent" android:layout_height="fill_parent" android:src="@drawable/icon_close" />
            <TextView android:textSize="@dimen/sp_14" android:textColor="@color/textColorBlack" android:gravity="center" android:layout_gravity="center" android:id="@id/tvStTime" android:background="@drawable/shape_white_20_bg" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:visibility="visible" android:clickable="true" android:layout_width="fill_parent" android:layout_height="fill_parent" android:minWidth="@dimen/dp_30" android:text="@string/text_jump" />
        </FrameLayout>
    </LinearLayout>
    <com.youth.banner.Banner android:id="@id/image_banner" android:layout_width="fill_parent" android:layout_height="fill_parent" app:banner_infinite_loop="false" />
</LinearLayout>
