<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:background="@android:color/black" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <FrameLayout android:id="@id/surface_container" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <FrameLayout android:id="@id/thumb" android:background="@color/transparent" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="fitCenter" android:layout_alignParentTop="true" android:layout_alignParentBottom="true" android:layout_alignParentStart="true" android:layout_alignParentEnd="true">
        <ImageView android:id="@id/thumbImage" android:layout_width="fill_parent" android:layout_height="fill_parent" android:src="@mipmap/player_default" android:scaleType="fitCenter" />
    </FrameLayout>
    <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/layout_bottom" android:background="@color/bottom_container_bg" android:layout_width="fill_parent" android:layout_height="@dimen/dp_40" android:layout_alignParentBottom="true">
        <View android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_weight="1.0" />
        <ImageView android:id="@id/fullscreen" android:layout_width="wrap_content" android:layout_height="fill_parent" android:layout_marginLeft="@dimen/dp_8" android:layout_marginRight="@dimen/dp_8" android:src="@drawable/video_switch_open" android:scaleType="center" android:layout_marginHorizontal="@dimen/dp_8" />
    </LinearLayout>
    <ImageView android:layout_gravity="center_vertical" android:id="@id/start" android:layout_width="@dimen/dp_60" android:layout_height="@dimen/dp_60" android:src="@mipmap/play_icon" android:scaleType="fitCenter" android:layout_centerHorizontal="true" android:layout_centerVertical="true" />
    <ImageView android:layout_gravity="center_vertical" android:id="@id/loading" android:visibility="invisible" android:layout_width="@dimen/dp_60" android:layout_height="@dimen/dp_60" android:scaleType="fitCenter" android:layout_centerHorizontal="true" android:layout_centerVertical="true" />
    <ImageView android:id="@id/small_close" android:paddingLeft="@dimen/dp_10" android:paddingTop="@dimen/dp_10" android:visibility="gone" android:layout_width="@dimen/dp_30" android:layout_height="@dimen/dp_30" android:src="@mipmap/video_small_close" android:scaleType="centerInside" />
    <TextView android:textSize="@dimen/sp_14" android:textColor="#ffffffff" android:id="@id/jump_ad" android:background="@drawable/video_jump_btn_bg" android:paddingLeft="@dimen/dp_10" android:paddingTop="@dimen/dp_4" android:paddingRight="@dimen/dp_10" android:paddingBottom="@dimen/dp_4" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_20" android:text="@string/jump_ad" android:layout_marginEnd="@dimen/dp_20" android:layout_alignParentEnd="true" android:paddingHorizontal="@dimen/dp_10" android:paddingVertical="@dimen/dp_4" />
    <TextView android:textSize="@dimen/sp_14" android:textColor="#ffffffff" android:gravity="center" android:id="@id/ad_time" android:background="@drawable/video_jump_btn_bg" android:paddingLeft="@dimen/dp_10" android:paddingTop="@dimen/dp_4" android:paddingRight="@dimen/dp_10" android:paddingBottom="@dimen/dp_4" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_20" android:minWidth="@dimen/dp_30" android:text="" android:layout_marginStart="@dimen/dp_20" android:layout_alignParentStart="true" android:paddingHorizontal="@dimen/dp_10" android:paddingVertical="@dimen/dp_4" />
</RelativeLayout>
