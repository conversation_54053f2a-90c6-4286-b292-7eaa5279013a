<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:orientation="vertical" android:paddingTop="@dimen/dp_10" android:paddingBottom="@dimen/dp_10" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <ImageView android:id="@id/item_img" android:layout_width="@dimen/dp_92" android:layout_height="@dimen/dp_52" android:src="@mipmap/default_img" android:scaleType="fitXY" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <ImageView android:id="@id/item_flag" android:background="@drawable/shape_gray_flag_bg" android:visibility="gone" android:layout_width="@dimen/dp_24" android:layout_height="@dimen/dp_16" app:layout_constraintStart_toStartOf="@id/item_img" app:layout_constraintTop_toTopOf="@id/item_img" />
    <TextView android:textSize="@dimen/sp_14" android:textColor="@color/textColorBlack" android:id="@id/item_title" android:layout_width="0.0dip" android:layout_height="wrap_content" android:maxLines="1" android:layout_marginStart="@dimen/dp_10" android:layout_marginEnd="@dimen/dp_15" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/item_img" app:layout_constraintTop_toTopOf="@id/item_img" />
    <TextView android:textSize="@dimen/sp_12" android:textColor="@color/textColorGray9" android:id="@id/item_sub_title" android:layout_width="0.0dip" android:layout_height="0.0dip" android:maxLines="2" app:layout_constraintBottom_toBottomOf="@id/item_img" app:layout_constraintEnd_toEndOf="@id/item_title" app:layout_constraintStart_toStartOf="@id/item_title" app:layout_constraintTop_toBottomOf="@id/item_title" />
</androidx.constraintlayout.widget.ConstraintLayout>
