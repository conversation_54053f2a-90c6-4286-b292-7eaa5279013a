<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:orientation="vertical" android:background="@drawable/shape_white_round_bg" android:layout_width="fill_parent" android:layout_height="fill_parent" android:minHeight="@dimen/dp_100"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView android:id="@id/tvQuestionTitle" android:paddingLeft="@dimen/dp_20" android:paddingTop="@dimen/dp_10" android:paddingRight="@dimen/dp_20" android:paddingBottom="@dimen/dp_10" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="" android:paddingHorizontal="@dimen/dp_20" android:paddingVertical="@dimen/dp_10" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/middle_black_text_style" />
    <LinearLayout android:orientation="vertical" android:id="@id/layoutMultipleChoice" android:paddingLeft="@dimen/dp_20" android:paddingRight="@dimen/dp_20" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/dp_3" android:layout_marginRight="@dimen/dp_3" android:maxHeight="@dimen/dp_360" android:layout_marginHorizontal="@dimen/dp_3" android:paddingHorizontal="@dimen/dp_20" app:layout_constraintBottom_toTopOf="@id/q_layout_bottom" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvQuestionTitle">
        <ScrollView android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
            <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
                <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
                    <TextView android:id="@id/choiceItemA" android:paddingTop="@dimen/dp_4" android:paddingBottom="@dimen/dp_4" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="" android:paddingVertical="@dimen/dp_4" style="@style/middle_black_text_style" />
                    <TextView android:id="@id/choiceItemB" android:paddingTop="@dimen/dp_4" android:paddingBottom="@dimen/dp_4" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="" android:paddingVertical="@dimen/dp_4" style="@style/middle_black_text_style" />
                    <TextView android:id="@id/choiceItemC" android:paddingTop="@dimen/dp_4" android:paddingBottom="@dimen/dp_4" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="" android:paddingVertical="@dimen/dp_4" style="@style/middle_black_text_style" />
                    <TextView android:id="@id/choiceItemD" android:paddingTop="@dimen/dp_4" android:paddingBottom="@dimen/dp_4" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="" android:paddingVertical="@dimen/dp_4" style="@style/middle_black_text_style" />
                </LinearLayout>
                <RadioGroup android:gravity="center" android:orientation="horizontal" android:id="@id/radioGroup" android:layout_width="fill_parent" android:layout_height="@dimen/dp_35" android:layout_marginTop="@dimen/dp_10" android:layout_marginBottom="@dimen/dp_10" android:layout_marginVertical="@dimen/dp_10" app:layout_constraintBottom_toTopOf="@id/q_layout_bottom" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/frameLayout3">
                    <RadioButton android:id="@id/radioButtonA" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:text="A" android:layout_weight="1.0" android:layout_marginEnd="@dimen/dp_15" android:paddingHorizontal="@dimen/dp_10" style="@style/style_radio_button" />
                    <RadioButton android:id="@id/radioButtonB" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:layout_marginLeft="@dimen/dp_15" android:layout_marginRight="@dimen/dp_15" android:text="B" android:layout_weight="1.0" android:layout_marginHorizontal="@dimen/dp_15" android:paddingHorizontal="@dimen/dp_10" style="@style/style_radio_button" />
                    <RadioButton android:id="@id/radioButtonC" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:layout_marginLeft="@dimen/dp_15" android:layout_marginRight="@dimen/dp_15" android:text="C" android:layout_weight="1.0" android:layout_marginHorizontal="@dimen/dp_15" android:paddingHorizontal="@dimen/dp_10" style="@style/style_radio_button" />
                    <RadioButton android:id="@id/radioButtonD" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:text="D" android:layout_weight="1.0" android:layout_marginStart="@dimen/dp_15" android:paddingHorizontal="@dimen/dp_10" style="@style/style_radio_button" />
                </RadioGroup>
            </LinearLayout>
        </ScrollView>
    </LinearLayout>
    <EditText android:textSize="@dimen/sp_14" android:gravity="start|center|top" android:id="@id/editAnswer" android:background="@color/colorGrayF3" android:padding="@dimen/dp_5" android:visibility="gone" android:scrollbars="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/dp_20" android:layout_marginRight="@dimen/dp_20" android:minHeight="@dimen/dp_80" android:maxLines="5" android:minLines="3" android:inputType="textMultiLine" android:layout_marginHorizontal="@dimen/dp_20" app:layout_constraintBottom_toTopOf="@id/q_layout_bottom" app:layout_constraintTop_toBottomOf="@id/tvQuestionTitle" />
    <LinearLayout android:orientation="vertical" android:id="@id/q_layout_bottom" android:paddingTop="@dimen/dp_10" android:layout_width="fill_parent" android:layout_height="@dimen/dp_55" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent">
        <LinearLayout android:background="@color/colorGray97" android:layout_width="fill_parent" android:layout_height="1.0px" />
        <TextView android:textSize="@dimen/sp_16" android:textColor="@drawable/selector_btn_text_color" android:layout_gravity="center" android:id="@id/btnSubmit" android:paddingLeft="@dimen/dp_30" android:paddingRight="@dimen/dp_30" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginTop="@dimen/dp_5" android:layout_marginBottom="@dimen/dp_5" android:minWidth="@dimen/dp_100" android:text="@string/text_sure" android:layout_marginVertical="@dimen/dp_5" android:paddingHorizontal="@dimen/dp_30" style="@style/middle_theme_color_text_style" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
