<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="end|center" android:orientation="vertical" android:id="@id/app_video_brightness_box" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:gravity="center" android:orientation="vertical" android:id="@id/content" android:background="@drawable/video_dialog_progress_bg" android:paddingTop="@dimen/dp_8" android:paddingBottom="@dimen/dp_8" android:layout_width="@dimen/dp_100" android:layout_height="wrap_content" android:paddingVertical="@dimen/dp_8">
        <ImageView android:id="@id/app_video_brightness_icon" android:layout_width="@dimen/dp_40" android:layout_height="@dimen/dp_40" android:src="@drawable/light_100" />
        <TextView android:textSize="@dimen/sp_16" android:textColor="@android:color/white" android:gravity="center" android:id="@id/app_video_brightness" android:paddingTop="@dimen/dp_8" android:layout_width="@dimen/dp_70" android:layout_height="wrap_content" android:text="50%" android:layout_below="@id/app_video_brightness_icon" />
    </LinearLayout>
</LinearLayout>
