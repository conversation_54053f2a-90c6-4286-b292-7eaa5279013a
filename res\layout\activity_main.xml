<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:background="@color/white" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout android:id="@id/layoutList" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <LinearLayout android:orientation="vertical" android:id="@id/list_line" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="fill_parent">
            <include android:id="@id/titleBar" layout="@layout/common_title_layout" />
            <include android:id="@id/select_title_layout" layout="@layout/kclb_select_title_layout" />
            <FrameLayout android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_weight="1.0">
                <include android:id="@id/login_layout" android:visibility="gone" layout="@layout/kclb_to_login_layout" />
                <com.example.myapplication.ui.view.NoScrollViewPager android:id="@id/courseListPager" android:layout_width="fill_parent" android:layout_height="fill_parent" />
            </FrameLayout>
        </LinearLayout>
        <LinearLayout android:gravity="center" android:orientation="vertical" android:id="@id/loading_line" android:paddingLeft="@dimen/dp_15" android:paddingRight="@dimen/dp_15" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent">
            <ImageView android:id="@id/img_loading" android:layout_width="@dimen/reload_img_size" android:layout_height="@dimen/reload_img_size" android:scaleType="fitXY" />
            <TextView android:id="@id/tv_loading" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/loading_text" style="@style/theme_color_text_style" />
        </LinearLayout>
        <LinearLayout android:gravity="center" android:orientation="vertical" android:id="@id/empty_view" android:background="@color/white" android:paddingLeft="@dimen/dp_15" android:paddingRight="@dimen/dp_15" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
            <LinearLayout android:gravity="center" android:orientation="vertical" android:id="@id/net_error" android:paddingTop="@dimen/dp_5" android:paddingBottom="@dimen/dp_5" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content">
                <ImageView android:id="@id/img_empty" android:paddingTop="@dimen/dp_5" android:paddingBottom="@dimen/dp_5" android:layout_width="@dimen/dp_60" android:layout_height="@dimen/dp_60" android:src="@drawable/icon_net_loss" />
                <TextView android:textSize="@dimen/sp_16" android:textColor="@color/textColorBlack" android:id="@id/tv_helplees" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/network_helpless" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="@id/swiperefreshlayout" app:layout_constraintTop_toTopOf="@id/swiperefreshlayout" />
            </LinearLayout>
            <TextView android:textSize="@dimen/sp_14" android:textColor="@color/textColorGray" android:gravity="center_horizontal" android:id="@id/tv_error" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="@id/swiperefreshlayout" app:layout_constraintTop_toTopOf="@id/swiperefreshlayout" />
            <TextView android:textSize="@dimen/sp_14" android:textColor="@color/primaryColor" android:id="@id/tv_refresh" android:paddingLeft="@dimen/dp_15" android:paddingTop="@dimen/dp_10" android:paddingRight="@dimen/dp_15" android:paddingBottom="@dimen/dp_10" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/click_re_try" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="@id/swiperefreshlayout" app:layout_constraintTop_toTopOf="@id/swiperefreshlayout" />
        </LinearLayout>
    </FrameLayout>
</LinearLayout>
