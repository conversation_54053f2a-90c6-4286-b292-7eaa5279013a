<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:fitsSystemWindows="true" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/app_bar" android:background="@color/primaryColor" android:layout_width="fill_parent" android:layout_height="?actionBarSize" app:layout_constraintTop_toTopOf="parent">
        <LinearLayout android:gravity="center" android:id="@id/left_img" android:layout_width="wrap_content" android:layout_height="fill_parent" android:paddingStart="@dimen/dp_10" android:paddingEnd="@dimen/dp_15" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
            <ImageView android:layout_gravity="center_vertical" android:id="@id/backIV" android:layout_width="@dimen/dp_50" android:layout_height="@dimen/dp_35" android:src="@drawable/menu_back_bg" android:paddingStart="@dimen/dp_15" android:paddingEnd="@dimen/dp_10" />
        </LinearLayout>
        <TextView android:gravity="center" android:id="@id/tv_title" android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/title_white_text_style" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <FrameLayout android:id="@id/ll_view" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/app_bar" />
</androidx.constraintlayout.widget.ConstraintLayout>
