<?xml version="1.0" encoding="utf-8"?>
<merge android:layout_width="wrap_content" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:layout_gravity="start" android:orientation="vertical" android:id="@id/mtrl_picker_header" android:paddingLeft="@dimen/mtrl_calendar_header_content_padding" android:paddingRight="@dimen/mtrl_calendar_header_content_padding" android:paddingBottom="@dimen/mtrl_calendar_header_content_padding" android:layout_width="wrap_content" android:layout_height="fill_parent" android:paddingStart="@dimen/mtrl_calendar_header_content_padding" android:paddingEnd="@dimen/mtrl_calendar_header_content_padding" style="?materialCalendarHeaderLayout">
        <FrameLayout android:id="@id/mtrl_picker_header_title_and_selection" android:paddingLeft="@dimen/mtrl_calendar_header_text_padding" android:layout_width="@dimen/mtrl_calendar_landscape_header_width" android:layout_height="0.0dip" android:layout_weight="1.0" android:paddingStart="@dimen/mtrl_calendar_header_text_padding">
            <include layout="@layout/mtrl_picker_header_title_text" />
            <include layout="@layout/mtrl_picker_header_selection_text" />
        </FrameLayout>
        <include layout="@layout/mtrl_picker_header_toggle" />
    </LinearLayout>
    <View android:layout_gravity="end" android:layout_width="@dimen/mtrl_calendar_header_divider_thickness" android:layout_height="fill_parent" style="?materialCalendarHeaderDivider" />
</merge>
