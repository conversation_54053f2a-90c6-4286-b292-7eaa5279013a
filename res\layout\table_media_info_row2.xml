<?xml version="1.0" encoding="utf-8"?>
<TableRow android:paddingLeft="@dimen/dp_16" android:paddingTop="0.0dip" android:paddingRight="@dimen/dp_16" android:paddingBottom="0.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <TextView android:textSize="@dimen/sp_12" android:id="@id/name" android:layout_width="wrap_content" android:layout_height="wrap_content" />
    <TextView android:textSize="@dimen/sp_12" android:id="@id/value" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:layout_width="fill_parent" android:layout_height="wrap_content" />
</TableRow>
