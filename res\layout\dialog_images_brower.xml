<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center_horizontal" android:orientation="vertical" android:paddingTop="@dimen/padding_5" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <TextView android:id="@id/tv_image_index" android:layout_width="wrap_content" android:layout_height="wrap_content" />
    <com.example.myapplication.ui.view.showimages.ShowImagesViewPager android:id="@id/vp_images" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_weight="1.0" />
</LinearLayout>
