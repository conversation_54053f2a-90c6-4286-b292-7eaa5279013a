<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <include android:id="@id/include" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" layout="@layout/common_title_layout" />
    <ScrollView android:id="@id/scrollView2" android:background="@color/white" android:layout_width="fill_parent" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/include">
        <LinearLayout android:orientation="vertical" android:paddingLeft="@dimen/dp_3" android:paddingRight="@dimen/dp_3" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingHorizontal="@dimen/dp_3">
            <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:layout_width="fill_parent" android:layout_height="@dimen/dp_45" android:paddingHorizontal="@dimen/dp_10">
                <TextView android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/auth_account" android:layout_weight="1.0" style="@style/middle_black_text_style" />
                <ImageView android:id="@id/add_button" android:focusable="true" android:layout_width="@dimen/add_button_size" android:layout_height="@dimen/add_button_size" android:src="@mipmap/add" android:scaleType="fitXY" />
            </LinearLayout>
            <View style="@style/thin_dividing_line_style" />
            <com.example.myapplication.ui.view.SlideRecyclerView android:id="@id/container" android:layout_width="fill_parent" android:layout_height="wrap_content" app:measureHeight="true" />
            <View style="@style/dividing_line_style" />
            <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/downloadLine" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:focusable="true" android:clickable="true" android:layout_width="fill_parent" android:layout_height="@dimen/dp_50" android:paddingHorizontal="@dimen/dp_10">
                <TextView android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/open_download_back" android:layout_weight="1.0" style="@style/middle_black_text_style" />
                <androidx.appcompat.widget.SwitchCompat android:id="@id/toggleSwitch" android:layout_width="wrap_content" android:layout_height="wrap_content" />
            </LinearLayout>
            <View style="@style/thin_dividing_line_style" />
            <androidx.constraintlayout.widget.ConstraintLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/back_buffer" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:focusable="true" android:visibility="gone" android:clickable="true" android:layout_width="fill_parent" android:layout_height="@dimen/dp_50" android:paddingHorizontal="@dimen/dp_10">
                <TextView android:id="@id/textView2" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/back_buffer" android:layout_weight="1.0" app:layout_constraintBottom_toBottomOf="@id/buffer_switch" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/buffer_switch" style="@style/middle_black_text_style" />
                <androidx.appcompat.widget.SwitchCompat android:id="@id/buffer_switch" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
                <TextView android:textSize="@dimen/dp_10" android:textColor="@color/textColorGray9" android:id="@id/textView3" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/evs_back_buffer" android:layout_weight="1.0" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/textView2" style="@style/middle_black_text_style" />
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/backPlay" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:focusable="true" android:visibility="gone" android:clickable="true" android:layout_width="fill_parent" android:layout_height="@dimen/dp_50" android:paddingHorizontal="@dimen/dp_10">
                <TextView android:id="@id/tvBackPlay" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/text_back_play" android:layout_weight="1.0" app:layout_constraintBottom_toBottomOf="@id/backPlaySwitch" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/backPlaySwitch" style="@style/middle_black_text_style" />
                <androidx.appcompat.widget.SwitchCompat android:id="@id/backPlaySwitch" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
                <TextView android:textSize="@dimen/dp_10" android:textColor="@color/textColorGray9" android:id="@id/tvBackPlayDesc" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_weight="1.0" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/tvBackPlay" style="@style/middle_black_text_style" />
            </androidx.constraintlayout.widget.ConstraintLayout>
            <androidx.constraintlayout.widget.ConstraintLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/layoutScale" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:focusable="true" android:visibility="visible" android:clickable="true" android:layout_width="fill_parent" android:layout_height="@dimen/dp_50" android:paddingHorizontal="@dimen/dp_10">
                <TextView android:id="@id/textScale" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/scale_gestrue" android:layout_weight="1.0" app:layout_constraintBottom_toBottomOf="@id/switchScale" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/switchScale" style="@style/middle_black_text_style" />
                <androidx.appcompat.widget.SwitchCompat android:id="@id/switchScale" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
                <TextView android:textSize="@dimen/dp_10" android:textColor="@color/textColorGray9" android:id="@id/textScaleDesc" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/scale_gestrue_desc" android:layout_weight="1.0" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/textScale" style="@style/middle_black_text_style" />
            </androidx.constraintlayout.widget.ConstraintLayout>
            <View style="@style/thin_dividing_line_style" />
            <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/layoutLanguage" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:focusable="true" android:visibility="gone" android:clickable="true" android:layout_width="fill_parent" android:layout_height="@dimen/dp_50" android:paddingHorizontal="@dimen/dp_10">
                <TextView android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/language" android:layout_weight="1.0" style="@style/middle_black_text_style" />
                <TextView android:id="@id/tvLanguage" android:layout_width="wrap_content" android:layout_height="wrap_content" style="@style/middle_black_text_style" />
                <ImageView android:layout_width="@dimen/add_button_size" android:layout_height="@dimen/add_button_size" android:src="@mipmap/retract" android:scaleType="fitXY" />
            </LinearLayout>
            <View style="@style/thin_dividing_line_style" />
            <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/share_log" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:focusable="true" android:visibility="gone" android:clickable="true" android:layout_width="fill_parent" android:layout_height="@dimen/dp_45" android:paddingHorizontal="@dimen/dp_10">
                <TextView android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/share_log" android:layout_weight="1.0" style="@style/middle_black_text_style" />
                <ImageView android:layout_width="@dimen/add_button_size" android:layout_height="@dimen/add_button_size" android:src="@mipmap/retract" android:scaleType="fitXY" />
            </LinearLayout>
            <View android:visibility="gone" style="@style/thin_dividing_line_style" />
            <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/speedSelect" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:focusable="true" android:clickable="true" android:layout_width="fill_parent" android:layout_height="@dimen/dp_50" android:paddingHorizontal="@dimen/dp_10">
                <TextView android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/text_speed" android:layout_weight="1.0" style="@style/middle_black_text_style" />
                <TextView android:id="@id/speedModel" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/speed_normal" style="@style/middle_black_text_style" />
                <ImageView android:layout_width="@dimen/add_button_size" android:layout_height="@dimen/add_button_size" android:src="@mipmap/retract" android:scaleType="fitXY" />
            </LinearLayout>
            <View style="@style/thin_dividing_line_style" />
            <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/speedTouch" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:focusable="true" android:clickable="true" android:layout_width="fill_parent" android:layout_height="@dimen/dp_50" android:paddingHorizontal="@dimen/dp_10">
                <TextView android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/text_speed_long" android:layout_weight="1.0" style="@style/middle_black_text_style" />
                <TextView android:id="@id/speedTouchModel" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="1.0x" style="@style/middle_black_text_style" />
                <ImageView android:layout_width="@dimen/add_button_size" android:layout_height="@dimen/add_button_size" android:src="@mipmap/retract" android:scaleType="fitXY" />
            </LinearLayout>
            <View style="@style/thin_dividing_line_style" />
            <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/llCacheSet" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:clickable="true" android:layout_width="fill_parent" android:layout_height="@dimen/dp_50" android:paddingHorizontal="@dimen/dp_10">
                <TextView android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/text_cache_set" android:layout_weight="1.0" style="@style/middle_black_text_style" />
                <TextView android:id="@id/cacheSet" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="" style="@style/middle_black_text_style" />
                <ImageView android:layout_width="@dimen/add_button_size" android:layout_height="@dimen/add_button_size" android:src="@mipmap/retract" android:scaleType="fitXY" />
            </LinearLayout>
            <View style="@style/thin_dividing_line_style" />
            <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/clearCache" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:clickable="true" android:layout_width="fill_parent" android:layout_height="@dimen/dp_50" android:paddingHorizontal="@dimen/dp_10">
                <TextView android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/clea_cache" android:layout_weight="1.0" style="@style/middle_black_text_style" />
                <TextView android:id="@id/cacheUsed" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/loading_count" style="@style/middle_black_text_style" />
                <ImageView android:layout_width="@dimen/add_button_size" android:layout_height="@dimen/add_button_size" android:src="@mipmap/retract" android:scaleType="fitXY" />
            </LinearLayout>
            <View style="@style/thin_dividing_line_style" />
            <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/privacy_security" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:focusable="true" android:clickable="true" android:layout_width="fill_parent" android:layout_height="@dimen/dp_50" android:paddingHorizontal="@dimen/dp_10">
                <TextView android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/privacy_and_security" android:layout_weight="1.0" style="@style/middle_black_text_style" />
                <ImageView android:layout_width="@dimen/add_button_size" android:layout_height="@dimen/add_button_size" android:src="@mipmap/retract" android:scaleType="fitXY" />
            </LinearLayout>
            <View style="@style/thin_dividing_line_style" />
            <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/yjfkyjb" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:focusable="true" android:clickable="true" android:layout_width="fill_parent" android:layout_height="@dimen/dp_50" android:paddingHorizontal="@dimen/dp_10">
                <TextView android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/feed_back" android:layout_weight="1.0" style="@style/middle_black_text_style" />
                <ImageView android:layout_width="@dimen/add_button_size" android:layout_height="@dimen/add_button_size" android:src="@mipmap/retract" android:scaleType="fitXY" />
            </LinearLayout>
            <View style="@style/thin_dividing_line_style" />
            <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/about_us" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:focusable="true" android:clickable="true" android:layout_width="fill_parent" android:layout_height="@dimen/dp_45" android:paddingHorizontal="@dimen/dp_10">
                <TextView android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/about_us" android:layout_weight="1.0" style="@style/middle_black_text_style" />
                <ImageView android:layout_width="@dimen/add_button_size" android:layout_height="@dimen/add_button_size" android:src="@mipmap/retract" android:scaleType="fitXY" />
            </LinearLayout>
            <View style="@style/dividing_line_style" />
            <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/help_view" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:focusable="true" android:clickable="true" android:layout_width="fill_parent" android:layout_height="@dimen/dp_45" android:paddingHorizontal="@dimen/dp_10">
                <TextView android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/question_and_answer" android:layout_weight="1.0" style="@style/middle_black_text_style" />
                <ImageView android:layout_width="@dimen/add_button_size" android:layout_height="@dimen/add_button_size" android:src="@mipmap/retract" android:scaleType="fitXY" />
            </LinearLayout>
        </LinearLayout>
    </ScrollView>
    <ImageView android:id="@id/imgLoading" android:visibility="gone" android:layout_width="@dimen/dp_45" android:layout_height="wrap_content" android:src="@drawable/loading_circle" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
