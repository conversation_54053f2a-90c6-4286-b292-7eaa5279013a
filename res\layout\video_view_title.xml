<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/layout_top" android:background="@drawable/video_title_bg" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="@dimen/dp_48" app:layout_constraintTop_toTopOf="parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <ImageView android:id="@id/help_back" android:paddingTop="@dimen/dp_12" android:paddingBottom="@dimen/dp_12" android:layout_width="@dimen/dp_60" android:layout_height="@dimen/dp_48" android:src="@drawable/playerbar_back_icon" android:scaleType="fitCenter" />
    <TextView android:textSize="@dimen/sp_16" android:textColor="@android:color/white" android:ellipsize="end" android:id="@id/help_title" android:layout_width="0.0dip" android:layout_height="wrap_content" android:lines="1" android:layout_weight="1.0" />
    <ImageView android:id="@id/iv_more" android:paddingTop="@dimen/dp_12" android:paddingBottom="@dimen/dp_12" android:visibility="gone" android:layout_width="@dimen/dp_60" android:layout_height="@dimen/dp_48" android:src="@drawable/ic_video_more" android:scaleType="fitCenter" />
</LinearLayout>
