<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center" android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <ImageView android:id="@id/iv_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:scaleType="fitCenter" />
    <TextView android:textSize="@dimen/sp_12" android:textColor="@color/textColorGray9" android:id="@id/tv_content_desc" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="" />
    <TextView android:textSize="@dimen/sp_16" android:textColor="@color/textColorBlack" android:gravity="center" android:id="@id/tv_event_desc" android:layout_width="wrap_content" android:layout_height="@dimen/dp_45" android:layout_marginTop="@dimen/dp_15" android:text="" />
</LinearLayout>
