<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:background="@drawable/confirm_dialog_bg" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="5.0dip" android:layout_marginRight="5.0dip"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:orientation="vertical" android:background="@android:color/transparent" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <TextView android:textSize="@dimen/sp_16" android:textColor="#ff333333" android:gravity="center_horizontal" android:id="@id/title_name" android:paddingTop="15.0dip" android:paddingBottom="10.0dip" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="@string/tips" />
    </LinearLayout>
    <LinearLayout android:orientation="vertical" android:background="@android:color/transparent" android:paddingLeft="@dimen/dp_15" android:paddingRight="@dimen/dp_15" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <TextView android:textSize="@dimen/sp_14" android:textColor="#ff333333" android:id="@id/text_view" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="10.0dip" android:text="@string/msg_out_of_limit" />
        <CheckBox android:id="@id/checkRemind" android:paddingTop="@dimen/dp_5" android:paddingBottom="@dimen/dp_5" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/no_more_remind" />
        <View android:background="#ffeeeeee" android:layout_width="fill_parent" android:layout_height="1.0px" android:layout_marginTop="15.0dip" />
    </LinearLayout>
    <LinearLayout android:gravity="center_horizontal" android:orientation="horizontal" android:background="@android:color/transparent" android:layout_width="fill_parent" android:layout_height="45.0dip">
        <TextView android:textSize="@dimen/sp_14" android:textColor="#ff333333" android:gravity="center" android:id="@id/btn_cancel" android:paddingTop="@dimen/dp_5" android:paddingBottom="@dimen/dp_5" android:layout_width="wrap_content" android:layout_height="fill_parent" android:text="@string/text_cancel" android:layout_weight="1.0" />
        <TextView android:textSize="@dimen/sp_14" android:textColor="#ffffffff" android:gravity="center" android:id="@id/btn_ok" android:background="@color/primaryColor" android:paddingTop="@dimen/dp_5" android:paddingBottom="@dimen/dp_5" android:layout_width="wrap_content" android:layout_height="fill_parent" android:text="@string/text_sure" android:layout_weight="1.0" />
    </LinearLayout>
</LinearLayout>
