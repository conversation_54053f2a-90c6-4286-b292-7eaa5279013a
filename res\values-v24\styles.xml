<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored" parent="@android:style/TextAppearance.Material.Widget.Button.Borderless.Colored" />
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Colored" parent="@android:style/TextAppearance.Material.Widget.Button.Colored" />
    <style name="Base.V14.Theme.MaterialComponents" parent="@style/Base.V14.Theme.MaterialComponents.Bridge">
        <item name="android:colorBackground">@color/design_dark_default_color_background</item>
        <item name="android:timePickerDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog</item>
        <item name="android:datePickerDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog</item>
        <item name="android:contextPopupMenuStyle">@style/Widget.MaterialComponents.PopupMenu.ContextMenu</item>
        <item name="actionBarPopupTheme">@style/ThemeOverlay.MaterialComponents.Dark</item>
        <item name="actionBarSize">@dimen/mtrl_toolbar_default_height</item>
        <item name="actionBarStyle">@style/Widget.MaterialComponents.ActionBar.Surface</item>
        <item name="actionBarTheme">@style/ThemeOverlay.MaterialComponents.ActionBar.Surface</item>
        <item name="actionOverflowMenuStyle">@style/Widget.MaterialComponents.PopupMenu.Overflow</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
        <item name="appBarLayoutStyle">@style/Widget.MaterialComponents.AppBarLayout.Surface</item>
        <item name="badgeStyle">@style/Widget.MaterialComponents.Badge</item>
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
        <item name="bottomSheetDialogTheme">@style/ThemeOverlay.MaterialComponents.BottomSheetDialog</item>
        <item name="checkboxStyle">@style/Widget.MaterialComponents.CompoundButton.CheckBox</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="colorAccent">?colorSecondary</item>
        <item name="colorError">@color/design_dark_default_color_error</item>
        <item name="colorPrimary">@color/design_dark_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_dark_default_color_primary_dark</item>
        <item name="extendedFloatingActionButtonStyle">@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="listPopupWindowStyle">@style/Widget.MaterialComponents.PopupMenu.ListPopupWindow</item>
        <item name="materialAlertDialogTheme">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.Button.OutlinedButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialButtonToggleGroupStyle">@style/Widget.MaterialComponents.MaterialButtonToggleGroup</item>
        <item name="materialCalendarFullscreenTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen</item>
        <item name="materialCalendarStyle">@style/Widget.MaterialComponents.MaterialCalendar</item>
        <item name="materialCalendarTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="minTouchTargetSize">@dimen/mtrl_min_touch_target_size</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="popupMenuStyle">@style/Widget.MaterialComponents.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.MaterialComponents.CompoundButton.RadioButton</item>
        <item name="snackbarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Snackbar</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="switchStyle">@style/Widget.MaterialComponents.CompoundButton.Switch</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
        <item name="textAppearanceLargePopupMenu">?textAppearanceSubtitle1</item>
        <item name="textAppearanceSmallPopupMenu">?textAppearanceSubtitle1</item>
        <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">com.google.android.material.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Dialog" parent="@style/Base.V14.Theme.MaterialComponents.Dialog.Bridge">
        <item name="android:colorBackground">@color/design_dark_default_color_background</item>
        <item name="android:contextPopupMenuStyle">@style/Widget.MaterialComponents.PopupMenu.ContextMenu</item>
        <item name="actionBarPopupTheme">@style/ThemeOverlay.MaterialComponents.Dark</item>
        <item name="actionBarSize">@dimen/mtrl_toolbar_default_height</item>
        <item name="actionBarStyle">@style/Widget.MaterialComponents.ActionBar.Surface</item>
        <item name="actionBarTheme">@style/ThemeOverlay.MaterialComponents.ActionBar.Surface</item>
        <item name="actionOverflowMenuStyle">@style/Widget.MaterialComponents.PopupMenu.Overflow</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
        <item name="appBarLayoutStyle">@style/Widget.MaterialComponents.AppBarLayout.Surface</item>
        <item name="badgeStyle">@style/Widget.MaterialComponents.Badge</item>
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
        <item name="checkboxStyle">@style/Widget.MaterialComponents.CompoundButton.CheckBox</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="colorAccent">?colorSecondary</item>
        <item name="colorError">@color/design_dark_default_color_error</item>
        <item name="colorPrimary">@color/design_dark_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_dark_default_color_primary_dark</item>
        <item name="colorSecondary">@color/design_dark_default_color_secondary</item>
        <item name="extendedFloatingActionButtonStyle">@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="listPopupWindowStyle">@style/Widget.MaterialComponents.PopupMenu.ListPopupWindow</item>
        <item name="materialAlertDialogTheme">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.Button.OutlinedButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialButtonToggleGroupStyle">@style/Widget.MaterialComponents.MaterialButtonToggleGroup</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="minTouchTargetSize">@dimen/mtrl_min_touch_target_size</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="popupMenuStyle">@style/Widget.MaterialComponents.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.MaterialComponents.CompoundButton.RadioButton</item>
        <item name="snackbarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Snackbar</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="switchStyle">@style/Widget.MaterialComponents.CompoundButton.Switch</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
        <item name="textAppearanceLargePopupMenu">?textAppearanceSubtitle1</item>
        <item name="textAppearanceSmallPopupMenu">?textAppearanceSubtitle1</item>
        <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">com.google.android.material.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light" parent="@style/Base.V14.Theme.MaterialComponents.Light.Bridge">
        <item name="android:colorBackground">@color/design_default_color_background</item>
        <item name="android:timePickerDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog</item>
        <item name="android:datePickerDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog</item>
        <item name="android:contextPopupMenuStyle">@style/Widget.MaterialComponents.PopupMenu.ContextMenu</item>
        <item name="actionBarPopupTheme">@style/ThemeOverlay.MaterialComponents.Light</item>
        <item name="actionBarSize">@dimen/mtrl_toolbar_default_height</item>
        <item name="actionBarStyle">@style/Widget.MaterialComponents.Light.ActionBar.Solid</item>
        <item name="actionOverflowMenuStyle">@style/Widget.MaterialComponents.PopupMenu.Overflow</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
        <item name="appBarLayoutStyle">@style/Widget.MaterialComponents.AppBarLayout.Primary</item>
        <item name="badgeStyle">@style/Widget.MaterialComponents.Badge</item>
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
        <item name="bottomSheetDialogTheme">@style/ThemeOverlay.MaterialComponents.Light.BottomSheetDialog</item>
        <item name="checkboxStyle">@style/Widget.MaterialComponents.CompoundButton.CheckBox</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="colorAccent">?colorSecondary</item>
        <item name="colorError">@color/design_default_color_error</item>
        <item name="colorPrimary">@color/design_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
        <item name="extendedFloatingActionButtonStyle">@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="listPopupWindowStyle">@style/Widget.MaterialComponents.PopupMenu.ListPopupWindow</item>
        <item name="materialAlertDialogTheme">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.Button.OutlinedButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialButtonToggleGroupStyle">@style/Widget.MaterialComponents.MaterialButtonToggleGroup</item>
        <item name="materialCalendarFullscreenTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen</item>
        <item name="materialCalendarStyle">@style/Widget.MaterialComponents.MaterialCalendar</item>
        <item name="materialCalendarTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="minTouchTargetSize">@dimen/mtrl_min_touch_target_size</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="popupMenuStyle">@style/Widget.MaterialComponents.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.MaterialComponents.CompoundButton.RadioButton</item>
        <item name="snackbarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Snackbar</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="switchStyle">@style/Widget.MaterialComponents.CompoundButton.Switch</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
        <item name="textAppearanceLargePopupMenu">?textAppearanceSubtitle1</item>
        <item name="textAppearanceSmallPopupMenu">?textAppearanceSubtitle1</item>
        <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">com.google.android.material.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.Dialog" parent="@style/Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge">
        <item name="android:colorBackground">@color/design_default_color_background</item>
        <item name="android:contextPopupMenuStyle">@style/Widget.MaterialComponents.PopupMenu.ContextMenu</item>
        <item name="actionBarPopupTheme">@style/ThemeOverlay.MaterialComponents.Light</item>
        <item name="actionBarSize">@dimen/mtrl_toolbar_default_height</item>
        <item name="actionBarStyle">@style/Widget.MaterialComponents.Light.ActionBar.Solid</item>
        <item name="actionOverflowMenuStyle">@style/Widget.MaterialComponents.PopupMenu.Overflow</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
        <item name="appBarLayoutStyle">@style/Widget.MaterialComponents.AppBarLayout.Primary</item>
        <item name="badgeStyle">@style/Widget.MaterialComponents.Badge</item>
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
        <item name="checkboxStyle">@style/Widget.MaterialComponents.CompoundButton.CheckBox</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="colorAccent">?colorSecondary</item>
        <item name="colorError">@color/design_default_color_error</item>
        <item name="colorPrimary">@color/design_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
        <item name="colorSecondary">@color/design_default_color_secondary</item>
        <item name="extendedFloatingActionButtonStyle">@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="listPopupWindowStyle">@style/Widget.MaterialComponents.PopupMenu.ListPopupWindow</item>
        <item name="materialAlertDialogTheme">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.Button.OutlinedButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialButtonToggleGroupStyle">@style/Widget.MaterialComponents.MaterialButtonToggleGroup</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="minTouchTargetSize">@dimen/mtrl_min_touch_target_size</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="popupMenuStyle">@style/Widget.MaterialComponents.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.MaterialComponents.CompoundButton.RadioButton</item>
        <item name="snackbarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Snackbar</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="switchStyle">@style/Widget.MaterialComponents.CompoundButton.Switch</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
        <item name="textAppearanceLargePopupMenu">?textAppearanceSubtitle1</item>
        <item name="textAppearanceSmallPopupMenu">?textAppearanceSubtitle1</item>
        <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">com.google.android.material.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Info.Media" parent="@style/TextAppearance.Compat.Notification.Info" />
    <style name="TextAppearance.Compat.Notification.Media" parent="@style/TextAppearance.Compat.Notification" />
    <style name="TextAppearance.Compat.Notification.Time.Media" parent="@style/TextAppearance.Compat.Notification.Time" />
    <style name="TextAppearance.Compat.Notification.Title.Media" parent="@style/TextAppearance.Compat.Notification.Title" />
</resources>
