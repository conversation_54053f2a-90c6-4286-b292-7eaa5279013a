<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:background="@color/white" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:gravity="center_vertical" android:orientation="horizontal" android:background="@color/primaryColor" android:paddingTop="@dimen/dp_5" android:paddingBottom="@dimen/dp_5" android:layout_width="fill_parent" android:layout_height="?actionBarSize" android:paddingVertical="@dimen/dp_5">
        <ImageView android:layout_gravity="center_vertical" android:id="@id/ivBack" android:paddingTop="@dimen/dp_12" android:paddingBottom="@dimen/dp_12" android:layout_width="wrap_content" android:layout_height="fill_parent" android:src="@drawable/menu_back_bg" android:paddingStart="@dimen/dp_10" android:paddingEnd="@dimen/dp_6" android:paddingVertical="@dimen/dp_12" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <EditText android:textSize="@dimen/sp_12" android:id="@id/etName" android:background="@drawable/shape_white_20_bg" android:paddingLeft="@dimen/dp_15" android:paddingRight="@dimen/dp_15" android:layout_width="0.0dip" android:layout_height="@dimen/dp_32" android:layout_marginLeft="@dimen/dp_5" android:layout_marginRight="@dimen/dp_5" android:hint="@string/input_video_name" android:maxLines="1" android:layout_weight="1.0" android:imeOptions="actionNone" android:layout_marginHorizontal="@dimen/dp_5" android:paddingHorizontal="@dimen/dp_15" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/layoutSearch" app:layout_constraintStart_toEndOf="@id/ivBack" app:layout_constraintTop_toTopOf="parent" />
        <ImageView android:id="@id/ivClear" android:paddingLeft="@dimen/dp_6" android:paddingRight="@dimen/dp_6" android:layout_width="wrap_content" android:layout_height="0.0dip" android:src="@drawable/ic_close" android:layout_marginEnd="@dimen/dp_6" android:paddingHorizontal="@dimen/dp_6" app:layout_constraintBottom_toBottomOf="@id/etName" app:layout_constraintEnd_toEndOf="@id/etName" app:layout_constraintTop_toTopOf="@id/etName" app:layout_constraintVertical_bias="0.476" />
        <FrameLayout android:id="@id/layoutSearch" android:paddingLeft="@dimen/dp_5" android:paddingRight="@dimen/dp_5" android:layout_width="wrap_content" android:layout_height="fill_parent" android:paddingHorizontal="@dimen/dp_5" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent">
            <TextView android:textSize="@dimen/sp_12" android:textStyle="bold" android:textColor="@color/primaryColor" android:gravity="center" android:layout_gravity="center" android:id="@id/tvSearch" android:background="@drawable/shape_white_20_bg" android:paddingLeft="@dimen/dp_15" android:paddingRight="@dimen/dp_15" android:layout_width="wrap_content" android:layout_height="@dimen/dp_32" android:text="@string/text_search" android:paddingHorizontal="@dimen/dp_15" />
        </FrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/layoutBody" android:focusable="true" android:clickable="true" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <LinearLayout android:orientation="vertical" android:id="@id/layoutHistory" android:background="@color/white" android:layout_width="0.0dip" android:layout_height="wrap_content" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
            <androidx.constraintlayout.widget.ConstraintLayout android:orientation="horizontal" android:paddingLeft="@dimen/dp_15" android:paddingRight="@dimen/dp_15" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingHorizontal="@dimen/dp_15">
                <TextView android:textSize="@dimen/sp_12" android:textColor="@color/textColorGray" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/text_record" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
                <TextView android:textSize="@dimen/sp_12" android:textColor="@color/textColorGray" android:id="@id/tvHistoryClear" android:paddingLeft="@dimen/dp_10" android:paddingTop="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:paddingBottom="@dimen/dp_10" android:layout_width="wrap_content" android:layout_height="fill_parent" android:text="@string/text_clear" android:paddingHorizontal="@dimen/dp_10" android:paddingVertical="@dimen/dp_10" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
            <com.google.android.flexbox.FlexboxLayout android:id="@id/flexHistory" android:layout_width="fill_parent" android:layout_height="wrap_content" app:alignItems="center" app:flexDirection="row" app:flexWrap="wrap" app:justifyContent="flex_start" />
        </LinearLayout>
        <androidx.recyclerview.widget.RecyclerView android:id="@id/rvVideo" android:background="@color/white" android:layout_width="0.0dip" android:layout_height="wrap_content" android:overScrollMode="never" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>
