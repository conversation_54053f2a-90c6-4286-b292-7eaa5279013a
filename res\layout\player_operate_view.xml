<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:layout_width="wrap_content" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:gravity="center_vertical" android:orientation="vertical" android:id="@id/operation_view" android:background="@drawable/option_bg_shape" android:visibility="visible" android:layout_width="wrap_content" android:layout_height="wrap_content" android:minWidth="@dimen/dp_160" android:minHeight="@dimen/dp_100" android:layout_centerInParent="true">
        <ImageView android:layout_gravity="center" android:id="@id/operation_bg" android:layout_width="@dimen/dp_40" android:layout_height="@dimen/dp_40" android:src="@drawable/video_volumn_bg" android:scaleType="fitCenter" />
        <LinearLayout android:layout_gravity="center_horizontal" android:orientation="horizontal" android:id="@id/operation_tv" android:visibility="visible" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/dp_8" android:layout_marginRight="@dimen/dp_8">
            <TextView android:textSize="@dimen/sp_12" android:textColor="#ffffffff" android:gravity="end" android:id="@id/currentTime_Tv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:minWidth="@dimen/dp_35" android:text="00:00" />
            <TextView android:textSize="@dimen/sp_12" android:textColor="#ffffffff" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="/" />
            <TextView android:textSize="@dimen/sp_12" android:textColor="#ffffffff" android:gravity="start" android:id="@id/totalTime_Tv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:minWidth="@dimen/dp_35" android:text="00:00" />
        </LinearLayout>
        <SeekBar android:id="@id/operation_bar" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/dp_8" android:layout_marginTop="@dimen/dp_12" android:layout_marginRight="@dimen/dp_8" android:minHeight="@dimen/dp_4" android:thumb="@null" android:progressTint="#e6ffffff" android:progressBackgroundTint="#ff3c3c3c" />
    </LinearLayout>
</RelativeLayout>
