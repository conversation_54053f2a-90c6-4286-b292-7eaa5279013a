<?xml version="1.0" encoding="utf-8"?>
<resources>
    <item type="id" name="about_us" />
    <item type="id" name="accessibility_action_clickable_span" />
    <item type="id" name="accessibility_custom_action_0" />
    <item type="id" name="accessibility_custom_action_1" />
    <item type="id" name="accessibility_custom_action_10" />
    <item type="id" name="accessibility_custom_action_11" />
    <item type="id" name="accessibility_custom_action_12" />
    <item type="id" name="accessibility_custom_action_13" />
    <item type="id" name="accessibility_custom_action_14" />
    <item type="id" name="accessibility_custom_action_15" />
    <item type="id" name="accessibility_custom_action_16" />
    <item type="id" name="accessibility_custom_action_17" />
    <item type="id" name="accessibility_custom_action_18" />
    <item type="id" name="accessibility_custom_action_19" />
    <item type="id" name="accessibility_custom_action_2" />
    <item type="id" name="accessibility_custom_action_20" />
    <item type="id" name="accessibility_custom_action_21" />
    <item type="id" name="accessibility_custom_action_22" />
    <item type="id" name="accessibility_custom_action_23" />
    <item type="id" name="accessibility_custom_action_24" />
    <item type="id" name="accessibility_custom_action_25" />
    <item type="id" name="accessibility_custom_action_26" />
    <item type="id" name="accessibility_custom_action_27" />
    <item type="id" name="accessibility_custom_action_28" />
    <item type="id" name="accessibility_custom_action_29" />
    <item type="id" name="accessibility_custom_action_3" />
    <item type="id" name="accessibility_custom_action_30" />
    <item type="id" name="accessibility_custom_action_31" />
    <item type="id" name="accessibility_custom_action_4" />
    <item type="id" name="accessibility_custom_action_5" />
    <item type="id" name="accessibility_custom_action_6" />
    <item type="id" name="accessibility_custom_action_7" />
    <item type="id" name="accessibility_custom_action_8" />
    <item type="id" name="accessibility_custom_action_9" />
    <item type="id" name="action0" />
    <item type="id" name="action_bar" />
    <item type="id" name="action_bar_activity_content" />
    <item type="id" name="action_bar_container" />
    <item type="id" name="action_bar_root" />
    <item type="id" name="action_bar_spinner" />
    <item type="id" name="action_bar_subtitle" />
    <item type="id" name="action_bar_title" />
    <item type="id" name="action_commit" />
    <item type="id" name="action_container" />
    <item type="id" name="action_context_bar" />
    <item type="id" name="action_divider" />
    <item type="id" name="action_image" />
    <item type="id" name="action_menu_divider" />
    <item type="id" name="action_menu_presenter" />
    <item type="id" name="action_mode_bar" />
    <item type="id" name="action_mode_bar_stub" />
    <item type="id" name="action_mode_close_button" />
    <item type="id" name="action_settings" />
    <item type="id" name="action_text" />
    <item type="id" name="actions" />
    <item type="id" name="activity_chooser_view_content" />
    <item type="id" name="ad_time" />
    <item type="id" name="addImg" />
    <item type="id" name="addUser" />
    <item type="id" name="add_button" />
    <item type="id" name="add_user_line" />
    <item type="id" name="agreeline" />
    <item type="id" name="agreementTv" />
    <item type="id" name="alertTitle" />
    <item type="id" name="appVersion" />
    <item type="id" name="app_bar" />
    <item type="id" name="app_video_brightness" />
    <item type="id" name="app_video_brightness_box" />
    <item type="id" name="app_video_brightness_icon" />
    <item type="id" name="arrowIv" />
    <item type="id" name="back" />
    <item type="id" name="backIV" />
    <item type="id" name="backPlay" />
    <item type="id" name="backPlaySwitch" />
    <item type="id" name="backTv" />
    <item type="id" name="back_buffer" />
    <item type="id" name="back_tiny" />
    <item type="id" name="banner_data_key" />
    <item type="id" name="banner_pos_key" />
    <item type="id" name="bottom_progressbar" />
    <item type="id" name="btnSmsVerify" />
    <item type="id" name="btnSubmit" />
    <item type="id" name="btnVerifyCode" />
    <item type="id" name="btn_cancel" />
    <item type="id" name="btn_ok" />
    <item type="id" name="btn_shortcut" />
    <item type="id" name="buffer_switch" />
    <item type="id" name="buffering_container" />
    <item type="id" name="buffering_imageview" />
    <item type="id" name="buttonPanel" />
    <item type="id" name="cacheSet" />
    <item type="id" name="cacheUsed" />
    <item type="id" name="cancelTv" />
    <item type="id" name="cancel_action" />
    <item type="id" name="cancel_button" />
    <item type="id" name="center_container" />
    <item type="id" name="checkRemind" />
    <item type="id" name="checkVersion" />
    <item type="id" name="checkbox" />
    <item type="id" name="checked" />
    <item type="id" name="chip" />
    <item type="id" name="chip_group" />
    <item type="id" name="choiceItemA" />
    <item type="id" name="choiceItemB" />
    <item type="id" name="choiceItemC" />
    <item type="id" name="choiceItemD" />
    <item type="id" name="chronometer" />
    <item type="id" name="clearCache" />
    <item type="id" name="companyLine" />
    <item type="id" name="confirm_button" />
    <item type="id" name="contactEt" />
    <item type="id" name="container" />
    <item type="id" name="content" />
    <item type="id" name="contentEt" />
    <item type="id" name="contentPanel" />
    <item type="id" name="continueTv" />
    <item type="id" name="coordinator" />
    <item type="id" name="courseImg" />
    <item type="id" name="courseListPager" />
    <item type="id" name="courseRecycler" />
    <item type="id" name="courseRefresh" />
    <item type="id" name="cover" />
    <item type="id" name="coverDirectory" />
    <item type="id" name="crash_error_activity_close_button" />
    <item type="id" name="crash_error_activity_image" />
    <item type="id" name="crash_error_activity_more_info_button" />
    <item type="id" name="crash_error_activity_restart_button" />
    <item type="id" name="crash_error_locate_more_info_button" />
    <item type="id" name="crash_error_message" />
    <item type="id" name="current" />
    <item type="id" name="currentTime_Tv" />
    <item type="id" name="customPanel" />
    <item type="id" name="custom_full_id" />
    <item type="id" name="custom_small_id" />
    <item type="id" name="date_picker_actions" />
    <item type="id" name="decor_content_parent" />
    <item type="id" name="default_activity_button" />
    <item type="id" name="deleteIv" />
    <item type="id" name="deleteTv" />
    <item type="id" name="desc" />
    <item type="id" name="descriptionLine" />
    <item type="id" name="descriptionTv" />
    <item type="id" name="design_bottom_sheet" />
    <item type="id" name="design_menu_item_action_area" />
    <item type="id" name="design_menu_item_action_area_stub" />
    <item type="id" name="design_menu_item_text" />
    <item type="id" name="design_navigation_view" />
    <item type="id" name="detailTv" />
    <item type="id" name="detail_view" />
    <item type="id" name="dialog_button" />
    <item type="id" name="dirLayout" />
    <item type="id" name="directoryTv" />
    <item type="id" name="discribleTv" />
    <item type="id" name="do_play_view" />
    <item type="id" name="downloadLine" />
    <item type="id" name="durationTv" />
    <item type="id" name="duration_image_tip" />
    <item type="id" name="duration_progressbar" />
    <item type="id" name="editAnswer" />
    <item type="id" name="editTextPhone" />
    <item type="id" name="editVerifyCode" />
    <item type="id" name="edit_query" />
    <item type="id" name="empty_View" />
    <item type="id" name="empty_view" />
    <item type="id" name="end_padder" />
    <item type="id" name="error_container" />
    <item type="id" name="error_retry" />
    <item type="id" name="error_tips" />
    <item type="id" name="etName" />
    <item type="id" name="expand_activities_button" />
    <item type="id" name="expanded_menu" />
    <item type="id" name="fileTypeIv" />
    <item type="id" name="filenameTv" />
    <item type="id" name="filter_chip" />
    <item type="id" name="flFiller" />
    <item type="id" name="flLoopA" />
    <item type="id" name="flLoopB" />
    <item type="id" name="flexHistory" />
    <item type="id" name="fragment_container_view_tag" />
    <item type="id" name="frameLayout" />
    <item type="id" name="frameLayout2" />
    <item type="id" name="frameLayout3" />
    <item type="id" name="frameLeft" />
    <item type="id" name="frameRight" />
    <item type="id" name="fullscreen" />
    <item type="id" name="gesture_control" />
    <item type="id" name="ghost_view" />
    <item type="id" name="ghost_view_holder" />
    <item type="id" name="gkfsTv" />
    <item type="id" name="glide_custom_view_target_tag" />
    <item type="id" name="group_divider" />
    <item type="id" name="headLayout" />
    <item type="id" name="help_ab_loop" />
    <item type="id" name="help_back" />
    <item type="id" name="help_current" />
    <item type="id" name="help_float_close" />
    <item type="id" name="help_float_goback" />
    <item type="id" name="help_float_lock" />
    <item type="id" name="help_fullscreen" />
    <item type="id" name="help_hide" />
    <item type="id" name="help_next" />
    <item type="id" name="help_progress" />
    <item type="id" name="help_seekbar" />
    <item type="id" name="help_start" />
    <item type="id" name="help_start2" />
    <item type="id" name="help_title" />
    <item type="id" name="help_total" />
    <item type="id" name="help_view" />
    <item type="id" name="history_layout" />
    <item type="id" name="history_line" />
    <item type="id" name="home" />
    <item type="id" name="hud_view" />
    <item type="id" name="ib_shortcut" />
    <item type="id" name="icon" />
    <item type="id" name="icon_frame" />
    <item type="id" name="icon_group" />
    <item type="id" name="image" />
    <item type="id" name="image_banner" />
    <item type="id" name="imgLoading" />
    <item type="id" name="imgPauseStart" />
    <item type="id" name="imgPlayLast" />
    <item type="id" name="imgPlayNext" />
    <item type="id" name="imgRotate" />
    <item type="id" name="imgTips" />
    <item type="id" name="img_empty" />
    <item type="id" name="img_load" />
    <item type="id" name="img_loading" />
    <item type="id" name="include" />
    <item type="id" name="info" />
    <item type="id" name="info_collection_line" />
    <item type="id" name="is_full_screen" />
    <item type="id" name="item_flag" />
    <item type="id" name="item_img" />
    <item type="id" name="item_sub_title" />
    <item type="id" name="item_title" />
    <item type="id" name="item_touch_helper_previous_elevation" />
    <item type="id" name="iv" />
    <item type="id" name="ivBack" />
    <item type="id" name="ivClear" />
    <item type="id" name="ivFloat" />
    <item type="id" name="ivSearch" />
    <item type="id" name="ivStClose" />
    <item type="id" name="iv_close" />
    <item type="id" name="iv_do_play" />
    <item type="id" name="iv_empty" />
    <item type="id" name="iv_icon" />
    <item type="id" name="iv_more" />
    <item type="id" name="iv_point" />
    <item type="id" name="iv_shortcut" />
    <item type="id" name="iv_subtitle" />
    <item type="id" name="iv_title" />
    <item type="id" name="jump_ad" />
    <item type="id" name="land_progress_line" />
    <item type="id" name="largeLabel" />
    <item type="id" name="layoutBody" />
    <item type="id" name="layoutEmptyLine" />
    <item type="id" name="layoutFloatController" />
    <item type="id" name="layoutFloatView" />
    <item type="id" name="layoutHistory" />
    <item type="id" name="layoutLanguage" />
    <item type="id" name="layoutList" />
    <item type="id" name="layoutLoop" />
    <item type="id" name="layoutMultipleChoice" />
    <item type="id" name="layoutRightArea" />
    <item type="id" name="layoutScale" />
    <item type="id" name="layoutSearch" />
    <item type="id" name="layoutTitle" />
    <item type="id" name="layout_bottom" />
    <item type="id" name="layout_top" />
    <item type="id" name="left_img" />
    <item type="id" name="line1" />
    <item type="id" name="line3" />
    <item type="id" name="list" />
    <item type="id" name="listIv" />
    <item type="id" name="list_item" />
    <item type="id" name="list_line" />
    <item type="id" name="llCacheSet" />
    <item type="id" name="ll_bottom" />
    <item type="id" name="ll_checkbox" />
    <item type="id" name="ll_subtitle" />
    <item type="id" name="ll_view" />
    <item type="id" name="loading" />
    <item type="id" name="loading_container" />
    <item type="id" name="loading_imageview" />
    <item type="id" name="loading_line" />
    <item type="id" name="loading_music" />
    <item type="id" name="loading_music_view" />
    <item type="id" name="loading_speed" />
    <item type="id" name="loading_view" />
    <item type="id" name="lock_screen" />
    <item type="id" name="loginBtn" />
    <item type="id" name="login_layout" />
    <item type="id" name="ly_title" />
    <item type="id" name="masked" />
    <item type="id" name="media_actions" />
    <item type="id" name="mediacontroller_progress" />
    <item type="id" name="menuIV" />
    <item type="id" name="menuTv" />
    <item type="id" name="menu_view" />
    <item type="id" name="message" />
    <item type="id" name="month_grid" />
    <item type="id" name="month_navigation_bar" />
    <item type="id" name="month_navigation_fragment_toggle" />
    <item type="id" name="month_navigation_next" />
    <item type="id" name="month_navigation_previous" />
    <item type="id" name="month_title" />
    <item type="id" name="motion_base" />
    <item type="id" name="mtrl_calendar_day_selector_frame" />
    <item type="id" name="mtrl_calendar_days_of_week" />
    <item type="id" name="mtrl_calendar_frame" />
    <item type="id" name="mtrl_calendar_main_pane" />
    <item type="id" name="mtrl_calendar_months" />
    <item type="id" name="mtrl_calendar_selection_frame" />
    <item type="id" name="mtrl_calendar_text_input_frame" />
    <item type="id" name="mtrl_calendar_year_selector_frame" />
    <item type="id" name="mtrl_card_checked_layer_id" />
    <item type="id" name="mtrl_child_content_container" />
    <item type="id" name="mtrl_internal_children_alpha_tag" />
    <item type="id" name="mtrl_picker_fullscreen" />
    <item type="id" name="mtrl_picker_header" />
    <item type="id" name="mtrl_picker_header_selection_text" />
    <item type="id" name="mtrl_picker_header_title_and_selection" />
    <item type="id" name="mtrl_picker_header_toggle" />
    <item type="id" name="mtrl_picker_text_input_date" />
    <item type="id" name="mtrl_picker_text_input_range_end" />
    <item type="id" name="mtrl_picker_text_input_range_start" />
    <item type="id" name="mtrl_picker_title_text" />
    <item type="id" name="name" />
    <item type="id" name="nameTv" />
    <item type="id" name="navigation_header_container" />
    <item type="id" name="net_error" />
    <item type="id" name="notice_tips" />
    <item type="id" name="notification_background" />
    <item type="id" name="notification_main_column" />
    <item type="id" name="notification_main_column_container" />
    <item type="id" name="off" />
    <item type="id" name="on" />
    <item type="id" name="operation_bar" />
    <item type="id" name="operation_bg" />
    <item type="id" name="operation_tv" />
    <item type="id" name="operation_view" />
    <item type="id" name="parentPanel" />
    <item type="id" name="parent_matrix" />
    <item type="id" name="passwordEt" />
    <item type="id" name="pause" />
    <item type="id" name="player" />
    <item type="id" name="playerBtn" />
    <item type="id" name="player_frame" />
    <item type="id" name="player_toolbar" />
    <item type="id" name="policyTv" />
    <item type="id" name="port_controller_area" />
    <item type="id" name="port_controller_scrollView" />
    <item type="id" name="portrait_progress_line" />
    <item type="id" name="privacy_policy_line" />
    <item type="id" name="privacy_security" />
    <item type="id" name="pro_percent" />
    <item type="id" name="progress" />
    <item type="id" name="progressBar" />
    <item type="id" name="progressFloat" />
    <item type="id" name="progress_circular" />
    <item type="id" name="progress_horizontal" />
    <item type="id" name="q_layout_bottom" />
    <item type="id" name="qqLine" />
    <item type="id" name="qs_videoview" />
    <item type="id" name="radio" />
    <item type="id" name="radioButtonA" />
    <item type="id" name="radioButtonB" />
    <item type="id" name="radioButtonC" />
    <item type="id" name="radioButtonD" />
    <item type="id" name="radioGroup" />
    <item type="id" name="recover_screen" />
    <item type="id" name="recyclerView" />
    <item type="id" name="recycler_view" />
    <item type="id" name="right_icon" />
    <item type="id" name="right_side" />
    <item type="id" name="rootView" />
    <item type="id" name="rvVideo" />
    <item type="id" name="rx_crash_tool" />
    <item type="id" name="save_non_transition_alpha" />
    <item type="id" name="save_overlay_view" />
    <item type="id" name="scrollIndicatorDown" />
    <item type="id" name="scrollIndicatorUp" />
    <item type="id" name="scrollView" />
    <item type="id" name="scrollView2" />
    <item type="id" name="sdk_line" />
    <item type="id" name="search_badge" />
    <item type="id" name="search_bar" />
    <item type="id" name="search_button" />
    <item type="id" name="search_close_btn" />
    <item type="id" name="search_edit_frame" />
    <item type="id" name="search_go_btn" />
    <item type="id" name="search_mag_icon" />
    <item type="id" name="search_plate" />
    <item type="id" name="search_src_text" />
    <item type="id" name="search_voice_btn" />
    <item type="id" name="seekbar" />
    <item type="id" name="seekbar2" />
    <item type="id" name="seekbar_value" />
    <item type="id" name="select_dialog_listview" />
    <item type="id" name="select_title_layout" />
    <item type="id" name="service_agreement_line" />
    <item type="id" name="share_button" />
    <item type="id" name="share_log" />
    <item type="id" name="shortcut" />
    <item type="id" name="show_clause_layout" />
    <item type="id" name="sizeTv" />
    <item type="id" name="slide_line" />
    <item type="id" name="smallLabel" />
    <item type="id" name="small_close" />
    <item type="id" name="sms_title" />
    <item type="id" name="snackbar_action" />
    <item type="id" name="snackbar_text" />
    <item type="id" name="spacer" />
    <item type="id" name="special_effects_controller_view_tag" />
    <item type="id" name="speedModel" />
    <item type="id" name="speedSelect" />
    <item type="id" name="speedTouch" />
    <item type="id" name="speedTouchModel" />
    <item type="id" name="speedTv" />
    <item type="id" name="speed_list" />
    <item type="id" name="spinner" />
    <item type="id" name="split_action_bar" />
    <item type="id" name="stateIv" />
    <item type="id" name="stateTv" />
    <item type="id" name="status_bar_latest_event_content" />
    <item type="id" name="submenuarrow" />
    <item type="id" name="submit_area" />
    <item type="id" name="subtitleDisplay" />
    <item type="id" name="subtitle_display" />
    <item type="id" name="subtitle_layout" />
    <item type="id" name="subtitle_space" />
    <item type="id" name="subtitle_switch" />
    <item type="id" name="surface_container" />
    <item type="id" name="swiperefreshlayout" />
    <item type="id" name="switchScale" />
    <item type="id" name="switchWidget" />
    <item type="id" name="table" />
    <item type="id" name="tag_accessibility_actions" />
    <item type="id" name="tag_accessibility_clickable_spans" />
    <item type="id" name="tag_accessibility_heading" />
    <item type="id" name="tag_accessibility_pane_title" />
    <item type="id" name="tag_on_apply_window_listener" />
    <item type="id" name="tag_on_receive_content_listener" />
    <item type="id" name="tag_on_receive_content_mime_types" />
    <item type="id" name="tag_screen_reader_focusable" />
    <item type="id" name="tag_state_description" />
    <item type="id" name="tag_transition_group" />
    <item type="id" name="tag_unhandled_key_event_manager" />
    <item type="id" name="tag_unhandled_key_listeners" />
    <item type="id" name="tag_window_insets_animation_callback" />
    <item type="id" name="techLine" />
    <item type="id" name="telphoneLine" />
    <item type="id" name="test_checkbox_android_button_tint" />
    <item type="id" name="test_checkbox_app_button_tint" />
    <item type="id" name="text" />
    <item type="id" name="text2" />
    <item type="id" name="textPhoneError" />
    <item type="id" name="textScale" />
    <item type="id" name="textScaleDesc" />
    <item type="id" name="textSpacerNoButtons" />
    <item type="id" name="textSpacerNoTitle" />
    <item type="id" name="textVerifyError" />
    <item type="id" name="textView" />
    <item type="id" name="textView2" />
    <item type="id" name="textView3" />
    <item type="id" name="text_input_end_icon" />
    <item type="id" name="text_input_start_icon" />
    <item type="id" name="text_mark" />
    <item type="id" name="text_view" />
    <item type="id" name="textinput_counter" />
    <item type="id" name="textinput_error" />
    <item type="id" name="textinput_helper_text" />
    <item type="id" name="thumb" />
    <item type="id" name="thumbImage" />
    <item type="id" name="tileiV" />
    <item type="id" name="time" />
    <item type="id" name="time2" />
    <item type="id" name="time_current" />
    <item type="id" name="time_current2" />
    <item type="id" name="title" />
    <item type="id" name="titleBar" />
    <item type="id" name="titleDividerNoCustom" />
    <item type="id" name="titleTv" />
    <item type="id" name="title_name" />
    <item type="id" name="title_template" />
    <item type="id" name="toggleSwitch" />
    <item type="id" name="toolbar" />
    <item type="id" name="topPanel" />
    <item type="id" name="total" />
    <item type="id" name="totalTime_Tv" />
    <item type="id" name="touchView" />
    <item type="id" name="touch_outside" />
    <item type="id" name="track_list_view" />
    <item type="id" name="transition_current_scene" />
    <item type="id" name="transition_layout_save" />
    <item type="id" name="transition_position" />
    <item type="id" name="transition_scene_layoutid_cache" />
    <item type="id" name="transition_transform" />
    <item type="id" name="tvBackPlay" />
    <item type="id" name="tvBackPlayDesc" />
    <item type="id" name="tvEndTime" />
    <item type="id" name="tvFloat" />
    <item type="id" name="tvHistoryClear" />
    <item type="id" name="tvLanguage" />
    <item type="id" name="tvLoopA" />
    <item type="id" name="tvLoopB" />
    <item type="id" name="tvQuestionTitle" />
    <item type="id" name="tvRecoverScreen" />
    <item type="id" name="tvSearch" />
    <item type="id" name="tvStTime" />
    <item type="id" name="tvTitle" />
    <item type="id" name="tvUpdate" />
    <item type="id" name="tv_agree" />
    <item type="id" name="tv_content_desc" />
    <item type="id" name="tv_current" />
    <item type="id" name="tv_duration" />
    <item type="id" name="tv_empty" />
    <item type="id" name="tv_error" />
    <item type="id" name="tv_event_desc" />
    <item type="id" name="tv_exit" />
    <item type="id" name="tv_filing_link" />
    <item type="id" name="tv_filing_number" />
    <item type="id" name="tv_helplees" />
    <item type="id" name="tv_image_index" />
    <item type="id" name="tv_loading" />
    <item type="id" name="tv_percent" />
    <item type="id" name="tv_refresh" />
    <item type="id" name="tv_subtitle" />
    <item type="id" name="tv_text_content" />
    <item type="id" name="tv_text_desc" />
    <item type="id" name="tv_tips" />
    <item type="id" name="tv_title" />
    <item type="id" name="txt_left_title" />
    <item type="id" name="txt_main_title" />
    <item type="id" name="txt_right_title" />
    <item type="id" name="unchecked" />
    <item type="id" name="up" />
    <item type="id" name="userEt" />
    <item type="id" name="value" />
    <item type="id" name="video_view" />
    <item type="id" name="view_offset_helper" />
    <item type="id" name="view_stub" />
    <item type="id" name="view_transition" />
    <item type="id" name="view_tree_lifecycle_owner" />
    <item type="id" name="view_tree_on_back_pressed_dispatcher_owner" />
    <item type="id" name="view_tree_saved_state_registry_owner" />
    <item type="id" name="view_tree_view_model_store_owner" />
    <item type="id" name="visible_removing_fragment_view_tag" />
    <item type="id" name="volume_progressbar" />
    <item type="id" name="vp_images" />
    <item type="id" name="websiteLine" />
    <item type="id" name="websiteTv" />
    <item type="id" name="webview" />
    <item type="id" name="wechatLine" />
    <item type="id" name="yjfkyjb" />
</resources>
