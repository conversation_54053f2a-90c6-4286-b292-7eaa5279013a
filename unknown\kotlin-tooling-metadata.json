{"schemaVersion": "1.1.0", "buildSystem": "<PERSON><PERSON><PERSON>", "buildSystemVersion": "7.5", "buildPlugin": "org.jetbrains.kotlin.gradle.plugin.KotlinAndroidPluginWrapper", "buildPluginVersion": "1.9.21", "projectSettings": {"isHmppEnabled": true, "isCompatibilityMetadataVariantEnabled": false, "isKPMEnabled": false}, "projectTargets": [{"target": "org.jetbrains.kotlin.gradle.plugin.mpp.KotlinAndroidTarget", "platformType": "androidJvm", "extras": {"android": {"sourceCompatibility": "1.8", "targetCompatibility": "1.8"}}}]}