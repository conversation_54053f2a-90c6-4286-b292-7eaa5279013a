<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:id="@id/container" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <tv.danmaku.ijk.media.player.evplayer.utils.TextMarkView android:id="@id/text_mark" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginBottom="@dimen/dp_1" />
    <com.example.myapplication.ui.view.OutlineTextView android:textSize="@dimen/sp_14" android:textColor="@color/white" android:gravity="center" android:id="@id/subtitle_display" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_above="@id/subtitle_space" android:layout_alignParentBottom="true" app:layout_constraintBottom_toTopOf="@id/subtitle_space" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:outlineColor="#ff111111" app:outlineWidth="4.0" />
    <View android:id="@id/subtitle_space" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_alignTop="@id/layout_bottom" android:layout_alignBottom="@id/layout_bottom" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="@id/layout_bottom" />
    <ImageView android:id="@id/cover" android:background="@color/black" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="fitCenter" />
    <ImageView android:id="@id/thumbImage" android:background="@color/black" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="fitCenter" />
    <ProgressBar android:id="@id/help_progress" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="@dimen/dp_1" android:max="100" android:progressDrawable="@drawable/video_dialog_progress" android:layout_alignParentBottom="true" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" style="?android:progressBarStyleHorizontal" />
    <include app:layout_constraintTop_toTopOf="parent" layout="@layout/video_view_controller" />
    <include app:layout_constraintBottom_toBottomOf="parent" layout="@layout/video_view_title" />
    <FrameLayout android:id="@id/buffering_container" android:background="@color/transparent" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_centerHorizontal="true" android:layout_centerVertical="true" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <ImageView android:layout_gravity="center" android:id="@id/buffering_imageview" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/audio" android:scaleType="fitCenter" />
    </FrameLayout>
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/loading_container" android:background="@color/transparent" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_centerInParent="true" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <ImageView android:layout_gravity="center" android:id="@id/loading_imageview" android:layout_width="@dimen/dp_40" android:layout_height="@dimen/dp_40" android:scaleType="fitCenter" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <TextView android:textColor="@color/white" android:layout_gravity="center" android:id="@id/loading_speed" android:layout_width="wrap_content" android:layout_height="wrap_content" android:shadowColor="@color/black" android:shadowDx="3.0" android:shadowDy="2.0" android:shadowRadius="1.0" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/loading_imageview" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/error_container" android:background="@color/transparent" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_centerInParent="true" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <TextView android:textColor="@color/white" android:layout_gravity="center" android:id="@id/error_tips" android:layout_width="wrap_content" android:layout_height="wrap_content" android:shadowColor="@color/black" android:shadowDx="3.0" android:shadowDy="2.0" android:shadowRadius="1.0" android:textAlignment="center" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <TextView android:textColor="@color/white" android:layout_gravity="center" android:id="@id/error_retry" android:background="@drawable/selector_black_round_bg" android:paddingLeft="@dimen/dp_15" android:paddingTop="@dimen/dp_5" android:paddingRight="@dimen/dp_15" android:paddingBottom="@dimen/dp_5" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_15" android:layout_marginBottom="@dimen/dp_15" android:text="@string/click_re_try" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/error_tips" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <ImageView android:id="@id/help_start" android:layout_width="@dimen/dp_60" android:layout_height="wrap_content" android:src="@mipmap/video_play_normal" android:scaleType="fitCenter" android:layout_centerInParent="true" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <ImageView android:id="@id/help_float_close" android:padding="@dimen/dp_12" android:visibility="visible" android:layout_width="@dimen/dp_40" android:layout_height="@dimen/dp_40" android:src="@mipmap/icon_close_1" android:scaleType="fitCenter" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <ImageView android:id="@id/help_float_goback" android:padding="@dimen/dp_12" android:visibility="visible" android:layout_width="@dimen/dp_40" android:layout_height="@dimen/dp_40" android:src="@drawable/ic_float_window" android:scaleType="fitCenter" android:layout_alignParentBottom="true" android:layout_alignParentEnd="true" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <LinearLayout android:gravity="center" android:orientation="vertical" android:id="@id/operation_view" android:background="@drawable/option_bg_shape" android:paddingLeft="@dimen/dp_20" android:paddingTop="@dimen/dp_20" android:paddingRight="@dimen/dp_20" android:paddingBottom="@dimen/dp_20" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_centerInParent="true" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <TextView android:textSize="@dimen/sp_20" android:textColor="@color/white" android:gravity="center" android:id="@id/tv_tips" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/fast_forward" />
    </LinearLayout>
    <include android:visibility="gone" layout="@layout/layout_gesture_cover" />
    <LinearLayout android:orientation="horizontal" android:id="@id/coverDirectory" android:background="@color/translucent_black" android:visibility="gone" android:clickable="true" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <ImageView android:id="@id/help_hide" android:paddingTop="@dimen/dp_12" android:paddingBottom="@dimen/dp_12" android:layout_width="@dimen/dp_60" android:layout_height="@dimen/dp_48" android:src="@drawable/playerbar_back_icon" android:scaleType="fitCenter" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    </LinearLayout>
    <TextView android:textSize="@dimen/sp_18" android:textColor="@color/white" android:id="@id/tvRecoverScreen" android:background="@drawable/option_bg_shape" android:paddingLeft="@dimen/dp_15" android:paddingTop="@dimen/dp_8" android:paddingRight="@dimen/dp_15" android:paddingBottom="@dimen/dp_8" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/reduction_screen" android:layout_above="@id/layout_bottom" android:layout_centerHorizontal="true" app:layout_constraintBottom_toTopOf="@id/layout_bottom" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <TextView android:textColor="@color/white" android:layout_gravity="center" android:id="@id/notice_tips" android:background="@drawable/option_bg_shape" android:padding="@dimen/dp_5" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/hide_control_view" android:shadowColor="@color/black" android:shadowDx="3.0" android:shadowDy="2.0" android:shadowRadius="1.0" android:layout_centerHorizontal="true" android:layout_centerVertical="true" android:textAlignment="center" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    <LinearLayout android:gravity="center" android:orientation="vertical" android:id="@id/layoutRightArea" android:padding="@dimen/dp_10" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <ImageView android:id="@id/help_float_lock" android:padding="@dimen/dp_3" android:visibility="visible" android:layout_width="@dimen/dp_40" android:layout_height="@dimen/dp_40" android:src="@drawable/ic_icon_unlock" />
        <ImageView android:id="@id/help_ab_loop" android:padding="@dimen/dp_3" android:visibility="invisible" android:layout_width="@dimen/dp_40" android:layout_height="@dimen/dp_40" android:layout_marginTop="@dimen/dp_10" android:layout_marginBottom="@dimen/dp_10" android:src="@drawable/ic_ab_loop_close" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
