<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:orientation="vertical" android:background="@color/splash_color" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <ImageView android:layout_width="0.0dip" android:layout_height="0.0dip" android:layout_marginBottom="@dimen/dp_20" android:src="@mipmap/ic_splash" android:scaleType="fitCenter" app:layout_constraintBottom_toTopOf="@id/ll_bottom" app:layout_constraintDimensionRatio="h,15:2" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" />
    <androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:id="@id/ll_bottom" android:layout_width="fill_parent" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:layout_constraintVertical_bias="0.9">
        <TextView android:textSize="@dimen/sp_14" android:textColor="#ffafb9c6" android:gravity="center" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginBottom="@dimen/dp_10" android:text="@string/splash_content" />
        <TextView android:textSize="@dimen/sp_12" android:textColor="#ffafb9c6" android:gravity="center" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginBottom="@dimen/dp_10" android:text="@string/splash_content_2" />
        <TextView android:textSize="@dimen/sp_12" android:textColor="#ffafb9c6" android:gravity="center" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="@string/filing_number" />
    </androidx.appcompat.widget.LinearLayoutCompat>
</androidx.constraintlayout.widget.ConstraintLayout>
