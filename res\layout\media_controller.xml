<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:background="@drawable/media_gradient_background" android:paddingLeft="@dimen/dp_4" android:paddingTop="@dimen/dp_8" android:paddingRight="@dimen/dp_4" android:paddingBottom="@dimen/dp_8" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/land_progress_line" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <TextView android:textSize="12.0sp" android:textStyle="bold" android:textColor="@color/dim_foreground_dark" android:id="@id/time_current2" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="" />
        <SeekBar android:id="@id/seekbar2" android:layout_width="0.0dip" android:layout_height="wrap_content" android:progressDrawable="@drawable/player_setting_bright_progressbar" android:thumb="@drawable/seek_thumb" android:layout_weight="1.0" android:splitTrack="false" />
        <TextView android:textSize="12.0sp" android:textStyle="bold" android:textColor="@color/dim_foreground_dark" android:id="@id/time2" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="" />
    </LinearLayout>
    <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <ImageView android:id="@id/pause" android:paddingLeft="@dimen/dp_3" android:paddingTop="@dimen/dp_10" android:paddingRight="@dimen/dp_3" android:paddingBottom="@dimen/dp_10" android:layout_width="@dimen/dp_35" android:layout_height="@dimen/dp_40" android:scaleType="fitCenter" style="@style/MediaButtonCompat.Play" />
        <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/portrait_progress_line" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/dp_4" android:layout_marginRight="@dimen/dp_4" android:layout_weight="1.0">
            <TextView android:textSize="12.0sp" android:textStyle="bold" android:textColor="@color/dim_foreground_dark" android:id="@id/time_current" android:layout_width="wrap_content" android:layout_height="wrap_content" />
            <SeekBar android:id="@id/mediacontroller_progress" android:layout_width="0.0dip" android:layout_height="@dimen/dp_40" android:progressDrawable="@drawable/player_setting_bright_progressbar" android:thumb="@drawable/seek_thumb" android:layout_weight="1.0" android:splitTrack="false" />
            <TextView android:textSize="12.0sp" android:textStyle="bold" android:textColor="@color/dim_foreground_dark" android:id="@id/time" android:layout_width="wrap_content" android:layout_height="wrap_content" />
        </LinearLayout>
        <TextView android:textSize="@dimen/sp_12" android:textColor="#ffffffff" android:gravity="center" android:id="@id/speedTv" android:paddingLeft="@dimen/dp_8" android:paddingRight="@dimen/dp_8" android:layout_width="wrap_content" android:layout_height="fill_parent" android:minWidth="@dimen/dp_48" android:text="1.0X" android:textAlignment="center" />
        <ImageButton android:id="@id/ib_shortcut" android:background="#00000000" android:paddingLeft="@dimen/dp_3" android:paddingTop="@dimen/dp_11" android:paddingRight="@dimen/dp_3" android:paddingBottom="@dimen/dp_11" android:visibility="gone" android:layout_width="@dimen/dp_48" android:layout_height="@dimen/dp_40" android:src="@drawable/shortcut" android:scaleType="fitCenter" />
        <ImageButton android:id="@id/is_full_screen" android:background="#00000000" android:paddingTop="@dimen/dp_10" android:paddingBottom="@dimen/dp_10" android:layout_width="@dimen/dp_48" android:layout_height="@dimen/dp_40" android:src="@drawable/video_switch_open" android:scaleType="fitCenter" />
    </LinearLayout>
</LinearLayout>
