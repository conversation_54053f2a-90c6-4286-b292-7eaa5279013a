<?xml version="1.0" encoding="utf-8"?>
<ScrollView android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginTop="48.0dip" android:layout_marginBottom="48.0dip" android:overScrollMode="ifContentScrolls"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <TextView android:textColor="?android:textColorSecondary" android:id="@android:id/message" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="24.0dip" android:layout_marginRight="24.0dip" android:layout_marginBottom="48.0dip" android:layout_marginStart="24.0dip" android:layout_marginEnd="24.0dip" style="?android:textAppearanceSmall" />
        <EditText android:id="@android:id/edit" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="20.0dip" android:layout_marginRight="20.0dip" android:layout_marginStart="20.0dip" android:layout_marginEnd="20.0dip" />
    </LinearLayout>
</ScrollView>
