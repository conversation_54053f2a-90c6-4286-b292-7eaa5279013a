# EV2解密软件分析报告

## 项目概述
- **应用名称**: EVPlayer2 (EV加密专用播放器)
- **包名**: cn.ieway.evplayer2
- **版本**: 2.7.9 (versionCode: 279)
- **开发商**: 湖南易维信息技术有限公司 (Hunan Yiwei Information Technology Co.)
- **目标SDK**: 31 (Android 12)
- **最小SDK**: 21 (Android 5.0)

## 应用架构分析

### 1. 加固保护机制
应用使用了**梆梆加固**（jiagu）保护：
- **StubApp**: 加固壳的入口点，负责解密和加载真实应用
- **libjiagu.so**: 加固库文件，支持多架构（x86, x64, a64, arm）
- **动态加载**: 根据设备架构动态选择对应的加固库

### 2. 核心组件

#### 视频播放器组件
1. **IjkVideoView** (`tv.danmaku.ijk.media.player.evplayer.widget.media.IjkVideoView`)
   - 基于IJK播放器的自定义视频播放组件
   - 支持多种播放器后端：AndroidMediaPlayer、IjkMediaPlayer、IjkExoMediaPlayer

2. **DemoQSVideoView** (`com.example.myapplication.videoplayer.DemoQSVideoView`)
   - 应用的主要视频播放器组件
   - 集成了播放控制、进度条、手势控制等功能

3. **TextMarkView** (`tv.danmaku.ijk.media.player.evplayer.utils.TextMarkView`)
   - 视频水印/文本标记组件
   - 可能用于防录屏或版权保护

#### 加密解密相关组件
1. **Native库文件**:
   - `libijkffmpeg.so`: FFmpeg解码库
   - `libijkplayer.so`: IJK播放器核心库
   - `libMetaLib.so`: 可能包含元数据处理和解密逻辑
   - `libjgdtc.so`: 加密解密核心库

2. **工具类**:
   - `com.tianyu.util.a`: 包含字符串解密方法（XOR 0x10）
   - `com.tianyu.util.DtcLoader`: 动态加载解密库
   - `com.stub.StubApp`: 加固壳，包含大量native方法

### 3. 加密视频处理逻辑

#### 字符串加密
```java
// com.tianyu.util.a.a(String) 方法
// 使用XOR 0x10进行简单字符串加密/解密
for (int i = 0; i < charArray.length; i++) {
    charArray[i] = (char) (charArray[i] ^ 0x10);
}
```

#### 文件格式支持
- **EV格式**: 专用的加密视频格式
- **权限管理**: 需要存储权限读取本地加密视频文件
- **下载服务**: `EvsDownloadService` 用于下载加密视频

### 4. 安全机制

#### 反调试和保护
1. **架构检测**: 检测x86模拟器环境
2. **文件完整性**: 检查ELF文件头验证native库
3. **动态加载**: 运行时解密和加载关键组件
4. **多层加固**: 梆梆加固 + 自定义保护

#### 播放控制
1. **AB循环播放**: 支持区间重复播放
2. **倍速播放**: 支持0.2x到3.0x倍速
3. **手势控制**: 亮度、音量、进度控制
4. **截图保护**: 可能包含防截图机制

## 关键发现

### 1. 解密流程推测
1. **文件识别**: 通过文件头或扩展名识别EV格式
2. **密钥获取**: 可能通过网络或本地算法生成解密密钥
3. **流式解密**: 在播放过程中实时解密视频数据
4. **内存保护**: 解密后的数据仅在内存中存在

### 2. 核心技术栈
- **播放器**: 基于IJK Player（FFmpeg）
- **UI框架**: Android原生 + ConstraintLayout
- **网络**: OkHttp3
- **加密**: 自定义算法 + Native实现
- **保护**: 梆梆加固

### 3. 潜在突破点
1. **Native库分析**: libMetaLib.so和libjgdtc.so可能包含解密逻辑
2. **内存dump**: 运行时获取解密后的视频数据
3. **Hook技术**: 拦截解密函数调用
4. **协议分析**: 分析网络通信获取密钥信息

## 建议的分析方向

1. **静态分析**: 
   - 反编译native库文件
   - 分析加密算法实现
   - 研究文件格式结构

2. **动态分析**:
   - Hook关键函数调用
   - 监控文件I/O操作
   - 内存数据提取

3. **协议分析**:
   - 抓包分析网络通信
   - 研究认证和密钥交换机制

4. **逆向工程**:
   - 去除加固保护
   - 分析真实应用逻辑
   - 重构解密算法
