<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:background="@android:color/black" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <FrameLayout android:id="@id/surface_container" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    <tv.danmaku.ijk.media.player.evplayer.utils.TextMarkView android:id="@id/text_mark" android:layout_width="fill_parent" android:layout_height="fill_parent" android:layout_marginBottom="@dimen/dp_1" />
    <FrameLayout android:id="@id/thumb" android:background="@color/transparent" android:layout_width="fill_parent" android:layout_height="fill_parent" android:scaleType="fitCenter" android:layout_alignParentTop="true" android:layout_alignParentBottom="true" android:layout_alignParentStart="true" android:layout_alignParentEnd="true">
        <ImageView android:id="@id/thumbImage" android:background="@android:color/black" android:layout_width="fill_parent" android:layout_height="fill_parent" android:src="@mipmap/player_default" android:scaleType="fitCenter" />
    </FrameLayout>
    <TextView android:textSize="@dimen/sp_16" android:textColor="@color/recover_screen_text" android:layout_gravity="center" android:id="@id/recover_screen" android:background="#99000000" android:padding="@dimen/dp_4" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginBottom="@dimen/dp_8" android:text="@string/reduction_screen" android:layout_centerInParent="true" />
    <LinearLayout android:gravity="center_horizontal" android:orientation="vertical" android:id="@id/layout_bottom" android:background="@drawable/media_gradient_background" android:visibility="invisible" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_alignParentBottom="true">
        <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/land_progress_line" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/dp_8" android:layout_marginRight="@dimen/dp_8" android:layout_marginHorizontal="@dimen/dp_8">
            <TextView android:textSize="12.0sp" android:textStyle="bold" android:textColor="@color/dim_foreground_dark" android:id="@id/time_current" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="00:00" />
            <SeekBar android:id="@id/seekbar" android:layout_width="0.0dip" android:layout_height="wrap_content" android:max="100" android:progressDrawable="@drawable/player_setting_bright_progressbar" android:thumb="@drawable/seek_thumb" android:layout_weight="1.0" android:splitTrack="false" />
            <TextView android:textSize="12.0sp" android:textStyle="bold" android:textColor="@color/dim_foreground_dark" android:id="@id/time" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="00:00" />
        </LinearLayout>
        <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="@dimen/dp_40">
            <ImageView android:id="@id/playerBtn" android:layout_width="wrap_content" android:layout_height="@dimen/dp_48" android:src="@drawable/vvc_ic_media_play" android:scaleType="fitCenter" android:layout_marginStart="@dimen/dp_8" android:layout_marginEnd="@dimen/dp_2" />
            <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/portrait_progress_line" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/dp_4" android:layout_marginRight="@dimen/dp_4" android:layout_weight="1.0" android:layout_marginHorizontal="@dimen/dp_4">
                <TextView android:textSize="@dimen/sp_14" android:textColor="#ffffffff" android:id="@id/current" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="00:00" android:layout_marginStart="@dimen/padding_6" />
                <SeekBar android:id="@id/progress" android:layout_width="0.0dip" android:layout_height="wrap_content" android:max="100" android:progressDrawable="@drawable/player_setting_bright_progressbar" android:thumb="@drawable/seek_thumb" android:layout_weight="1.0" android:splitTrack="false" />
                <TextView android:textSize="@dimen/sp_14" android:textColor="#ffffffff" android:id="@id/total" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="00:00" android:layout_marginEnd="@dimen/dp_2" />
            </LinearLayout>
            <TextView android:textSize="@dimen/sp_14" android:textColor="#ffffffff" android:gravity="center" android:id="@id/speedTv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/dp_4" android:layout_marginRight="@dimen/dp_4" android:minWidth="@dimen/dp_40" android:text="1.0X" android:layout_marginHorizontal="@dimen/dp_4" />
            <ImageButton android:id="@id/iv_shortcut" android:background="#00000000" android:visibility="gone" android:layout_width="@dimen/dp_25" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/dp_8" android:layout_marginRight="@dimen/dp_8" android:src="@drawable/shortcut" android:scaleType="fitCenter" android:layout_marginHorizontal="@dimen/dp_8" />
            <ImageView android:id="@id/fullscreen" android:layout_width="@dimen/dp_25" android:layout_height="wrap_content" android:src="@drawable/video_switch_open" android:scaleType="fitCenter" android:layout_marginStart="@dimen/padding_8" android:layout_marginEnd="@dimen/dp_15" />
        </LinearLayout>
    </LinearLayout>
    <ProgressBar android:id="@id/bottom_progressbar" android:layout_width="fill_parent" android:layout_height="@dimen/dp_1" android:max="100" android:progressDrawable="@drawable/video_progress" android:layout_alignParentBottom="true" style="?android:progressBarStyleHorizontal" />
    <ImageView android:id="@id/back_tiny" android:visibility="gone" android:layout_width="@dimen/dp_24" android:layout_height="@dimen/dp_24" android:layout_marginTop="@dimen/dp_6" android:layout_marginStart="@dimen/dp_6" />
    <LinearLayout android:gravity="center_vertical" android:id="@id/layout_top" android:background="@drawable/video_title_bg" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="@dimen/dp_48">
        <ImageView android:id="@id/back" android:layout_width="@dimen/dp_48" android:layout_height="@dimen/dp_48" android:src="@drawable/playerbar_back_icon" android:scaleType="centerInside" android:paddingStart="@dimen/dp_10" />
        <TextView android:textSize="@dimen/sp_14" android:textColor="@android:color/white" android:ellipsize="end" android:id="@id/title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:lines="1" android:paddingStart="@dimen/dp_10" />
    </LinearLayout>
    <ImageView android:layout_gravity="center_vertical" android:id="@id/start" android:layout_width="@dimen/dp_60" android:layout_height="@dimen/dp_60" android:src="@mipmap/video_play_normal" android:scaleType="fitCenter" android:layout_centerHorizontal="true" android:layout_centerVertical="true" />
    <ImageView android:layout_gravity="center_vertical" android:id="@id/loading" android:visibility="invisible" android:layout_width="@dimen/dp_60" android:layout_height="@dimen/dp_60" android:scaleType="fitCenter" android:layout_centerHorizontal="true" android:layout_centerVertical="true" />
    <ImageView android:id="@id/small_close" android:paddingTop="@dimen/dp_10" android:visibility="gone" android:layout_width="@dimen/dp_30" android:layout_height="@dimen/dp_30" android:src="@mipmap/video_small_close" android:scaleType="centerInside" android:paddingStart="@dimen/dp_10" />
    <TableLayout android:id="@id/hud_view" android:background="@color/ijk_transparent_dark" android:padding="@dimen/dp_8" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_centerVertical="true" android:layout_alignParentEnd="true" />
    <TextView android:textSize="@dimen/sp_14" android:id="@id/subtitleDisplay" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_alignParentBottom="true" android:layout_centerHorizontal="true" />
    <LinearLayout android:gravity="center" android:orientation="vertical" android:id="@id/operation_view" android:background="@drawable/option_bg_shape" android:paddingLeft="@dimen/dp_20" android:paddingTop="@dimen/dp_20" android:paddingRight="@dimen/dp_20" android:paddingBottom="@dimen/dp_20" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_centerInParent="true" android:paddingHorizontal="@dimen/dp_20" android:paddingVertical="@dimen/dp_20">
        <TextView android:textSize="@dimen/sp_20" android:textColor="@color/white" android:gravity="center" android:id="@id/tv_tips" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/fast_forward" />
    </LinearLayout>
    <ImageView android:id="@id/lock_screen" android:visibility="gone" android:layout_width="@dimen/dp_30" android:layout_height="@dimen/dp_30" android:src="@mipmap/unlock" android:scaleType="centerInside" android:layout_centerVertical="true" android:layout_marginEnd="@dimen/dp_50" android:layout_alignParentEnd="true" />
</RelativeLayout>
