<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout android:id="@id/swiperefreshlayout" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <androidx.recyclerview.widget.RecyclerView android:id="@id/recyclerView" android:layout_width="fill_parent" android:layout_height="fill_parent" />
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
    <LinearLayout android:gravity="center" android:orientation="vertical" android:id="@id/empty_view" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="fill_parent" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <LinearLayout android:gravity="center" android:orientation="vertical" android:id="@id/net_error" android:paddingTop="@dimen/dp_5" android:paddingBottom="@dimen/dp_5" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:paddingVertical="@dimen/dp_5">
            <TextView android:textSize="@dimen/sp_16" android:textColor="@color/textColorBlack" android:id="@id/tv_helplees" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/network_helpless" android:drawablePadding="@dimen/dp_15" app:drawableTopCompat="@drawable/icon_net_loss" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="@id/swiperefreshlayout" app:layout_constraintTop_toTopOf="@id/swiperefreshlayout" />
        </LinearLayout>
        <TextView android:textSize="@dimen/sp_14" android:textColor="@color/textColorGray" android:id="@id/tv_error" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="@id/swiperefreshlayout" app:layout_constraintTop_toTopOf="@id/swiperefreshlayout" />
        <TextView android:textSize="@dimen/sp_14" android:textColor="@color/textColorGray" android:id="@id/tv_refresh" android:paddingTop="@dimen/dp_5" android:paddingBottom="@dimen/dp_5" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/refresh_pull" android:paddingVertical="@dimen/dp_5" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="@id/swiperefreshlayout" app:layout_constraintTop_toTopOf="@id/swiperefreshlayout" />
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
