<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:background="@color/white_frame_bg" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:gravity="center_vertical" android:orientation="horizontal" android:background="@color/primaryColor" android:layout_width="fill_parent" android:layout_height="?actionBarSize">
        <ImageView android:id="@id/backIV" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/BackImageStyle" />
        <TextView android:textSize="@dimen/sp_18" android:textColor="@color/colorF6" android:ellipsize="end" android:gravity="center" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="@string/privacy_and_security" android:lines="1" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
    <androidx.core.widget.NestedScrollView android:layout_width="fill_parent" android:layout_height="fill_parent">
        <LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content">
            <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:paddingLeft="@dimen/dp_15" android:paddingTop="@dimen/dp_10" android:paddingRight="@dimen/dp_15" android:paddingBottom="@dimen/dp_10" android:layout_width="fill_parent" android:layout_height="wrap_content" android:minHeight="@dimen/dp_50" android:paddingHorizontal="@dimen/dp_15" android:paddingVertical="@dimen/dp_10">
                <TextView android:textSize="@dimen/sp_16" android:textColor="@color/title_textcolor" android:fadingEdge="horizontal" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/privacy_management" android:singleLine="true" />
            </LinearLayout>
            <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/privacy_policy_line" android:background="@color/item_bg" android:paddingLeft="@dimen/dp_15" android:paddingTop="@dimen/dp_10" android:paddingRight="@dimen/dp_15" android:paddingBottom="@dimen/dp_10" android:layout_width="fill_parent" android:layout_height="wrap_content" android:minHeight="@dimen/dp_50" android:paddingHorizontal="@dimen/dp_15" android:paddingVertical="@dimen/dp_10">
                <TextView android:textSize="@dimen/sp_16" android:textColor="@color/title_textcolor" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/text_privacy_policy" android:singleLine="true" android:layout_weight="1.0" />
                <ImageView android:layout_width="@dimen/dp_25" android:layout_height="@dimen/dp_25" android:src="@drawable/icon_arrow_right" android:scaleType="fitCenter" />
            </LinearLayout>
            <View android:background="@color/divider_color" android:layout_width="fill_parent" android:layout_height="1.0px" />
            <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/service_agreement_line" android:background="@color/item_bg" android:paddingLeft="@dimen/dp_15" android:paddingTop="@dimen/dp_10" android:paddingRight="@dimen/dp_15" android:paddingBottom="@dimen/dp_10" android:layout_width="fill_parent" android:layout_height="wrap_content" android:minHeight="@dimen/dp_50" android:paddingHorizontal="@dimen/dp_15" android:paddingVertical="@dimen/dp_10">
                <TextView android:textSize="@dimen/sp_16" android:textColor="@color/title_textcolor" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/text_service_agreement" android:singleLine="true" android:layout_weight="1.0" />
                <ImageView android:layout_width="@dimen/dp_25" android:layout_height="@dimen/dp_25" android:src="@drawable/icon_arrow_right" android:scaleType="fitCenter" />
            </LinearLayout>
            <View android:background="@color/divider_color" android:layout_width="fill_parent" android:layout_height="1.0px" />
            <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/info_collection_line" android:background="@color/item_bg" android:paddingLeft="@dimen/dp_15" android:paddingTop="@dimen/dp_10" android:paddingRight="@dimen/dp_15" android:paddingBottom="@dimen/dp_10" android:layout_width="fill_parent" android:layout_height="wrap_content" android:minHeight="@dimen/dp_50" android:paddingHorizontal="@dimen/dp_15" android:paddingVertical="@dimen/dp_10">
                <TextView android:textSize="@dimen/sp_16" android:textColor="@color/title_textcolor" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/personal_info_collection_list" android:singleLine="true" android:layout_weight="1.0" />
                <ImageView android:layout_width="@dimen/dp_25" android:layout_height="@dimen/dp_25" android:src="@drawable/icon_arrow_right" android:scaleType="fitCenter" />
            </LinearLayout>
            <View android:background="@color/divider_color" android:layout_width="fill_parent" android:layout_height="1.0px" />
            <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/sdk_line" android:background="@color/item_bg" android:paddingLeft="@dimen/dp_15" android:paddingTop="@dimen/dp_10" android:paddingRight="@dimen/dp_15" android:paddingBottom="@dimen/dp_10" android:layout_width="fill_parent" android:layout_height="wrap_content" android:minHeight="@dimen/dp_50" android:paddingHorizontal="@dimen/dp_15" android:paddingVertical="@dimen/dp_10">
                <TextView android:textSize="@dimen/sp_16" android:textColor="@color/title_textcolor" android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/sdk_share_list" android:singleLine="true" android:layout_weight="1.0" />
                <ImageView android:layout_width="@dimen/dp_25" android:layout_height="@dimen/dp_25" android:src="@drawable/icon_arrow_right" android:scaleType="fitCenter" />
            </LinearLayout>
            <View android:background="@color/divider_color" android:layout_width="fill_parent" android:layout_height="1.0px" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>
