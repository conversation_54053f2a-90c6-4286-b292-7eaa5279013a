<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:orientation="vertical" android:background="@color/white" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <include android:id="@id/include" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" layout="@layout/common_title_layout" />
    <ScrollView android:scrollbarThumbVertical="@color/transparent" android:id="@id/scrollView" android:layout_width="fill_parent" android:layout_height="0.0dip" android:overScrollMode="never" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toBottomOf="@id/include">
        <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/touchView" android:layout_width="fill_parent" android:layout_height="wrap_content">
            <LinearLayout android:layout_gravity="center_horizontal" android:orientation="vertical" android:layout_width="@dimen/dp_360" android:layout_height="fill_parent" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
                <LinearLayout android:gravity="center_horizontal" android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="@dimen/login_logo_layout_marginTop" android:layout_marginBottom="@dimen/login_logo_marginBottom">
                    <ImageView android:layout_width="@dimen/icon_image_size" android:layout_height="@dimen/icon_image_size" android:layout_marginTop="@dimen/dp_25" android:src="@mipmap/ic_launcher" android:scaleType="fitXY" />
                    <TextView android:textSize="@dimen/login_title_textSize" android:textStyle="bold" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_10" android:layout_marginBottom="@dimen/dp_10" android:text="@string/app_name" style="@style/grey_text_style" />
                </LinearLayout>
                <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/dp_20" android:layout_marginTop="@dimen/dp_10" android:layout_marginRight="@dimen/dp_20" android:layout_marginBottom="@dimen/dp_10">
                    <ImageView android:paddingLeft="@dimen/dp_5" android:paddingRight="@dimen/dp_5" android:layout_width="@dimen/login_user_image_Size" android:layout_height="@dimen/login_user_image_Size" android:src="@mipmap/user_icon" android:scaleType="fitCenter" />
                    <EditText android:textColor="@color/textColorBlack" android:textColorHint="@color/textColorGray9" android:id="@id/userEt" android:background="@null" android:layout_width="fill_parent" android:layout_height="wrap_content" android:hint="@string/input_account" android:inputType="textEmailAddress" style="@style/middle_grey_text_style" />
                </LinearLayout>
                <View android:layout_marginLeft="@dimen/dp_20" android:layout_marginRight="@dimen/dp_20" style="@style/thin_dividing_line_style" />
                <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/dp_20" android:layout_marginTop="@dimen/dp_10" android:layout_marginRight="@dimen/dp_20" android:layout_marginBottom="@dimen/dp_10">
                    <ImageView android:paddingLeft="@dimen/dp_5" android:paddingRight="@dimen/dp_5" android:layout_width="@dimen/login_user_image_Size" android:layout_height="@dimen/login_user_image_Size" android:src="@mipmap/user_password" android:scaleType="fitCenter" />
                    <EditText android:textColor="@color/textColorBlack" android:textColorHint="@color/textColorGray9" android:id="@id/passwordEt" android:background="@null" android:layout_width="fill_parent" android:layout_height="wrap_content" android:hint="@string/input_pwd" android:inputType="textPassword" android:imeOptions="actionDone" android:imeActionLabel="@string/action_sign_in_short" style="@style/middle_grey_text_style" />
                </LinearLayout>
                <View android:layout_marginLeft="@dimen/dp_20" android:layout_marginRight="@dimen/dp_20" style="@style/thin_dividing_line_style" />
                <TextView android:gravity="center" android:id="@id/loginBtn" android:background="@drawable/login_button_selector" android:padding="@dimen/tjsqzh_button_padding" android:layout_width="fill_parent" android:layout_height="@dimen/dp_50" android:layout_margin="@dimen/dp_20" android:text="@string/login" style="@style/title_white_text_style" />
                <LinearLayout android:gravity="center" android:orientation="horizontal" android:id="@id/agreeline" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_margin="@dimen/margin">
                    <CheckBox android:id="@id/checkbox" android:layout_width="wrap_content" android:layout_height="wrap_content" android:checked="false" />
                </LinearLayout>
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>
