<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <include layout="@layout/common_title_layout" />
    <androidx.core.widget.NestedScrollView android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_weight="1.0" android:overScrollMode="never">
        <LinearLayout android:orientation="vertical" android:descendantFocusability="blocksDescendants" android:layout_width="fill_parent" android:layout_height="wrap_content">
            <LinearLayout android:gravity="center_horizontal" android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginTop="@dimen/login_logo_layout_marginTop" android:layout_marginBottom="@dimen/login_logo_marginBottom">
                <ImageView android:layout_width="@dimen/icon_image_size" android:layout_height="@dimen/icon_image_size" android:layout_marginTop="@dimen/dp_30" android:src="@mipmap/ic_launcher" android:scaleType="fitCenter" />
                <TextView android:textSize="@dimen/login_title_textSize" android:textStyle="bold" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/dp_10" android:text="@string/app_name" style="@style/grey_text_style" />
                <TextView android:textSize="@dimen/sp_16" android:id="@id/appVersion" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_margin="@dimen/margin" style="@style/theme_color_text_style" />
            </LinearLayout>
            <View android:layout_marginTop="@dimen/dp_10" style="@style/dividing_line_style" />
            <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/checkVersion" android:background="?android:selectableItemBackground" android:paddingLeft="@dimen/setting_dimen" android:paddingRight="@dimen/setting_dimen" android:layout_width="fill_parent" android:layout_height="@dimen/dp_50" android:paddingHorizontal="@dimen/setting_dimen">
                <TextView android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/check_version" android:layout_weight="1.0" style="@style/middle_grey_text_style" />
                <ImageView android:layout_width="@dimen/arrow_folder_size" android:layout_height="@dimen/arrow_folder_size" android:src="@mipmap/retract" android:scaleType="fitXY" />
            </LinearLayout>
            <View style="@style/thin_dividing_line_style" />
            <LinearLayout android:orientation="vertical" android:id="@id/websiteLine" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:background="?android:selectableItemBackground" android:paddingLeft="@dimen/setting_dimen" android:paddingRight="@dimen/setting_dimen" android:layout_width="fill_parent" android:layout_height="@dimen/dp_50" android:paddingHorizontal="@dimen/setting_dimen">
                    <TextView android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/address_web" android:layout_weight="1.0" style="@style/middle_grey_text_style" />
                    <TextView android:textColorLink="@color/primaryColor" android:autoLink="web" android:id="@id/websiteTv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/website" style="@style/middle_grey_text_style" />
                </LinearLayout>
                <View style="@style/thin_dividing_line_style" />
            </LinearLayout>
            <LinearLayout android:orientation="vertical" android:id="@id/companyLine" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:background="?android:selectableItemBackground" android:paddingLeft="@dimen/setting_dimen" android:paddingRight="@dimen/setting_dimen" android:layout_width="fill_parent" android:layout_height="@dimen/dp_50" android:paddingHorizontal="@dimen/setting_dimen">
                    <TextView android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/company_name" style="@style/middle_grey_text_style" />
                    <TextView android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="@string/company" android:textAlignment="textEnd" style="@style/middle_grey_text_style" />
                </LinearLayout>
                <View style="@style/thin_dividing_line_style" />
            </LinearLayout>
            <LinearLayout android:orientation="vertical" android:id="@id/wechatLine" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:background="?android:selectableItemBackground" android:paddingLeft="@dimen/setting_dimen" android:paddingRight="@dimen/setting_dimen" android:layout_width="fill_parent" android:layout_height="@dimen/dp_50" android:paddingHorizontal="@dimen/setting_dimen">
                    <TextView android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/wechat_name" android:layout_weight="1.0" style="@style/middle_grey_text_style" />
                    <TextView android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/wechat" style="@style/middle_grey_text_style" />
                </LinearLayout>
                <View style="@style/thin_dividing_line_style" />
            </LinearLayout>
            <LinearLayout android:orientation="vertical" android:id="@id/qqLine" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:background="?android:selectableItemBackground" android:paddingLeft="@dimen/setting_dimen" android:paddingRight="@dimen/setting_dimen" android:layout_width="fill_parent" android:layout_height="@dimen/dp_50" android:paddingHorizontal="@dimen/setting_dimen">
                    <TextView android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/qq_name" android:layout_weight="1.0" style="@style/middle_grey_text_style" />
                    <TextView android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/qq" style="@style/middle_grey_text_style" />
                </LinearLayout>
                <View style="@style/thin_dividing_line_style" />
            </LinearLayout>
            <LinearLayout android:orientation="vertical" android:id="@id/telphoneLine" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:background="?android:selectableItemBackground" android:paddingLeft="@dimen/setting_dimen" android:paddingRight="@dimen/setting_dimen" android:layout_width="fill_parent" android:layout_height="@dimen/dp_50" android:paddingHorizontal="@dimen/setting_dimen">
                    <TextView android:layout_width="0.0dip" android:layout_height="wrap_content" android:text="@string/contact_us" android:layout_weight="1.0" style="@style/middle_grey_text_style" />
                    <TextView android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/telphone" style="@style/middle_grey_text_style" />
                </LinearLayout>
                <View style="@style/thin_dividing_line_style" />
            </LinearLayout>
            <LinearLayout android:orientation="vertical" android:id="@id/techLine" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content">
                <LinearLayout android:gravity="center_vertical" android:paddingLeft="@dimen/setting_dimen" android:paddingRight="@dimen/setting_dimen" android:layout_width="fill_parent" android:layout_height="@dimen/dp_50" android:paddingHorizontal="@dimen/setting_dimen">
                    <TextView android:textSize="@dimen/textSize" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/text_rights" style="@style/middle_grey_text_style" />
                    <TextView android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="@string/tech" android:textAlignment="textEnd" style="@style/middle_grey_text_style" />
                </LinearLayout>
                <View style="@style/thin_dividing_line_style" />
            </LinearLayout>
            <LinearLayout android:orientation="vertical" android:id="@id/descriptionLine" android:paddingLeft="@dimen/setting_dimen" android:paddingRight="@dimen/setting_dimen" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingHorizontal="@dimen/setting_dimen">
                <TextView android:id="@id/descriptionTv" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/company_description" style="@style/middle_grey_text_style" />
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
    <LinearLayout android:gravity="center_horizontal" android:orientation="vertical" android:id="@id/agreeline" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginBottom="@dimen/dp_20">
        <androidx.appcompat.widget.LinearLayoutCompat android:gravity="bottom|center" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content">
            <TextView android:gravity="center" android:id="@id/agreementTv" android:layout_width="wrap_content" android:layout_height="fill_parent" android:text="@string/text_agreement_2" style="@style/small_theme_color_text_style" />
            <TextView android:padding="@dimen/dp_6" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="|" style="@style/small_grey_text_style" />
            <TextView android:gravity="center" android:id="@id/policyTv" android:layout_width="wrap_content" android:layout_height="fill_parent" android:text="@string/text_policy_2" style="@style/small_theme_color_text_style" />
        </androidx.appcompat.widget.LinearLayoutCompat>
        <TextView android:textSize="@dimen/sp_12" android:textColor="@color/textColorGray9" android:id="@id/tv_filing_number" android:paddingLeft="@dimen/dp_10" android:paddingTop="@dimen/dp_5" android:paddingRight="@dimen/dp_10" android:paddingBottom="@dimen/dp_5" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/filing_number" android:paddingHorizontal="@dimen/dp_10" android:paddingVertical="@dimen/dp_5" />
        <TextView android:textSize="@dimen/sp_12" android:textColor="@color/textColorGray9" android:autoLink="web" android:id="@id/tv_filing_link" android:paddingLeft="@dimen/dp_10" android:paddingTop="@dimen/dp_5" android:paddingRight="@dimen/dp_10" android:paddingBottom="@dimen/dp_5" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/link_url" android:paddingHorizontal="@dimen/dp_10" android:paddingVertical="@dimen/dp_5" />
    </LinearLayout>
</LinearLayout>
