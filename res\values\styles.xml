<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="AlertDialog.AppCompat" parent="@style/Base.AlertDialog.AppCompat" />
    <style name="AlertDialog.AppCompat.Light" parent="@style/Base.AlertDialog.AppCompat.Light" />
    <style name="Animation.AppCompat.Dialog" parent="@style/Base.Animation.AppCompat.Dialog" />
    <style name="Animation.AppCompat.DropDownUp" parent="@style/Base.Animation.AppCompat.DropDownUp" />
    <style name="Animation.AppCompat.Tooltip" parent="@style/Base.Animation.AppCompat.Tooltip" />
    <style name="Animation.Design.BottomSheetDialog" parent="@style/Animation.AppCompat.Dialog">
        <item name="android:windowEnterAnimation">@anim/design_bottom_sheet_slide_in</item>
        <item name="android:windowExitAnimation">@anim/design_bottom_sheet_slide_out</item>
    </style>
    <style name="Animation.MaterialComponents.BottomSheetDialog" parent="@style/Animation.AppCompat.Dialog">
        <item name="android:windowEnterAnimation">@anim/mtrl_bottom_sheet_slide_in</item>
        <item name="android:windowExitAnimation">@anim/mtrl_bottom_sheet_slide_out</item>
    </style>
    <style name="AppTheme" parent="@style/Theme.AppCompat.Light.NoActionBar">
        <item name="android:actionMenuTextColor">@android:color/white</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="AppTheme.AppBarOverlay" parent="@style/ThemeOverlay.AppCompat.Dark.ActionBar" />
    <style name="AppTheme.PopActivity" parent="@style/Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@android:color/white</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:background">@null</item>
        <item name="android:title">@null</item>
        <item name="android:dialogTitle">@null</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowCloseOnTouchOutside">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="AppTheme.PopupOverlay" parent="@style/ThemeOverlay.AppCompat.Light" />
    <style name="AppTheme.Splash" parent="@style/Theme.MaterialComponents.Light.NoActionBar">
        <item name="colorAccent">@color/splash_color</item>
        <item name="colorPrimary">@color/splash_color</item>
        <item name="colorPrimaryDark">@color/splash_color</item>
    </style>
    <style name="BackImageStyle">
        <item name="android:layout_width">@dimen/dp_40</item>
        <item name="android:layout_height">fill_parent</item>
        <item name="android:src">@mipmap/back_white</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="android:paddingStart">@dimen/dp_9</item>
        <item name="android:paddingEnd">@dimen/dp_9</item>
        <item name="android:layout_marginStart">@dimen/dp_9</item>
    </style>
    <style name="Base.AlertDialog.AppCompat" parent="@android:style/Widget">
        <item name="android:layout">@layout/abc_alert_dialog_material</item>
        <item name="buttonIconDimen">@dimen/abc_alert_dialog_button_dimen</item>
        <item name="listItemLayout">@layout/select_dialog_item_material</item>
        <item name="listLayout">@layout/abc_select_dialog_material</item>
        <item name="multiChoiceItemLayout">@layout/select_dialog_multichoice_material</item>
        <item name="singleChoiceItemLayout">@layout/select_dialog_singlechoice_material</item>
    </style>
    <style name="Base.AlertDialog.AppCompat.Light" parent="@style/Base.AlertDialog.AppCompat" />
    <style name="Base.Animation.AppCompat.Dialog" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/abc_popup_enter</item>
        <item name="android:windowExitAnimation">@anim/abc_popup_exit</item>
    </style>
    <style name="Base.Animation.AppCompat.DropDownUp" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/abc_grow_fade_in_from_bottom</item>
        <item name="android:windowExitAnimation">@anim/abc_shrink_fade_out_from_bottom</item>
    </style>
    <style name="Base.Animation.AppCompat.Tooltip" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@anim/abc_tooltip_enter</item>
        <item name="android:windowExitAnimation">@anim/abc_tooltip_exit</item>
    </style>
    <style name="Base.CardView" parent="@android:style/Widget">
        <item name="cardCornerRadius">@dimen/cardview_default_radius</item>
        <item name="cardElevation">@dimen/cardview_default_elevation</item>
        <item name="cardMaxElevation">@dimen/cardview_default_elevation</item>
        <item name="cardPreventCornerOverlap">true</item>
        <item name="cardUseCompatPadding">false</item>
    </style>
    <style name="Base.DialogWindowTitle.AppCompat" parent="@android:style/Widget">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Title</item>
        <item name="android:maxLines">1</item>
        <item name="android:scrollHorizontally">true</item>
    </style>
    <style name="Base.DialogWindowTitleBackground.AppCompat" parent="@android:style/Widget">
        <item name="android:background">@null</item>
        <item name="android:paddingLeft">?dialogPreferredPadding</item>
        <item name="android:paddingTop">@dimen/abc_dialog_padding_top_material</item>
        <item name="android:paddingRight">?dialogPreferredPadding</item>
    </style>
    <style name="Base.MaterialAlertDialog.MaterialComponents.Title.Icon" parent="@android:style/Widget">
        <item name="android:layout_width">32.0dip</item>
        <item name="android:layout_height">32.0dip</item>
        <item name="android:src">@null</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="android:contentDescription">@string/icon_content_description</item>
    </style>
    <style name="Base.MaterialAlertDialog.MaterialComponents.Title.Panel" parent="@android:style/Widget">
        <item name="android:paddingLeft">?dialogPreferredPadding</item>
        <item name="android:paddingTop">@dimen/abc_dialog_padding_top_material</item>
        <item name="android:paddingRight">?dialogPreferredPadding</item>
        <item name="android:layout_width">fill_parent</item>
        <item name="android:layout_height">wrap_content</item>
    </style>
    <style name="Base.MaterialAlertDialog.MaterialComponents.Title.Text" parent="@style/RtlOverlay.DialogWindowTitle.AppCompat">
        <item name="android:textAppearance">?textAppearanceSubtitle1</item>
        <item name="android:textColor">@color/material_on_surface_emphasis_high_type</item>
        <item name="android:ellipsize">end</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:singleLine">true</item>
    </style>
    <style name="Base.TextAppearance.AppCompat" parent="@android:style/TextAppearance.Material" />
    <style name="Base.TextAppearance.AppCompat.Body1" parent="@android:style/TextAppearance.Material.Body1" />
    <style name="Base.TextAppearance.AppCompat.Body2" parent="@android:style/TextAppearance.Material.Body2" />
    <style name="Base.TextAppearance.AppCompat.Button" parent="@android:style/TextAppearance.Material.Button" />
    <style name="Base.TextAppearance.AppCompat.Caption" parent="@android:style/TextAppearance.Material.Caption" />
    <style name="Base.TextAppearance.AppCompat.Display1" parent="@android:style/TextAppearance.Material.Display1" />
    <style name="Base.TextAppearance.AppCompat.Display2" parent="@android:style/TextAppearance.Material.Display2" />
    <style name="Base.TextAppearance.AppCompat.Display3" parent="@android:style/TextAppearance.Material.Display3" />
    <style name="Base.TextAppearance.AppCompat.Display4" parent="@android:style/TextAppearance.Material.Display4" />
    <style name="Base.TextAppearance.AppCompat.Headline" parent="@android:style/TextAppearance.Material.Headline" />
    <style name="Base.TextAppearance.AppCompat.Inverse" parent="@android:style/TextAppearance.Material.Inverse" />
    <style name="Base.TextAppearance.AppCompat.Large" parent="@android:style/TextAppearance.Material.Large" />
    <style name="Base.TextAppearance.AppCompat.Large.Inverse" parent="@android:style/TextAppearance.Material.Large.Inverse" />
    <style name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" parent="@android:style/TextAppearance.Material.Widget.PopupMenu.Large" />
    <style name="Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" parent="@android:style/TextAppearance.Material.Widget.PopupMenu.Small" />
    <style name="Base.TextAppearance.AppCompat.Medium" parent="@android:style/TextAppearance.Material.Medium" />
    <style name="Base.TextAppearance.AppCompat.Medium.Inverse" parent="@android:style/TextAppearance.Material.Medium.Inverse" />
    <style name="Base.TextAppearance.AppCompat.Menu" parent="@android:style/TextAppearance.Material.Menu" />
    <style name="Base.TextAppearance.AppCompat.SearchResult" parent="">
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">?android:textColorPrimary</item>
        <item name="android:textColorHint">?android:textColorHint</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Subtitle" parent="@android:style/TextAppearance.Material.SearchResult.Subtitle" />
    <style name="Base.TextAppearance.AppCompat.SearchResult.Title" parent="@android:style/TextAppearance.Material.SearchResult.Title" />
    <style name="Base.TextAppearance.AppCompat.Small" parent="@android:style/TextAppearance.Material.Small" />
    <style name="Base.TextAppearance.AppCompat.Small.Inverse" parent="@android:style/TextAppearance.Material.Small.Inverse" />
    <style name="Base.TextAppearance.AppCompat.Subhead" parent="@android:style/TextAppearance.Material.Subhead" />
    <style name="Base.TextAppearance.AppCompat.Subhead.Inverse" parent="@style/Base.TextAppearance.AppCompat.Subhead">
        <item name="android:textColor">?android:textColorPrimaryInverse</item>
        <item name="android:textColorHighlight">?android:textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:textColorHintInverse</item>
        <item name="android:textColorLink">?android:textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Title" parent="@android:style/TextAppearance.Material.Title" />
    <style name="Base.TextAppearance.AppCompat.Title.Inverse" parent="@style/Base.TextAppearance.AppCompat.Title">
        <item name="android:textColor">?android:textColorPrimaryInverse</item>
        <item name="android:textColorHighlight">?android:textColorHighlightInverse</item>
        <item name="android:textColorHint">?android:textColorHintInverse</item>
        <item name="android:textColorLink">?android:textColorLinkInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Tooltip" parent="@style/Base.TextAppearance.AppCompat">
        <item name="android:textSize">14.0sp</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="@style/TextAppearance.AppCompat.Button">
        <item name="android:textColor">?actionMenuTextColor</item>
        <item name="textAllCaps">@bool/abc_config_actionMenuItemAllCaps</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Subtitle" />
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Subtitle.Inverse" />
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Title" />
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Title.Inverse" />
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="@android:style/TextAppearance.Material.Widget.ActionMode.Subtitle" />
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title" parent="@android:style/TextAppearance.Material.Widget.ActionMode.Title" />
    <style name="Base.TextAppearance.AppCompat.Widget.Button" parent="@android:style/TextAppearance.Material.Widget.Button" />
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored" parent="@style/Base.TextAppearance.AppCompat.Widget.Button">
        <item name="android:textColor">@color/abc_btn_colored_borderless_text_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Colored" parent="@style/Base.TextAppearance.AppCompat.Widget.Button">
        <item name="android:textColor">@color/abc_btn_colored_text_material</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Inverse" parent="@style/TextAppearance.AppCompat.Button">
        <item name="android:textColor">?android:textColorPrimaryInverse</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.DropDownItem" parent="@android:style/TextAppearance.Small">
        <item name="android:textColor">?android:textColorPrimaryDisableOnly</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header" parent="@style/TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/abc_text_size_menu_header_material</item>
        <item name="android:textColor">?android:textColorSecondary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="@android:style/TextAppearance.Material.Widget.PopupMenu.Large" />
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="@android:style/TextAppearance.Material.Widget.PopupMenu.Small" />
    <style name="Base.TextAppearance.AppCompat.Widget.Switch" parent="@android:style/TextAppearance.Material.Button" />
    <style name="Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="@android:style/TextAppearance.Material.Widget.TextView.SpinnerItem" />
    <style name="Base.TextAppearance.MaterialComponents.Badge" parent="@style/TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/mtrl_badge_text_size</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">?colorOnError</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.08928572</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="Base.TextAppearance.MaterialComponents.Button" parent="@style/TextAppearance.AppCompat.Button">
        <item name="android:textSize">14.0sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.08928572</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="Base.TextAppearance.MaterialComponents.Headline6" parent="@style/TextAppearance.AppCompat.Title">
        <item name="android:textSize">20.0sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.0125</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="Base.TextAppearance.MaterialComponents.Subtitle2" parent="@style/TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">14.0sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.007142857</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item" parent="@android:style/TextAppearance.Medium">
        <item name="android:textColor">?android:textColorPrimaryDisableOnly</item>
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Subtitle" />
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title" parent="@android:style/TextAppearance.Material.Widget.ActionBar.Title" />
    <style name="Base.Theme.AppCompat" parent="@style/Base.V21.Theme.AppCompat" />
    <style name="Base.Theme.AppCompat.CompactMenu" parent="">
        <item name="android:listViewStyle">@style/Widget.AppCompat.ListView.Menu</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.DropDownUp</item>
        <item name="android:itemTextAppearance">?android:textAppearanceMedium</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog" parent="@style/Base.V21.Theme.AppCompat.Dialog" />
    <style name="Base.Theme.AppCompat.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog.FixedSize" parent="@style/Base.Theme.AppCompat.Dialog">
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.DialogWhenLarge" parent="@style/Theme.AppCompat" />
    <style name="Base.Theme.AppCompat.Light" parent="@style/Base.V21.Theme.AppCompat.Light" />
    <style name="Base.Theme.AppCompat.Light.DarkActionBar" parent="@style/Base.Theme.AppCompat.Light">
        <item name="actionBarPopupTheme">@style/ThemeOverlay.AppCompat.Light</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.Dark.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog" parent="@style/Base.V21.Theme.AppCompat.Light.Dialog" />
    <style name="Base.Theme.AppCompat.Light.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Light.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.FixedSize" parent="@style/Base.Theme.AppCompat.Light.Dialog">
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Light.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.DialogWhenLarge" parent="@style/Theme.AppCompat.Light" />
    <style name="Base.Theme.MaterialComponents" parent="@style/Base.V14.Theme.MaterialComponents" />
    <style name="Base.Theme.MaterialComponents.Bridge" parent="@style/Base.V14.Theme.MaterialComponents.Bridge" />
    <style name="Base.Theme.MaterialComponents.CompactMenu" parent="">
        <item name="android:listViewStyle">@style/Widget.AppCompat.ListView.Menu</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.DropDownUp</item>
        <item name="android:itemTextAppearance">?android:textAppearanceMedium</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Dialog" parent="@style/Base.V14.Theme.MaterialComponents.Dialog" />
    <style name="Base.Theme.MaterialComponents.Dialog.Alert" parent="@style/Base.Theme.MaterialComponents.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Dialog.Bridge" parent="@style/Base.V14.Theme.MaterialComponents.Dialog.Bridge" />
    <style name="Base.Theme.MaterialComponents.Dialog.FixedSize" parent="@style/Base.Theme.MaterialComponents.Dialog">
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Dialog.MinWidth" parent="@style/Base.Theme.MaterialComponents.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.DialogWhenLarge" parent="@style/Theme.MaterialComponents" />
    <style name="Base.Theme.MaterialComponents.Light" parent="@style/Base.V14.Theme.MaterialComponents.Light" />
    <style name="Base.Theme.MaterialComponents.Light.Bridge" parent="@style/Base.V14.Theme.MaterialComponents.Light.Bridge" />
    <style name="Base.Theme.MaterialComponents.Light.DarkActionBar" parent="@style/Base.Theme.MaterialComponents.Light">
        <item name="actionBarTheme">@style/ThemeOverlay.MaterialComponents.Dark.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge" parent="@style/Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge" />
    <style name="Base.Theme.MaterialComponents.Light.Dialog" parent="@style/Base.V14.Theme.MaterialComponents.Light.Dialog" />
    <style name="Base.Theme.MaterialComponents.Light.Dialog.Alert" parent="@style/Base.Theme.MaterialComponents.Light.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Light.Dialog.Bridge" parent="@style/Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge" />
    <style name="Base.Theme.MaterialComponents.Light.Dialog.FixedSize" parent="@style/Base.Theme.MaterialComponents.Light.Dialog">
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Light.Dialog.MinWidth" parent="@style/Base.Theme.MaterialComponents.Light.Dialog">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents.Light.DialogWhenLarge" parent="@style/Theme.MaterialComponents.Light" />
    <style name="Base.ThemeOverlay.AppCompat" parent="@style/Platform.ThemeOverlay.AppCompat" />
    <style name="Base.ThemeOverlay.AppCompat.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat">
        <item name="colorControlNormal">?android:textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dark" parent="@style/Platform.ThemeOverlay.AppCompat.Dark">
        <item name="android:colorForeground">@color/foreground_material_dark</item>
        <item name="android:colorBackground">@color/background_material_dark</item>
        <item name="android:textColorPrimary">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_light</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>
        <item name="android:windowBackground">@color/background_material_dark</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_dark</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_light</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_dark</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorControlNormal">?android:textColorSecondary</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>
        <item name="isLightTheme">false</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dark.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat.Dark">
        <item name="colorControlNormal">?android:textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dialog" parent="@style/Base.V21.ThemeOverlay.AppCompat.Dialog" />
    <style name="Base.ThemeOverlay.AppCompat.Dialog.Alert" parent="@style/Base.ThemeOverlay.AppCompat.Dialog">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Light" parent="@style/Platform.ThemeOverlay.AppCompat.Light">
        <item name="android:colorForeground">@color/foreground_material_light</item>
        <item name="android:colorBackground">@color/background_material_light</item>
        <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_light</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>
        <item name="android:windowBackground">@color/background_material_light</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_light</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_dark</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_light</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorControlNormal">?android:textColorSecondary</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>
        <item name="isLightTheme">true</item>
    </style>
    <style name="Base.ThemeOverlay.MaterialComponents.Dialog" parent="@style/Base.V14.ThemeOverlay.MaterialComponents.Dialog">
        <item name="android:windowBackground">@drawable/mtrl_dialog_background</item>
    </style>
    <style name="Base.ThemeOverlay.MaterialComponents.Dialog.Alert" parent="@style/Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert" />
    <style name="Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog" parent="@style/Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog" />
    <style name="Base.V14.Theme.MaterialComponents" parent="@style/Base.V14.Theme.MaterialComponents.Bridge">
        <item name="android:colorBackground">@color/design_dark_default_color_background</item>
        <item name="android:timePickerDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog</item>
        <item name="android:datePickerDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog</item>
        <item name="actionBarPopupTheme">@style/ThemeOverlay.MaterialComponents.Dark</item>
        <item name="actionBarSize">@dimen/mtrl_toolbar_default_height</item>
        <item name="actionBarStyle">@style/Widget.MaterialComponents.ActionBar.Surface</item>
        <item name="actionBarTheme">@style/ThemeOverlay.MaterialComponents.ActionBar.Surface</item>
        <item name="actionOverflowMenuStyle">@style/Widget.MaterialComponents.PopupMenu.Overflow</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
        <item name="appBarLayoutStyle">@style/Widget.MaterialComponents.AppBarLayout.Surface</item>
        <item name="badgeStyle">@style/Widget.MaterialComponents.Badge</item>
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
        <item name="bottomSheetDialogTheme">@style/ThemeOverlay.MaterialComponents.BottomSheetDialog</item>
        <item name="checkboxStyle">@style/Widget.MaterialComponents.CompoundButton.CheckBox</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="colorAccent">?colorSecondary</item>
        <item name="colorError">@color/design_dark_default_color_error</item>
        <item name="colorPrimary">@color/design_dark_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_dark_default_color_primary_dark</item>
        <item name="extendedFloatingActionButtonStyle">@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="listPopupWindowStyle">@style/Widget.MaterialComponents.PopupMenu.ListPopupWindow</item>
        <item name="materialAlertDialogTheme">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.Button.OutlinedButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialButtonToggleGroupStyle">@style/Widget.MaterialComponents.MaterialButtonToggleGroup</item>
        <item name="materialCalendarFullscreenTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen</item>
        <item name="materialCalendarStyle">@style/Widget.MaterialComponents.MaterialCalendar</item>
        <item name="materialCalendarTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="minTouchTargetSize">@dimen/mtrl_min_touch_target_size</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="popupMenuStyle">@style/Widget.MaterialComponents.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.MaterialComponents.CompoundButton.RadioButton</item>
        <item name="snackbarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Snackbar</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="switchStyle">@style/Widget.MaterialComponents.CompoundButton.Switch</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
        <item name="textAppearanceLargePopupMenu">?textAppearanceSubtitle1</item>
        <item name="textAppearanceSmallPopupMenu">?textAppearanceSubtitle1</item>
        <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">com.google.android.material.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Bridge" parent="@style/Platform.MaterialComponents">
        <item name="colorOnBackground">@color/design_dark_default_color_on_background</item>
        <item name="colorOnError">@color/design_dark_default_color_on_error</item>
        <item name="colorOnPrimary">@color/design_dark_default_color_on_primary</item>
        <item name="colorOnPrimarySurface">?colorOnSurface</item>
        <item name="colorOnSecondary">@color/design_dark_default_color_on_secondary</item>
        <item name="colorOnSurface">@color/design_dark_default_color_on_surface</item>
        <item name="colorPrimarySurface">?colorSurface</item>
        <item name="colorPrimaryVariant">@color/design_dark_default_color_primary_variant</item>
        <item name="colorSecondary">@color/design_dark_default_color_secondary</item>
        <item name="colorSecondaryVariant">@color/design_dark_default_color_secondary_variant</item>
        <item name="colorSurface">@color/design_dark_default_color_surface</item>
        <item name="elevationOverlayColor">?colorOnSurface</item>
        <item name="elevationOverlayEnabled">true</item>
        <item name="isMaterialTheme">true</item>
        <item name="popupMenuBackground">@drawable/mtrl_popupmenu_background_dark</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.MaterialComponents.LargeComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.MaterialComponents.MediumComponent</item>
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.MaterialComponents.SmallComponent</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Dialog" parent="@style/Base.V14.Theme.MaterialComponents.Dialog.Bridge">
        <item name="android:colorBackground">@color/design_dark_default_color_background</item>
        <item name="actionBarPopupTheme">@style/ThemeOverlay.MaterialComponents.Dark</item>
        <item name="actionBarSize">@dimen/mtrl_toolbar_default_height</item>
        <item name="actionBarStyle">@style/Widget.MaterialComponents.ActionBar.Surface</item>
        <item name="actionBarTheme">@style/ThemeOverlay.MaterialComponents.ActionBar.Surface</item>
        <item name="actionOverflowMenuStyle">@style/Widget.MaterialComponents.PopupMenu.Overflow</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
        <item name="appBarLayoutStyle">@style/Widget.MaterialComponents.AppBarLayout.Surface</item>
        <item name="badgeStyle">@style/Widget.MaterialComponents.Badge</item>
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
        <item name="checkboxStyle">@style/Widget.MaterialComponents.CompoundButton.CheckBox</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="colorAccent">?colorSecondary</item>
        <item name="colorError">@color/design_dark_default_color_error</item>
        <item name="colorPrimary">@color/design_dark_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_dark_default_color_primary_dark</item>
        <item name="colorSecondary">@color/design_dark_default_color_secondary</item>
        <item name="extendedFloatingActionButtonStyle">@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="listPopupWindowStyle">@style/Widget.MaterialComponents.PopupMenu.ListPopupWindow</item>
        <item name="materialAlertDialogTheme">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.Button.OutlinedButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialButtonToggleGroupStyle">@style/Widget.MaterialComponents.MaterialButtonToggleGroup</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="minTouchTargetSize">@dimen/mtrl_min_touch_target_size</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="popupMenuStyle">@style/Widget.MaterialComponents.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.MaterialComponents.CompoundButton.RadioButton</item>
        <item name="snackbarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Snackbar</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="switchStyle">@style/Widget.MaterialComponents.CompoundButton.Switch</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
        <item name="textAppearanceLargePopupMenu">?textAppearanceSubtitle1</item>
        <item name="textAppearanceSmallPopupMenu">?textAppearanceSubtitle1</item>
        <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">com.google.android.material.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Dialog.Bridge" parent="@style/Platform.MaterialComponents.Dialog">
        <item name="colorOnBackground">@color/design_dark_default_color_on_background</item>
        <item name="colorOnError">@color/design_dark_default_color_on_error</item>
        <item name="colorOnPrimary">@color/design_dark_default_color_on_primary</item>
        <item name="colorOnPrimarySurface">?colorOnSurface</item>
        <item name="colorOnSecondary">@color/design_dark_default_color_on_secondary</item>
        <item name="colorOnSurface">@color/design_dark_default_color_on_surface</item>
        <item name="colorPrimarySurface">?colorSurface</item>
        <item name="colorPrimaryVariant">@color/design_dark_default_color_primary_variant</item>
        <item name="colorSecondary">@color/design_dark_default_color_secondary</item>
        <item name="colorSecondaryVariant">@color/design_dark_default_color_secondary_variant</item>
        <item name="colorSurface">@color/design_dark_default_color_surface</item>
        <item name="elevationOverlayColor">?colorOnSurface</item>
        <item name="elevationOverlayEnabled">true</item>
        <item name="isMaterialTheme">true</item>
        <item name="popupMenuBackground">@drawable/mtrl_popupmenu_background_dark</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.MaterialComponents.LargeComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.MaterialComponents.MediumComponent</item>
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.MaterialComponents.SmallComponent</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light" parent="@style/Base.V14.Theme.MaterialComponents.Light.Bridge">
        <item name="android:colorBackground">@color/design_default_color_background</item>
        <item name="android:timePickerDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog</item>
        <item name="android:datePickerDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog</item>
        <item name="actionBarPopupTheme">@style/ThemeOverlay.MaterialComponents.Light</item>
        <item name="actionBarSize">@dimen/mtrl_toolbar_default_height</item>
        <item name="actionBarStyle">@style/Widget.MaterialComponents.Light.ActionBar.Solid</item>
        <item name="actionOverflowMenuStyle">@style/Widget.MaterialComponents.PopupMenu.Overflow</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
        <item name="appBarLayoutStyle">@style/Widget.MaterialComponents.AppBarLayout.Primary</item>
        <item name="badgeStyle">@style/Widget.MaterialComponents.Badge</item>
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
        <item name="bottomSheetDialogTheme">@style/ThemeOverlay.MaterialComponents.Light.BottomSheetDialog</item>
        <item name="checkboxStyle">@style/Widget.MaterialComponents.CompoundButton.CheckBox</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="colorAccent">?colorSecondary</item>
        <item name="colorError">@color/design_default_color_error</item>
        <item name="colorPrimary">@color/design_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
        <item name="extendedFloatingActionButtonStyle">@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="listPopupWindowStyle">@style/Widget.MaterialComponents.PopupMenu.ListPopupWindow</item>
        <item name="materialAlertDialogTheme">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.Button.OutlinedButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialButtonToggleGroupStyle">@style/Widget.MaterialComponents.MaterialButtonToggleGroup</item>
        <item name="materialCalendarFullscreenTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen</item>
        <item name="materialCalendarStyle">@style/Widget.MaterialComponents.MaterialCalendar</item>
        <item name="materialCalendarTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="minTouchTargetSize">@dimen/mtrl_min_touch_target_size</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="popupMenuStyle">@style/Widget.MaterialComponents.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.MaterialComponents.CompoundButton.RadioButton</item>
        <item name="snackbarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Snackbar</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="switchStyle">@style/Widget.MaterialComponents.CompoundButton.Switch</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
        <item name="textAppearanceLargePopupMenu">?textAppearanceSubtitle1</item>
        <item name="textAppearanceSmallPopupMenu">?textAppearanceSubtitle1</item>
        <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">com.google.android.material.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.Bridge" parent="@style/Platform.MaterialComponents.Light">
        <item name="colorOnBackground">@color/design_default_color_on_background</item>
        <item name="colorOnError">@color/design_default_color_on_error</item>
        <item name="colorOnPrimary">@color/design_default_color_on_primary</item>
        <item name="colorOnPrimarySurface">?colorOnPrimary</item>
        <item name="colorOnSecondary">@color/design_default_color_on_secondary</item>
        <item name="colorOnSurface">@color/design_default_color_on_surface</item>
        <item name="colorPrimarySurface">?colorPrimary</item>
        <item name="colorPrimaryVariant">@color/design_default_color_primary_variant</item>
        <item name="colorSecondary">@color/design_default_color_secondary</item>
        <item name="colorSecondaryVariant">@color/design_default_color_secondary_variant</item>
        <item name="colorSurface">@color/design_default_color_surface</item>
        <item name="elevationOverlayColor">?colorOnSurface</item>
        <item name="elevationOverlayEnabled">false</item>
        <item name="isMaterialTheme">true</item>
        <item name="popupMenuBackground">@drawable/mtrl_popupmenu_background</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.MaterialComponents.LargeComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.MaterialComponents.MediumComponent</item>
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.MaterialComponents.SmallComponent</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge" parent="@style/Theme.AppCompat.Light.DarkActionBar">
        <item name="colorOnBackground">@color/design_default_color_on_background</item>
        <item name="colorOnError">@color/design_default_color_on_error</item>
        <item name="colorOnPrimary">@color/design_default_color_on_primary</item>
        <item name="colorOnPrimarySurface">?colorOnPrimary</item>
        <item name="colorOnSecondary">@color/design_default_color_on_secondary</item>
        <item name="colorOnSurface">@color/design_default_color_on_surface</item>
        <item name="colorPrimarySurface">?colorPrimary</item>
        <item name="colorPrimaryVariant">@color/design_default_color_primary_variant</item>
        <item name="colorSecondary">@color/design_default_color_secondary</item>
        <item name="colorSecondaryVariant">@color/design_default_color_secondary_variant</item>
        <item name="colorSurface">@color/design_default_color_surface</item>
        <item name="elevationOverlayColor">?colorOnSurface</item>
        <item name="elevationOverlayEnabled">false</item>
        <item name="isMaterialTheme">true</item>
        <item name="popupMenuBackground">@drawable/mtrl_popupmenu_background</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.MaterialComponents.LargeComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.MaterialComponents.MediumComponent</item>
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.MaterialComponents.SmallComponent</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.Dialog" parent="@style/Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge">
        <item name="android:colorBackground">@color/design_default_color_background</item>
        <item name="actionBarPopupTheme">@style/ThemeOverlay.MaterialComponents.Light</item>
        <item name="actionBarSize">@dimen/mtrl_toolbar_default_height</item>
        <item name="actionBarStyle">@style/Widget.MaterialComponents.Light.ActionBar.Solid</item>
        <item name="actionOverflowMenuStyle">@style/Widget.MaterialComponents.PopupMenu.Overflow</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
        <item name="appBarLayoutStyle">@style/Widget.MaterialComponents.AppBarLayout.Primary</item>
        <item name="badgeStyle">@style/Widget.MaterialComponents.Badge</item>
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
        <item name="checkboxStyle">@style/Widget.MaterialComponents.CompoundButton.CheckBox</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="colorAccent">?colorSecondary</item>
        <item name="colorError">@color/design_default_color_error</item>
        <item name="colorPrimary">@color/design_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
        <item name="colorSecondary">@color/design_default_color_secondary</item>
        <item name="extendedFloatingActionButtonStyle">@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="listPopupWindowStyle">@style/Widget.MaterialComponents.PopupMenu.ListPopupWindow</item>
        <item name="materialAlertDialogTheme">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.Button.OutlinedButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialButtonToggleGroupStyle">@style/Widget.MaterialComponents.MaterialButtonToggleGroup</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="minTouchTargetSize">@dimen/mtrl_min_touch_target_size</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="popupMenuStyle">@style/Widget.MaterialComponents.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.MaterialComponents.CompoundButton.RadioButton</item>
        <item name="snackbarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Snackbar</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="switchStyle">@style/Widget.MaterialComponents.CompoundButton.Switch</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
        <item name="textAppearanceLargePopupMenu">?textAppearanceSubtitle1</item>
        <item name="textAppearanceSmallPopupMenu">?textAppearanceSubtitle1</item>
        <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">com.google.android.material.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge" parent="@style/Platform.MaterialComponents.Light.Dialog">
        <item name="colorOnBackground">@color/design_default_color_on_background</item>
        <item name="colorOnError">@color/design_default_color_on_error</item>
        <item name="colorOnPrimary">@color/design_default_color_on_primary</item>
        <item name="colorOnPrimarySurface">?colorOnPrimary</item>
        <item name="colorOnSecondary">@color/design_default_color_on_secondary</item>
        <item name="colorOnSurface">@color/design_default_color_on_surface</item>
        <item name="colorPrimarySurface">?colorPrimary</item>
        <item name="colorPrimaryVariant">@color/design_default_color_primary_variant</item>
        <item name="colorSecondary">@color/design_default_color_secondary</item>
        <item name="colorSecondaryVariant">@color/design_default_color_secondary_variant</item>
        <item name="colorSurface">@color/design_default_color_surface</item>
        <item name="elevationOverlayColor">?colorOnSurface</item>
        <item name="elevationOverlayEnabled">false</item>
        <item name="isMaterialTheme">true</item>
        <item name="popupMenuBackground">@drawable/mtrl_popupmenu_background</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.MaterialComponents.LargeComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.MaterialComponents.MediumComponent</item>
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.MaterialComponents.SmallComponent</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.Dialog" parent="@style/ThemeOverlay.AppCompat.Dialog">
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
    </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert" parent="@style/ThemeOverlay.AppCompat.Dialog.Alert">
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
    </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog" parent="@style/ThemeOverlay.AppCompat.Dialog.Alert">
        <item name="android:backgroundDimAmount">0.32</item>
        <item name="android:checkedTextViewStyle">@style/Widget.MaterialComponents.CheckedTextView</item>
        <item name="alertDialogStyle">@style/MaterialAlertDialog.MaterialComponents</item>
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="buttonBarNegativeButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="buttonBarNeutralButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
        <item name="buttonBarPositiveButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="materialAlertDialogBodyTextStyle">@style/MaterialAlertDialog.MaterialComponents.Body.Text</item>
    </style>
    <style name="Base.V21.Theme.AppCompat" parent="@style/Base.V7.Theme.AppCompat">
        <item name="android:colorControlNormal">?colorControlNormal</item>
        <item name="android:colorControlActivated">?colorControlActivated</item>
        <item name="android:colorButtonNormal">?colorButtonNormal</item>
        <item name="android:colorControlHighlight">?colorControlHighlight</item>
        <item name="android:colorPrimary">?colorPrimary</item>
        <item name="android:colorPrimaryDark">?colorPrimaryDark</item>
        <item name="android:colorAccent">?colorAccent</item>
        <item name="actionBarDivider">?android:actionBarDivider</item>
        <item name="actionBarItemBackground">@drawable/abc_action_bar_item_background_material</item>
        <item name="actionBarSize">?android:actionBarSize</item>
        <item name="actionButtonStyle">?android:actionButtonStyle</item>
        <item name="actionModeBackground">?android:actionModeBackground</item>
        <item name="actionModeCloseDrawable">?android:actionModeCloseDrawable</item>
        <item name="borderlessButtonStyle">?android:borderlessButtonStyle</item>
        <item name="buttonStyle">?android:buttonStyle</item>
        <item name="buttonStyleSmall">?android:buttonStyleSmall</item>
        <item name="checkboxStyle">?android:checkboxStyle</item>
        <item name="checkedTextViewStyle">?android:checkedTextViewStyle</item>
        <item name="dividerHorizontal">?android:dividerHorizontal</item>
        <item name="dividerVertical">?android:dividerVertical</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:editTextColor</item>
        <item name="homeAsUpIndicator">?android:homeAsUpIndicator</item>
        <item name="listChoiceBackgroundIndicator">?android:listChoiceBackgroundIndicator</item>
        <item name="listPreferredItemHeightSmall">?android:listPreferredItemHeightSmall</item>
        <item name="radioButtonStyle">?android:radioButtonStyle</item>
        <item name="ratingBarStyle">?android:ratingBarStyle</item>
        <item name="selectableItemBackground">?android:selectableItemBackground</item>
        <item name="selectableItemBackgroundBorderless">?android:selectableItemBackgroundBorderless</item>
        <item name="spinnerStyle">?android:spinnerStyle</item>
        <item name="textAppearanceLargePopupMenu">?android:textAppearanceLargePopupMenu</item>
        <item name="textAppearanceSmallPopupMenu">?android:textAppearanceSmallPopupMenu</item>
    </style>
    <style name="Base.V21.Theme.AppCompat.Dialog" parent="@style/Base.V7.Theme.AppCompat.Dialog">
        <item name="android:windowElevation">@dimen/abc_floating_window_z</item>
    </style>
    <style name="Base.V21.Theme.AppCompat.Light" parent="@style/Base.V7.Theme.AppCompat.Light">
        <item name="android:colorControlNormal">?colorControlNormal</item>
        <item name="android:colorControlActivated">?colorControlActivated</item>
        <item name="android:colorButtonNormal">?colorButtonNormal</item>
        <item name="android:colorControlHighlight">?colorControlHighlight</item>
        <item name="android:colorPrimary">?colorPrimary</item>
        <item name="android:colorPrimaryDark">?colorPrimaryDark</item>
        <item name="android:colorAccent">?colorAccent</item>
        <item name="actionBarDivider">?android:actionBarDivider</item>
        <item name="actionBarItemBackground">@drawable/abc_action_bar_item_background_material</item>
        <item name="actionBarSize">?android:actionBarSize</item>
        <item name="actionButtonStyle">?android:actionButtonStyle</item>
        <item name="actionModeBackground">?android:actionModeBackground</item>
        <item name="actionModeCloseDrawable">?android:actionModeCloseDrawable</item>
        <item name="borderlessButtonStyle">?android:borderlessButtonStyle</item>
        <item name="buttonStyle">?android:buttonStyle</item>
        <item name="buttonStyleSmall">?android:buttonStyleSmall</item>
        <item name="checkboxStyle">?android:checkboxStyle</item>
        <item name="checkedTextViewStyle">?android:checkedTextViewStyle</item>
        <item name="dividerHorizontal">?android:dividerHorizontal</item>
        <item name="dividerVertical">?android:dividerVertical</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:editTextColor</item>
        <item name="homeAsUpIndicator">?android:homeAsUpIndicator</item>
        <item name="listChoiceBackgroundIndicator">?android:listChoiceBackgroundIndicator</item>
        <item name="listPreferredItemHeightSmall">?android:listPreferredItemHeightSmall</item>
        <item name="radioButtonStyle">?android:radioButtonStyle</item>
        <item name="ratingBarStyle">?android:ratingBarStyle</item>
        <item name="selectableItemBackground">?android:selectableItemBackground</item>
        <item name="selectableItemBackgroundBorderless">?android:selectableItemBackgroundBorderless</item>
        <item name="spinnerStyle">?android:spinnerStyle</item>
        <item name="textAppearanceLargePopupMenu">?android:textAppearanceLargePopupMenu</item>
        <item name="textAppearanceSmallPopupMenu">?android:textAppearanceSmallPopupMenu</item>
    </style>
    <style name="Base.V21.Theme.AppCompat.Light.Dialog" parent="@style/Base.V7.Theme.AppCompat.Light.Dialog">
        <item name="android:windowElevation">@dimen/abc_floating_window_z</item>
    </style>
    <style name="Base.V21.ThemeOverlay.AppCompat.Dialog" parent="@style/Base.V7.ThemeOverlay.AppCompat.Dialog">
        <item name="android:windowElevation">@dimen/abc_floating_window_z</item>
    </style>
    <style name="Base.V7.Theme.AppCompat" parent="@style/Platform.AppCompat">
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>
        <item name="android:textViewStyle">@style/Widget.AppCompat.TextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Widget.Button</item>
        <item name="actionBarDivider">?dividerVertical</item>
        <item name="actionBarItemBackground">?selectableItemBackgroundBorderless</item>
        <item name="actionBarPopupTheme">@null</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarSplitStyle">?actionBarStyle</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.ActionBar.Solid</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.ActionBar.TabBar</item>
        <item name="actionBarTabStyle">@style/Widget.AppCompat.ActionBar.TabView</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.ActionBar.TabText</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Spinner.DropDown.ActionBar</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:textColorPrimaryDisableOnly</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>
        <item name="actionModeCloseDrawable">@drawable/abc_ic_ab_back_material</item>
        <item name="actionModeCopyDrawable">@drawable/abc_ic_menu_copy_mtrl_am_alpha</item>
        <item name="actionModeCutDrawable">@drawable/abc_ic_menu_cut_mtrl_alpha</item>
        <item name="actionModePasteDrawable">@drawable/abc_ic_menu_paste_mtrl_am_alpha</item>
        <item name="actionModeSelectAllDrawable">@drawable/abc_ic_menu_selectall_mtrl_alpha</item>
        <item name="actionModeShareDrawable">@drawable/abc_ic_menu_share_mtrl_alpha</item>
        <item name="actionModeSplitBackground">?colorPrimaryDark</item>
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.PopupMenu.Overflow</item>
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.AppCompat.Dialog.Alert</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarNegativeButtonStyle">?buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?buttonBarButtonStyle</item>
        <item name="buttonBarPositiveButtonStyle">?buttonBarButtonStyle</item>
        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="colorAccent">@color/accent_material_dark</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorControlActivated">?colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorControlNormal">?android:textColorSecondary</item>
        <item name="colorError">@color/error_color_material_dark</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>
        <item name="controlBackground">?selectableItemBackgroundBorderless</item>
        <item name="dialogCornerRadius">@dimen/abc_dialog_corner_radius_material</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogTheme">@style/ThemeOverlay.AppCompat.Dialog</item>
        <item name="dividerHorizontal">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="dividerVertical">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>
        <item name="dropDownListViewStyle">?android:dropDownListViewStyle</item>
        <item name="dropdownListPreferredItemHeight">?listPreferredItemHeightSmall</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:textColorPrimary</item>
        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="homeAsUpIndicator">@drawable/abc_ic_ab_back_material</item>
        <item name="imageButtonStyle">@style/Widget.AppCompat.ImageButton</item>
        <item name="isLightTheme">false</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>
        <item name="listDividerAlertDialog">@null</item>
        <item name="listMenuViewStyle">@style/Widget.AppCompat.ListMenuView</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="listPreferredItemHeight">@dimen/abc_list_item_height_material</item>
        <item name="listPreferredItemHeightLarge">@dimen/abc_list_item_height_large_material</item>
        <item name="listPreferredItemHeightSmall">@dimen/abc_list_item_height_small_material</item>
        <item name="listPreferredItemPaddingEnd">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingStart">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="panelBackground">@drawable/abc_menu_hardkey_panel_mtrl_mult</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="popupMenuStyle">@style/Widget.AppCompat.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="ratingBarStyleIndicator">@style/Widget.AppCompat.RatingBar.Indicator</item>
        <item name="ratingBarStyleSmall">@style/Widget.AppCompat.RatingBar.Small</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView</item>
        <item name="seekBarStyle">@style/Widget.AppCompat.SeekBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_dark</item>
        <item name="selectableItemBackgroundBorderless">?selectableItemBackground</item>
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Large</item>
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSecondary">@style/TextAppearance.AppCompat.Body1</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearancePopupMenuHeader">@style/TextAppearance.AppCompat.Widget.PopupMenu.Header</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Small</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_dark</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="tooltipForegroundColor">@color/foreground_material_light</item>
        <item name="tooltipFrameBackground">@drawable/tooltip_frame_light</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowActionModeOverlay">false</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowNoTitle">false</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Dialog" parent="@style/Base.Theme.AppCompat">
        <item name="android:colorBackground">?colorBackgroundFloating</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:listDivider">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowSoftInputMode">adjustPan</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="listPreferredItemPaddingLeft">24.0dip</item>
        <item name="listPreferredItemPaddingRight">24.0dip</item>
        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Light" parent="@style/Platform.AppCompat.Light">
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>
        <item name="android:textViewStyle">@style/Widget.AppCompat.TextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Widget.Button</item>
        <item name="actionBarDivider">?dividerVertical</item>
        <item name="actionBarItemBackground">?selectableItemBackgroundBorderless</item>
        <item name="actionBarPopupTheme">@null</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarSplitStyle">?actionBarStyle</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.Light.ActionBar.Solid</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.Light.ActionBar.TabBar</item>
        <item name="actionBarTabStyle">@style/Widget.AppCompat.Light.ActionBar.TabView</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.Light.ActionBar.TabText</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:textColorPrimaryDisableOnly</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>
        <item name="actionModeCloseDrawable">@drawable/abc_ic_ab_back_material</item>
        <item name="actionModeCopyDrawable">@drawable/abc_ic_menu_copy_mtrl_am_alpha</item>
        <item name="actionModeCutDrawable">@drawable/abc_ic_menu_cut_mtrl_alpha</item>
        <item name="actionModePasteDrawable">@drawable/abc_ic_menu_paste_mtrl_am_alpha</item>
        <item name="actionModeSelectAllDrawable">@drawable/abc_ic_menu_selectall_mtrl_alpha</item>
        <item name="actionModeShareDrawable">@drawable/abc_ic_menu_share_mtrl_alpha</item>
        <item name="actionModeSplitBackground">?colorPrimaryDark</item>
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.Light.PopupMenu.Overflow</item>
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat.Light</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.AppCompat.Dialog.Alert</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarNegativeButtonStyle">?buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?buttonBarButtonStyle</item>
        <item name="buttonBarPositiveButtonStyle">?buttonBarButtonStyle</item>
        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="colorAccent">@color/accent_material_light</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorControlActivated">?colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorControlNormal">?android:textColorSecondary</item>
        <item name="colorError">@color/error_color_material_light</item>
        <item name="colorPrimary">@color/primary_material_light</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_light</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>
        <item name="controlBackground">?selectableItemBackgroundBorderless</item>
        <item name="dialogCornerRadius">@dimen/abc_dialog_corner_radius_material</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogTheme">@style/ThemeOverlay.AppCompat.Dialog</item>
        <item name="dividerHorizontal">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="dividerVertical">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>
        <item name="dropDownListViewStyle">?android:dropDownListViewStyle</item>
        <item name="dropdownListPreferredItemHeight">?listPreferredItemHeightSmall</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:textColorPrimary</item>
        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="homeAsUpIndicator">@drawable/abc_ic_ab_back_material</item>
        <item name="imageButtonStyle">@style/Widget.AppCompat.ImageButton</item>
        <item name="isLightTheme">true</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_light</item>
        <item name="listDividerAlertDialog">@null</item>
        <item name="listMenuViewStyle">@style/Widget.AppCompat.ListMenuView</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="listPreferredItemHeight">@dimen/abc_list_item_height_material</item>
        <item name="listPreferredItemHeightLarge">@dimen/abc_list_item_height_large_material</item>
        <item name="listPreferredItemHeightSmall">@dimen/abc_list_item_height_small_material</item>
        <item name="listPreferredItemPaddingEnd">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingStart">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="panelBackground">@drawable/abc_menu_hardkey_panel_mtrl_mult</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="popupMenuStyle">@style/Widget.AppCompat.Light.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="ratingBarStyleIndicator">@style/Widget.AppCompat.RatingBar.Indicator</item>
        <item name="ratingBarStyleSmall">@style/Widget.AppCompat.RatingBar.Small</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.Light.SearchView</item>
        <item name="seekBarStyle">@style/Widget.AppCompat.SeekBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_light</item>
        <item name="selectableItemBackgroundBorderless">?selectableItemBackground</item>
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large</item>
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSecondary">@style/TextAppearance.AppCompat.Body1</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearancePopupMenuHeader">@style/TextAppearance.AppCompat.Widget.PopupMenu.Header</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_light</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="tooltipForegroundColor">@color/foreground_material_dark</item>
        <item name="tooltipFrameBackground">@drawable/tooltip_frame_dark</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowActionModeOverlay">false</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowNoTitle">false</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Light.Dialog" parent="@style/Base.Theme.AppCompat.Light">
        <item name="android:colorBackground">?colorBackgroundFloating</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:listDivider">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowSoftInputMode">adjustPan</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="listPreferredItemPaddingLeft">24.0dip</item>
        <item name="listPreferredItemPaddingRight">24.0dip</item>
        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>
    </style>
    <style name="Base.V7.ThemeOverlay.AppCompat.Dialog" parent="@style/Base.ThemeOverlay.AppCompat">
        <item name="android:colorBackground">?colorBackgroundFloating</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:listDivider">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowSoftInputMode">adjustPan</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="listPreferredItemPaddingLeft">24.0dip</item>
        <item name="listPreferredItemPaddingRight">24.0dip</item>
        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.AutoCompleteTextView" parent="@android:style/Widget.AutoCompleteTextView">
        <item name="android:textAppearance">?android:textAppearanceMediumInverse</item>
        <item name="android:textColor">?editTextColor</item>
        <item name="android:background">?editTextBackground</item>
        <item name="android:dropDownSelector">?listChoiceBackgroundIndicator</item>
        <item name="android:popupBackground">@drawable/abc_popup_background_mtrl_mult</item>
        <item name="android:textCursorDrawable">@drawable/abc_text_cursor_material</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.EditText" parent="@android:style/Widget.EditText">
        <item name="android:textAppearance">?android:textAppearanceMediumInverse</item>
        <item name="android:textColor">?editTextColor</item>
        <item name="android:background">?editTextBackground</item>
        <item name="android:textCursorDrawable">@drawable/abc_text_cursor_material</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.Toolbar" parent="@android:style/Widget">
        <item name="android:paddingLeft">@dimen/abc_action_bar_default_padding_start_material</item>
        <item name="android:paddingRight">@dimen/abc_action_bar_default_padding_end_material</item>
        <item name="android:minHeight">?actionBarSize</item>
        <item name="buttonGravity">top</item>
        <item name="collapseContentDescription">@string/abc_toolbar_collapse_description</item>
        <item name="collapseIcon">?homeAsUpIndicator</item>
        <item name="contentInsetStart">16.0dip</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="maxButtonHeight">@dimen/abc_action_bar_default_height_material</item>
        <item name="subtitleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle</item>
        <item name="titleMargin">4.0dip</item>
        <item name="titleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Title</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar" parent="">
        <item name="android:gravity">center_vertical</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>
        <item name="background">@null</item>
        <item name="backgroundSplit">@null</item>
        <item name="backgroundStacked">@null</item>
        <item name="contentInsetEnd">@dimen/abc_action_bar_content_inset_material</item>
        <item name="contentInsetStart">@dimen/abc_action_bar_content_inset_material</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="displayOptions">showTitle</item>
        <item name="divider">?dividerVertical</item>
        <item name="elevation">@dimen/abc_action_bar_elevation_material</item>
        <item name="height">?actionBarSize</item>
        <item name="popupTheme">?actionBarPopupTheme</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle</item>
        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Title</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.ActionBar">
        <item name="background">?colorPrimary</item>
        <item name="backgroundSplit">?colorPrimary</item>
        <item name="backgroundStacked">?colorPrimary</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabBar" parent="">
        <item name="divider">?actionBarDivider</item>
        <item name="dividerPadding">8.0dip</item>
        <item name="showDividers">middle</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabText" parent="@android:style/Widget.Material.ActionBar.TabText" />
    <style name="Base.Widget.AppCompat.ActionBar.TabView" parent="@android:style/Widget.Material.ActionBar.TabView" />
    <style name="Base.Widget.AppCompat.ActionButton" parent="@android:style/Widget.Material.ActionButton" />
    <style name="Base.Widget.AppCompat.ActionButton.CloseMode" parent="@android:style/Widget.Material.ActionButton.CloseMode">
        <item name="android:minWidth">56.0dip</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.Overflow" parent="@android:style/Widget.Material.ActionButton.Overflow">
        <item name="android:src">@null</item>
        <item name="srcCompat">@drawable/abc_ic_menu_overflow_material</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionMode" parent="">
        <item name="background">?actionModeBackground</item>
        <item name="backgroundSplit">?actionModeSplitBackground</item>
        <item name="closeItemLayout">@layout/abc_action_mode_close_item_material</item>
        <item name="height">?actionBarSize</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle</item>
        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Title</item>
    </style>
    <style name="Base.Widget.AppCompat.ActivityChooserView" parent="">
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/abc_ab_share_pack_mtrl_alpha</item>
        <item name="divider">?dividerVertical</item>
        <item name="dividerPadding">6.0dip</item>
        <item name="showDividers">middle</item>
    </style>
    <style name="Base.Widget.AppCompat.AutoCompleteTextView" parent="@android:style/Widget.Material.AutoCompleteTextView">
        <item name="android:background">?editTextBackground</item>
    </style>
    <style name="Base.Widget.AppCompat.Button" parent="@android:style/Widget.Material.Button" />
    <style name="Base.Widget.AppCompat.Button.Borderless" parent="@android:style/Widget.Material.Button.Borderless" />
    <style name="Base.Widget.AppCompat.Button.Borderless.Colored" parent="@android:style/Widget.Material.Button.Borderless.Colored">
        <item name="android:textColor">@color/abc_btn_colored_borderless_text_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog" parent="@style/Widget.AppCompat.Button.Borderless.Colored">
        <item name="android:minWidth">64.0dip</item>
        <item name="android:minHeight">@dimen/abc_alert_dialog_button_bar_height</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Colored" parent="@style/Base.Widget.AppCompat.Button">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.Button.Colored</item>
        <item name="android:background">@drawable/abc_btn_colored_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Small" parent="@android:style/Widget.Material.Button.Small" />
    <style name="Base.Widget.AppCompat.ButtonBar" parent="@android:style/Widget.Material.ButtonBar" />
    <style name="Base.Widget.AppCompat.ButtonBar.AlertDialog" parent="@style/Base.Widget.AppCompat.ButtonBar" />
    <style name="Base.Widget.AppCompat.CompoundButton.CheckBox" parent="@android:style/Widget.Material.CompoundButton.CheckBox" />
    <style name="Base.Widget.AppCompat.CompoundButton.RadioButton" parent="@android:style/Widget.Material.CompoundButton.RadioButton" />
    <style name="Base.Widget.AppCompat.CompoundButton.Switch" parent="@android:style/Widget.CompoundButton">
        <item name="android:background">?controlBackground</item>
        <item name="android:textOn">@string/abc_capital_on</item>
        <item name="android:textOff">@string/abc_capital_off</item>
        <item name="android:thumb">@drawable/abc_switch_thumb_material</item>
        <item name="showText">false</item>
        <item name="switchPadding">@dimen/abc_switch_padding</item>
        <item name="switchTextAppearance">@style/TextAppearance.AppCompat.Widget.Switch</item>
        <item name="track">@drawable/abc_switch_track_mtrl_alpha</item>
    </style>
    <style name="Base.Widget.AppCompat.DrawerArrowToggle" parent="@style/Base.Widget.AppCompat.DrawerArrowToggle.Common">
        <item name="barLength">18.0dip</item>
        <item name="drawableSize">24.0dip</item>
        <item name="gapBetweenBars">3.0dip</item>
    </style>
    <style name="Base.Widget.AppCompat.DrawerArrowToggle.Common" parent="">
        <item name="arrowHeadLength">8.0dip</item>
        <item name="arrowShaftLength">16.0dip</item>
        <item name="color">?android:textColorSecondary</item>
        <item name="spinBars">true</item>
        <item name="thickness">2.0dip</item>
    </style>
    <style name="Base.Widget.AppCompat.DropDownItem.Spinner" parent="@android:style/Widget.Material.DropDownItem.Spinner" />
    <style name="Base.Widget.AppCompat.EditText" parent="@android:style/Widget.Material.EditText">
        <item name="android:background">?editTextBackground</item>
    </style>
    <style name="Base.Widget.AppCompat.ImageButton" parent="@android:style/Widget.Material.ImageButton" />
    <style name="Base.Widget.AppCompat.Light.ActionBar" parent="@style/Base.Widget.AppCompat.ActionBar">
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.Light.ActionBar">
        <item name="background">?colorPrimary</item>
        <item name="backgroundSplit">?colorPrimary</item>
        <item name="backgroundStacked">?colorPrimary</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabBar" parent="@style/Base.Widget.AppCompat.ActionBar.TabBar" />
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText" parent="@android:style/Widget.Material.Light.ActionBar.TabText" />
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="@android:style/Widget.Material.Light.ActionBar.TabText" />
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabView" parent="@android:style/Widget.Material.Light.ActionBar.TabView" />
    <style name="Base.Widget.AppCompat.Light.PopupMenu" parent="@android:style/Widget.Material.Light.PopupMenu" />
    <style name="Base.Widget.AppCompat.Light.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.Light.PopupMenu">
        <item name="android:dropDownHorizontalOffset">-4.0dip</item>
        <item name="android:overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.ListMenuView" parent="@android:style/Widget">
        <item name="subMenuArrow">@drawable/abc_ic_arrow_drop_right_black_24dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ListPopupWindow" parent="@android:style/Widget.Material.ListPopupWindow" />
    <style name="Base.Widget.AppCompat.ListView" parent="@android:style/Widget.Material.ListView" />
    <style name="Base.Widget.AppCompat.ListView.DropDown" parent="@android:style/Widget.Material.ListView.DropDown" />
    <style name="Base.Widget.AppCompat.ListView.Menu" parent="@style/Base.Widget.AppCompat.ListView" />
    <style name="Base.Widget.AppCompat.PopupMenu" parent="@android:style/Widget.Material.PopupMenu" />
    <style name="Base.Widget.AppCompat.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.PopupMenu">
        <item name="android:dropDownHorizontalOffset">-4.0dip</item>
        <item name="android:overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.PopupWindow" parent="@android:style/Widget.PopupWindow" />
    <style name="Base.Widget.AppCompat.ProgressBar" parent="@android:style/Widget.Material.ProgressBar" />
    <style name="Base.Widget.AppCompat.ProgressBar.Horizontal" parent="@android:style/Widget.Material.ProgressBar.Horizontal" />
    <style name="Base.Widget.AppCompat.RatingBar" parent="@android:style/Widget.Material.RatingBar" />
    <style name="Base.Widget.AppCompat.RatingBar.Indicator" parent="@android:style/Widget.RatingBar">
        <item name="android:maxHeight">36.0dip</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_indicator_material</item>
        <item name="android:progressDrawable">@drawable/abc_ratingbar_indicator_material</item>
        <item name="android:minHeight">36.0dip</item>
        <item name="android:thumb">@null</item>
        <item name="android:isIndicator">true</item>
    </style>
    <style name="Base.Widget.AppCompat.RatingBar.Small" parent="@android:style/Widget.RatingBar">
        <item name="android:maxHeight">16.0dip</item>
        <item name="android:indeterminateDrawable">@drawable/abc_ratingbar_small_material</item>
        <item name="android:progressDrawable">@drawable/abc_ratingbar_small_material</item>
        <item name="android:minHeight">16.0dip</item>
        <item name="android:thumb">@null</item>
        <item name="android:isIndicator">true</item>
    </style>
    <style name="Base.Widget.AppCompat.SearchView" parent="@android:style/Widget">
        <item name="closeIcon">@drawable/abc_ic_clear_material</item>
        <item name="commitIcon">@drawable/abc_ic_commit_search_api_mtrl_alpha</item>
        <item name="goIcon">@drawable/abc_ic_go_search_api_material</item>
        <item name="layout">@layout/abc_search_view</item>
        <item name="queryBackground">@drawable/abc_textfield_search_material</item>
        <item name="searchHintIcon">@drawable/abc_ic_search_api_material</item>
        <item name="searchIcon">@drawable/abc_ic_search_api_material</item>
        <item name="submitBackground">@drawable/abc_textfield_search_material</item>
        <item name="suggestionRowLayout">@layout/abc_search_dropdown_item_icons_2line</item>
        <item name="voiceIcon">@drawable/abc_ic_voice_search_api_material</item>
    </style>
    <style name="Base.Widget.AppCompat.SearchView.ActionBar" parent="@style/Base.Widget.AppCompat.SearchView">
        <item name="defaultQueryHint">@string/abc_search_hint</item>
        <item name="queryBackground">@null</item>
        <item name="searchHintIcon">@null</item>
        <item name="submitBackground">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.SeekBar" parent="@android:style/Widget.Material.SeekBar" />
    <style name="Base.Widget.AppCompat.SeekBar.Discrete" parent="@style/Base.Widget.AppCompat.SeekBar">
        <item name="tickMark">@drawable/abc_seekbar_tick_mark_material</item>
    </style>
    <style name="Base.Widget.AppCompat.Spinner" parent="@android:style/Widget.Material.Spinner" />
    <style name="Base.Widget.AppCompat.Spinner.Underlined" parent="@style/Base.Widget.AppCompat.Spinner">
        <item name="android:background">@drawable/abc_spinner_textfield_background_material</item>
    </style>
    <style name="Base.Widget.AppCompat.TextView" parent="@android:style/Widget.Material.TextView" />
    <style name="Base.Widget.AppCompat.TextView.SpinnerItem" parent="@android:style/Widget.Material.TextView.SpinnerItem" />
    <style name="Base.Widget.AppCompat.Toolbar" parent="@style/Base.V7.Widget.AppCompat.Toolbar" />
    <style name="Base.Widget.AppCompat.Toolbar.Button.Navigation" parent="@android:style/Widget.Material.Toolbar.Button.Navigation" />
    <style name="Base.Widget.Design.TabLayout" parent="@android:style/Widget">
        <item name="android:background">@null</item>
        <item name="tabIconTint">@null</item>
        <item name="tabIndicator">@drawable/mtrl_tabs_default_indicator</item>
        <item name="tabIndicatorAnimationDuration">@integer/design_tab_indicator_anim_duration_ms</item>
        <item name="tabIndicatorColor">?colorAccent</item>
        <item name="tabIndicatorGravity">bottom</item>
        <item name="tabMaxWidth">@dimen/design_tab_max_width</item>
        <item name="tabPaddingEnd">12.0dip</item>
        <item name="tabPaddingStart">12.0dip</item>
        <item name="tabRippleColor">?colorControlHighlight</item>
        <item name="tabTextAppearance">@style/TextAppearance.Design.Tab</item>
        <item name="tabTextColor">@null</item>
        <item name="tabUnboundedRipple">false</item>
    </style>
    <style name="Base.Widget.MaterialComponents.AutoCompleteTextView" parent="@style/Widget.AppCompat.AutoCompleteTextView">
        <item name="android:textAppearance">?textAppearanceSubtitle1</item>
        <item name="android:background">@null</item>
        <item name="android:paddingLeft">12.0dip</item>
        <item name="android:paddingTop">16.0dip</item>
        <item name="android:paddingRight">12.0dip</item>
        <item name="android:paddingBottom">16.0dip</item>
        <item name="android:dropDownVerticalOffset">@dimen/mtrl_exposed_dropdown_menu_popup_vertical_offset</item>
        <item name="android:paddingStart">12.0dip</item>
        <item name="android:paddingEnd">12.0dip</item>
        <item name="android:popupElevation">@dimen/mtrl_exposed_dropdown_menu_popup_elevation</item>
    </style>
    <style name="Base.Widget.MaterialComponents.CheckedTextView" parent="@android:style/Widget.Material.CheckedTextView" />
    <style name="Base.Widget.MaterialComponents.Chip" parent="@android:style/Widget">
        <item name="android:textAppearance">?textAppearanceBody2</item>
        <item name="android:textColor">@color/mtrl_chip_text_color</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:text">@null</item>
        <item name="android:checkable">false</item>
        <item name="android:stateListAnimator">@animator/mtrl_chip_state_list_anim</item>
        <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_circle</item>
        <item name="checkedIconVisible">true</item>
        <item name="chipBackgroundColor">@color/mtrl_chip_background_color</item>
        <item name="chipEndPadding">6.0dip</item>
        <item name="chipIcon">@null</item>
        <item name="chipIconSize">24.0dip</item>
        <item name="chipIconVisible">true</item>
        <item name="chipMinHeight">32.0dip</item>
        <item name="chipMinTouchTargetSize">48.0dip</item>
        <item name="chipStartPadding">4.0dip</item>
        <item name="chipStrokeColor">?colorOnSurface</item>
        <item name="chipStrokeWidth">0.0dip</item>
        <item name="chipSurfaceColor">@color/mtrl_chip_surface_color</item>
        <item name="closeIcon">@drawable/ic_mtrl_chip_close_circle</item>
        <item name="closeIconEndPadding">2.0dip</item>
        <item name="closeIconSize">18.0dip</item>
        <item name="closeIconStartPadding">2.0dip</item>
        <item name="closeIconTint">@color/mtrl_chip_close_icon_tint</item>
        <item name="closeIconVisible">true</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="ensureMinTouchTargetSize">true</item>
        <item name="iconEndPadding">0.0dip</item>
        <item name="iconStartPadding">0.0dip</item>
        <item name="rippleColor">@color/mtrl_chip_ripple_color</item>
        <item name="shapeAppearance">?shapeAppearanceSmallComponent</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.Chip</item>
        <item name="textEndPadding">6.0dip</item>
        <item name="textStartPadding">8.0dip</item>
    </style>
    <style name="Base.Widget.MaterialComponents.PopupMenu" parent="@style/Widget.AppCompat.PopupMenu">
        <item name="android:dropDownVerticalOffset">1.0px</item>
        <item name="overlapAnchor">false</item>
    </style>
    <style name="Base.Widget.MaterialComponents.PopupMenu.ContextMenu" parent="@style/Widget.AppCompat.PopupMenu">
        <item name="android:overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow" parent="@style/Widget.AppCompat.ListPopupWindow">
        <item name="android:popupBackground">?popupMenuBackground</item>
        <item name="android:dropDownVerticalOffset">1.0dip</item>
        <item name="android:popupElevation">8.0dip</item>
    </style>
    <style name="Base.Widget.MaterialComponents.PopupMenu.Overflow" parent="@style/Widget.AppCompat.PopupMenu.Overflow">
        <item name="android:dropDownVerticalOffset">1.0px</item>
    </style>
    <style name="Base.Widget.MaterialComponents.TextInputEditText" parent="@style/Widget.AppCompat.EditText">
        <item name="android:textAppearance">?textAppearanceSubtitle1</item>
        <item name="android:background">@null</item>
        <item name="android:paddingLeft">12.0dip</item>
        <item name="android:paddingTop">16.0dip</item>
        <item name="android:paddingRight">12.0dip</item>
        <item name="android:paddingBottom">16.0dip</item>
        <item name="android:paddingStart">12.0dip</item>
        <item name="android:paddingEnd">12.0dip</item>
    </style>
    <style name="Base.Widget.MaterialComponents.TextInputLayout" parent="@style/Widget.Design.TextInputLayout">
        <item name="android:textColorHint">@color/mtrl_indicator_text_color</item>
        <item name="boxBackgroundColor">@null</item>
        <item name="boxBackgroundMode">outline</item>
        <item name="boxCollapsedPaddingTop">0.0dip</item>
        <item name="boxStrokeColor">@color/mtrl_outlined_stroke_color</item>
        <item name="boxStrokeWidth">@dimen/mtrl_textinput_box_stroke_width_default</item>
        <item name="boxStrokeWidthFocused">@dimen/mtrl_textinput_box_stroke_width_focused</item>
        <item name="counterOverflowTextAppearance">?textAppearanceCaption</item>
        <item name="counterOverflowTextColor">@color/mtrl_error</item>
        <item name="counterTextAppearance">?textAppearanceCaption</item>
        <item name="counterTextColor">@color/mtrl_indicator_text_color</item>
        <item name="endIconTint">@color/mtrl_outlined_icon_tint</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="errorIconDrawable">@drawable/mtrl_ic_error</item>
        <item name="errorIconTint">@color/mtrl_error</item>
        <item name="errorTextAppearance">?textAppearanceCaption</item>
        <item name="errorTextColor">@color/mtrl_error</item>
        <item name="helperTextTextAppearance">?textAppearanceCaption</item>
        <item name="helperTextTextColor">@color/mtrl_indicator_text_color</item>
        <item name="hintTextAppearance">?textAppearanceCaption</item>
        <item name="hintTextColor">?colorPrimary</item>
        <item name="shapeAppearance">?shapeAppearanceSmallComponent</item>
        <item name="shapeAppearanceOverlay">@null</item>
        <item name="startIconTint">@color/mtrl_outlined_icon_tint</item>
    </style>
    <style name="Base.Widget.MaterialComponents.TextView" parent="@style/Widget.AppCompat.TextView" />
    <style name="Base_Theme_AppCompat_Empty" />
    <style name="BottomSheetDialog" parent="@style/Theme.Design.BottomSheetDialog">
        <item name="android:backgroundDimAmount">0.7</item>
        <item name="android:background">@color/transparent</item>
        <item name="bottomSheetStyle">@style/BottomSheetDialogWrapper</item>
    </style>
    <style name="BottomSheetDialogWrapper" parent="@android:style/Widget">
        <item name="behavior_autoHide">true</item>
        <item name="behavior_peekHeight">auto</item>
        <item name="behavior_skipCollapsed">false</item>
    </style>
    <style name="CardView" parent="@style/Base.CardView" />
    <style name="CardView.Dark" parent="@style/CardView">
        <item name="cardBackgroundColor">@color/cardview_dark_background</item>
    </style>
    <style name="CardView.Light" parent="@style/CardView">
        <item name="cardBackgroundColor">@color/cardview_light_background</item>
    </style>
    <style name="EmptyTheme" />
    <style name="MaterialAlertDialog.MaterialComponents" parent="@style/AlertDialog.AppCompat">
        <item name="android:layout">@layout/mtrl_alert_dialog</item>
        <item name="backgroundInsetBottom">@dimen/mtrl_alert_dialog_background_inset_bottom</item>
        <item name="backgroundInsetEnd">@dimen/mtrl_alert_dialog_background_inset_end</item>
        <item name="backgroundInsetStart">@dimen/mtrl_alert_dialog_background_inset_start</item>
        <item name="backgroundInsetTop">@dimen/mtrl_alert_dialog_background_inset_top</item>
        <item name="listItemLayout">@layout/mtrl_alert_select_dialog_item</item>
        <item name="multiChoiceItemLayout">@layout/mtrl_alert_select_dialog_multichoice</item>
        <item name="shapeAppearance">?shapeAppearanceMediumComponent</item>
        <item name="singleChoiceItemLayout">@layout/mtrl_alert_select_dialog_singlechoice</item>
    </style>
    <style name="MaterialAlertDialog.MaterialComponents.Body.Text" parent="@style/TextAppearance.MaterialComponents.Body2">
        <item name="android:textAppearance">?textAppearanceBody2</item>
        <item name="android:textColor">@color/material_on_surface_emphasis_medium</item>
    </style>
    <style name="MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar" parent="@android:style/Widget.DeviceDefault.DatePicker">
        <item name="android:headerBackground">?colorPrimary</item>
        <item name="android:headerMonthTextAppearance">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text</item>
        <item name="android:headerDayOfMonthTextAppearance">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day</item>
        <item name="android:headerYearTextAppearance">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text</item>
        <item name="android:calendarTextColor">?colorOnSurface</item>
        <item name="android:datePickerMode">calendar</item>
        <item name="backgroundInsetBottom">@dimen/mtrl_alert_dialog_picker_background_inset</item>
        <item name="backgroundInsetTop">@dimen/mtrl_alert_dialog_picker_background_inset</item>
        <item name="shapeAppearance">?shapeAppearanceMediumComponent</item>
    </style>
    <style name="MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner" parent="@android:style/Widget.DeviceDefault.DatePicker">
        <item name="android:datePickerMode">spinner</item>
        <item name="backgroundInsetBottom">@dimen/mtrl_alert_dialog_picker_background_inset</item>
        <item name="backgroundInsetTop">@dimen/mtrl_alert_dialog_picker_background_inset</item>
        <item name="shapeAppearance">?shapeAppearanceMediumComponent</item>
    </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Icon" parent="@style/Base.MaterialAlertDialog.MaterialComponents.Title.Icon">
        <item name="android:layout_gravity">start|center</item>
        <item name="android:layout_marginRight">8.0dip</item>
        <item name="android:layout_marginEnd">8.0dip</item>
    </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked" parent="@style/Base.MaterialAlertDialog.MaterialComponents.Title.Icon">
        <item name="android:layout_gravity">center</item>
    </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Panel" parent="@style/Base.MaterialAlertDialog.MaterialComponents.Title.Panel">
        <item name="android:orientation">horizontal</item>
    </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked" parent="@style/Base.MaterialAlertDialog.MaterialComponents.Title.Panel">
        <item name="android:orientation">vertical</item>
    </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Text" parent="@style/Base.MaterialAlertDialog.MaterialComponents.Title.Text">
        <item name="android:layout_gravity">start|center</item>
        <item name="android:textAlignment">viewStart</item>
    </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked" parent="@style/Base.MaterialAlertDialog.MaterialComponents.Title.Text">
        <item name="android:layout_gravity">center</item>
        <item name="android:textAlignment">center</item>
    </style>
    <style name="MediaButtonCompat">
        <item name="android:background">@null</item>
        <item name="android:layout_width">71.0dip</item>
        <item name="android:layout_height">52.0dip</item>
    </style>
    <style name="MediaButtonCompat.Ffwd" parent="@style/MediaButtonCompat">
        <item name="android:src">@drawable/vvc_ic_media_ff</item>
    </style>
    <style name="MediaButtonCompat.Next" parent="@style/MediaButtonCompat">
        <item name="android:src">@drawable/vvc_ic_media_next</item>
    </style>
    <style name="MediaButtonCompat.Pause" parent="@style/MediaButtonCompat">
        <item name="android:src">@drawable/vvc_ic_media_pause</item>
    </style>
    <style name="MediaButtonCompat.Play" parent="@style/MediaButtonCompat">
        <item name="android:src">@drawable/vvc_ic_media_play</item>
    </style>
    <style name="MediaButtonCompat.Previous" parent="@style/MediaButtonCompat">
        <item name="android:src">@drawable/vvc_ic_media_previous</item>
    </style>
    <style name="MediaButtonCompat.Rew" parent="@style/MediaButtonCompat">
        <item name="android:src">@drawable/vvc_ic_media_rew</item>
    </style>
    <style name="MyEditText" parent="@style/Theme.AppCompat.Light">
        <item name="colorControlActivated">@color/colorThemBlue</item>
        <item name="colorControlNormal">@color/colorGrayEC</item>
    </style>
    <style name="NormalBottomSheetDialog" parent="@style/BottomSheetDialog">
        <item name="android:backgroundDimAmount">0.5</item>
    </style>
    <style name="Platform.AppCompat" parent="@style/Platform.V21.AppCompat" />
    <style name="Platform.AppCompat.Light" parent="@style/Platform.V21.AppCompat.Light" />
    <style name="Platform.MaterialComponents" parent="@style/Theme.AppCompat" />
    <style name="Platform.MaterialComponents.Dialog" parent="@style/Theme.AppCompat.Dialog" />
    <style name="Platform.MaterialComponents.Light" parent="@style/Theme.AppCompat.Light" />
    <style name="Platform.MaterialComponents.Light.Dialog" parent="@style/Theme.AppCompat.Light.Dialog" />
    <style name="Platform.ThemeOverlay.AppCompat" parent="">
        <item name="android:colorControlNormal">?colorControlNormal</item>
        <item name="android:colorControlActivated">?colorControlActivated</item>
        <item name="android:colorButtonNormal">?colorButtonNormal</item>
        <item name="android:colorControlHighlight">?colorControlHighlight</item>
        <item name="android:colorPrimary">?colorPrimary</item>
        <item name="android:colorPrimaryDark">?colorPrimaryDark</item>
        <item name="android:colorAccent">?colorAccent</item>
    </style>
    <style name="Platform.ThemeOverlay.AppCompat.Dark" parent="@style/Platform.ThemeOverlay.AppCompat" />
    <style name="Platform.ThemeOverlay.AppCompat.Light" parent="@style/Platform.ThemeOverlay.AppCompat" />
    <style name="Platform.V21.AppCompat" parent="@android:style/Theme.Material.NoActionBar">
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorLink">?android:colorAccent</item>
        <item name="android:buttonBarStyle">?buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?buttonBarButtonStyle</item>
        <item name="android:textColorLinkInverse">?android:colorAccent</item>
    </style>
    <style name="Platform.V21.AppCompat.Light" parent="@android:style/Theme.Material.Light.NoActionBar">
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:textColorLink">?android:colorAccent</item>
        <item name="android:buttonBarStyle">?buttonBarStyle</item>
        <item name="android:buttonBarButtonStyle">?buttonBarButtonStyle</item>
        <item name="android:textColorLinkInverse">?android:colorAccent</item>
    </style>
    <style name="Platform.Widget.AppCompat.Spinner" parent="@android:style/Widget.Holo.Spinner" />
    <style name="Preference">
        <item name="android:layout">@layout/preference</item>
    </style>
    <style name="Preference.Category" parent="@style/Preference">
        <item name="android:layout">@layout/preference_category</item>
        <item name="android:selectable">false</item>
        <item name="android:shouldDisableView">false</item>
    </style>
    <style name="Preference.Category.Material" parent="@style/Preference.Category">
        <item name="android:layout">@layout/preference_category_material</item>
        <item name="allowDividerAbove">true</item>
        <item name="allowDividerBelow">true</item>
        <item name="iconSpaceReserved">@bool/config_materialPreferenceIconSpaceReserved</item>
    </style>
    <style name="Preference.CheckBoxPreference" parent="@style/Preference">
        <item name="android:widgetLayout">@layout/preference_widget_checkbox</item>
    </style>
    <style name="Preference.CheckBoxPreference.Material" parent="@style/Preference.CheckBoxPreference">
        <item name="android:layout">@layout/preference_material</item>
        <item name="allowDividerAbove">false</item>
        <item name="allowDividerBelow">true</item>
        <item name="iconSpaceReserved">@bool/config_materialPreferenceIconSpaceReserved</item>
    </style>
    <style name="Preference.DialogPreference" parent="@style/Preference">
        <item name="android:positiveButtonText">@android:string/ok</item>
        <item name="android:negativeButtonText">@android:string/cancel</item>
    </style>
    <style name="Preference.DialogPreference.EditTextPreference" parent="@style/Preference.DialogPreference">
        <item name="android:dialogLayout">@layout/preference_dialog_edittext</item>
    </style>
    <style name="Preference.DialogPreference.EditTextPreference.Material" parent="@style/Preference.DialogPreference.EditTextPreference">
        <item name="android:layout">@layout/preference_material</item>
        <item name="allowDividerAbove">false</item>
        <item name="allowDividerBelow">true</item>
        <item name="iconSpaceReserved">@bool/config_materialPreferenceIconSpaceReserved</item>
        <item name="singleLineTitle">false</item>
    </style>
    <style name="Preference.DialogPreference.Material" parent="@style/Preference.DialogPreference">
        <item name="android:layout">@layout/preference_material</item>
        <item name="allowDividerAbove">false</item>
        <item name="allowDividerBelow">true</item>
        <item name="iconSpaceReserved">@bool/config_materialPreferenceIconSpaceReserved</item>
    </style>
    <style name="Preference.DropDown" parent="@style/Preference">
        <item name="android:layout">@layout/preference_dropdown</item>
    </style>
    <style name="Preference.DropDown.Material" parent="@style/Preference.DropDown">
        <item name="android:layout">@layout/preference_dropdown_material</item>
        <item name="allowDividerAbove">false</item>
        <item name="allowDividerBelow">true</item>
        <item name="iconSpaceReserved">@bool/config_materialPreferenceIconSpaceReserved</item>
    </style>
    <style name="Preference.Information" parent="@style/Preference">
        <item name="android:enabled">false</item>
        <item name="android:layout">@layout/preference_information</item>
        <item name="android:shouldDisableView">false</item>
    </style>
    <style name="Preference.Information.Material" parent="@style/Preference.Information">
        <item name="android:enabled">false</item>
        <item name="android:layout">@layout/preference_information_material</item>
        <item name="android:shouldDisableView">false</item>
    </style>
    <style name="Preference.Material" parent="@style/Preference">
        <item name="android:layout">@layout/preference_material</item>
        <item name="allowDividerAbove">false</item>
        <item name="allowDividerBelow">true</item>
        <item name="iconSpaceReserved">@bool/config_materialPreferenceIconSpaceReserved</item>
        <item name="singleLineTitle">false</item>
    </style>
    <style name="Preference.PreferenceScreen" parent="@style/Preference" />
    <style name="Preference.PreferenceScreen.Material" parent="@style/Preference.PreferenceScreen">
        <item name="android:layout">@layout/preference_material</item>
        <item name="allowDividerAbove">false</item>
        <item name="allowDividerBelow">true</item>
        <item name="iconSpaceReserved">@bool/config_materialPreferenceIconSpaceReserved</item>
    </style>
    <style name="Preference.SeekBarPreference" parent="@style/Preference">
        <item name="android:layout">@layout/preference_widget_seekbar</item>
        <item name="adjustable">true</item>
        <item name="showSeekBarValue">true</item>
        <item name="updatesContinuously">false</item>
    </style>
    <style name="Preference.SeekBarPreference.Material" parent="@style/Preference.SeekBarPreference">
        <item name="android:layout">@layout/preference_widget_seekbar_material</item>
        <item name="adjustable">true</item>
        <item name="allowDividerAbove">false</item>
        <item name="allowDividerBelow">true</item>
        <item name="iconSpaceReserved">@bool/config_materialPreferenceIconSpaceReserved</item>
        <item name="showSeekBarValue">false</item>
    </style>
    <style name="Preference.SwitchPreference" parent="@style/Preference">
        <item name="android:widgetLayout">@layout/preference_widget_switch</item>
        <item name="android:switchTextOn">@string/v7_preference_on</item>
        <item name="android:switchTextOff">@string/v7_preference_off</item>
    </style>
    <style name="Preference.SwitchPreference.Material" parent="@style/Preference.SwitchPreference">
        <item name="android:layout">@layout/preference_material</item>
        <item name="allowDividerAbove">false</item>
        <item name="allowDividerBelow">true</item>
        <item name="iconSpaceReserved">@bool/config_materialPreferenceIconSpaceReserved</item>
        <item name="singleLineTitle">false</item>
    </style>
    <style name="Preference.SwitchPreferenceCompat" parent="@style/Preference">
        <item name="android:widgetLayout">@layout/preference_widget_switch_compat</item>
        <item name="android:switchTextOn">@string/v7_preference_on</item>
        <item name="android:switchTextOff">@string/v7_preference_off</item>
    </style>
    <style name="Preference.SwitchPreferenceCompat.Material" parent="@style/Preference.SwitchPreferenceCompat">
        <item name="android:layout">@layout/preference_material</item>
        <item name="allowDividerAbove">false</item>
        <item name="allowDividerBelow">true</item>
        <item name="iconSpaceReserved">@bool/config_materialPreferenceIconSpaceReserved</item>
    </style>
    <style name="PreferenceCategoryTitleTextStyle">
        <item name="android:textAppearance">?preferenceCategoryTitleTextAppearance</item>
        <item name="android:textColor">?android:colorAccent</item>
    </style>
    <style name="PreferenceFragment">
        <item name="android:paddingLeft">0.0dip</item>
        <item name="android:paddingRight">0.0dip</item>
        <item name="android:divider">?android:listDivider</item>
        <item name="android:paddingStart">0.0dip</item>
        <item name="android:paddingEnd">0.0dip</item>
    </style>
    <style name="PreferenceFragment.Material" parent="@style/PreferenceFragment">
        <item name="android:divider">@drawable/preference_list_divider_material</item>
        <item name="allowDividerAfterLastItem">false</item>
    </style>
    <style name="PreferenceFragmentList">
        <item name="android:paddingLeft">16.0dip</item>
        <item name="android:paddingRight">16.0dip</item>
        <item name="android:paddingStart">16.0dip</item>
        <item name="android:paddingEnd">16.0dip</item>
    </style>
    <style name="PreferenceFragmentList.Material" parent="@style/PreferenceFragmentList">
        <item name="android:paddingLeft">0.0dip</item>
        <item name="android:paddingRight">0.0dip</item>
        <item name="android:paddingStart">0.0dip</item>
        <item name="android:paddingEnd">0.0dip</item>
    </style>
    <style name="PreferenceSummaryTextStyle">
        <item name="android:textAppearance">?android:textAppearanceListItemSecondary</item>
    </style>
    <style name="PreferenceThemeOverlay">
        <item name="android:scrollbars">vertical</item>
        <item name="checkBoxPreferenceStyle">@style/Preference.CheckBoxPreference.Material</item>
        <item name="dialogPreferenceStyle">@style/Preference.DialogPreference.Material</item>
        <item name="dropdownPreferenceStyle">@style/Preference.DropDown.Material</item>
        <item name="editTextPreferenceStyle">@style/Preference.DialogPreference.EditTextPreference.Material</item>
        <item name="preferenceCategoryStyle">@style/Preference.Category.Material</item>
        <item name="preferenceCategoryTitleTextAppearance">@style/TextAppearance.AppCompat.Body2</item>
        <item name="preferenceFragmentCompatStyle">@style/PreferenceFragment.Material</item>
        <item name="preferenceFragmentListStyle">@style/PreferenceFragmentList.Material</item>
        <item name="preferenceFragmentStyle">@style/PreferenceFragment.Material</item>
        <item name="preferenceScreenStyle">@style/Preference.PreferenceScreen.Material</item>
        <item name="preferenceStyle">@style/Preference.Material</item>
        <item name="seekBarPreferenceStyle">@style/Preference.SeekBarPreference.Material</item>
        <item name="switchPreferenceCompatStyle">@style/Preference.SwitchPreferenceCompat.Material</item>
        <item name="switchPreferenceStyle">@style/Preference.SwitchPreference.Material</item>
    </style>
    <style name="PreferenceThemeOverlay.v14" parent="@style/PreferenceThemeOverlay" />
    <style name="PreferenceThemeOverlay.v14.Material" parent="@style/PreferenceThemeOverlay.v14" />
    <style name="ProgressDialog" parent="@style/Theme.AppCompat.Light.Dialog">
        <item name="android:textSize">@dimen/sp_14</item>
        <item name="android:textColor">@color/black</item>
        <item name="colorAccent">@color/primaryColor</item>
    </style>
    <style name="RtlOverlay.DialogWindowTitle.AppCompat" parent="@style/Base.DialogWindowTitle.AppCompat">
        <item name="android:textAlignment">viewStart</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem" parent="@android:style/Widget">
        <item name="android:layout_gravity">start|center</item>
        <item name="android:paddingEnd">8.0dip</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.DialogTitle.Icon" parent="@android:style/Widget">
        <item name="android:layout_marginEnd">8.0dip</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem" parent="@android:style/Widget">
        <item name="android:paddingEnd">16.0dip</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup" parent="@android:style/Widget">
        <item name="android:layout_marginStart">16.0dip</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut" parent="@android:style/Widget">
        <item name="android:textAlignment">viewEnd</item>
        <item name="android:layout_marginStart">16.0dip</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow" parent="@android:style/Widget">
        <item name="android:layout_marginStart">8.0dip</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text" parent="@android:style/Widget">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:layout_alignParentStart">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Title" parent="@android:style/Widget">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:layout_marginStart">16.0dip</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown" parent="@android:style/Widget">
        <item name="android:paddingStart">@dimen/abc_dropdownitem_text_padding_left</item>
        <item name="android:paddingEnd">4.0dip</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" parent="@android:style/Widget">
        <item name="android:layout_alignParentStart">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" parent="@android:style/Widget">
        <item name="android:layout_toStartOf">@id/edit_query</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query" parent="@android:style/Widget">
        <item name="android:layout_alignParentEnd">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text" parent="@style/Base.Widget.AppCompat.DropDownItem.Spinner">
        <item name="android:layout_toStartOf">@android:id/icon2</item>
        <item name="android:layout_toEndOf">@android:id/icon1</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon" parent="@android:style/Widget">
        <item name="android:layout_marginStart">@dimen/abc_dropdownitem_text_padding_left</item>
    </style>
    <style name="RtlUnderlay.Widget.AppCompat.ActionButton" parent="@android:style/Widget">
        <item name="android:paddingStart">12.0dip</item>
        <item name="android:paddingEnd">12.0dip</item>
    </style>
    <style name="RtlUnderlay.Widget.AppCompat.ActionButton.Overflow" parent="@style/Base.Widget.AppCompat.ActionButton">
        <item name="android:paddingStart">@dimen/abc_action_bar_overflow_padding_start_material</item>
        <item name="android:paddingEnd">@dimen/abc_action_bar_overflow_padding_end_material</item>
    </style>
    <style name="ShapeAppearance.MaterialComponents" parent="">
        <item name="cornerFamily">rounded</item>
    </style>
    <style name="ShapeAppearance.MaterialComponents.LargeComponent" parent="@style/ShapeAppearance.MaterialComponents">
        <item name="cornerSize">@dimen/mtrl_shape_corner_size_large_component</item>
    </style>
    <style name="ShapeAppearance.MaterialComponents.MediumComponent" parent="@style/ShapeAppearance.MaterialComponents">
        <item name="cornerSize">@dimen/mtrl_shape_corner_size_medium_component</item>
    </style>
    <style name="ShapeAppearance.MaterialComponents.SmallComponent" parent="@style/ShapeAppearance.MaterialComponents">
        <item name="cornerSize">@dimen/mtrl_shape_corner_size_small_component</item>
    </style>
    <style name="ShapeAppearance.MaterialComponents.Test" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">10.0px</item>
    </style>
    <style name="ShapeAppearanceOverlay" />
    <style name="ShapeAppearanceOverlay.BottomLeftDifferentCornerSize" parent="@style/ShapeAppearanceOverlay">
        <item name="cornerSizeBottomLeft">20.0px</item>
    </style>
    <style name="ShapeAppearanceOverlay.BottomRightCut" parent="@style/ShapeAppearanceOverlay">
        <item name="cornerFamilyBottomRight">cut</item>
    </style>
    <style name="ShapeAppearanceOverlay.Cut" parent="@style/ShapeAppearanceOverlay">
        <item name="cornerFamily">cut</item>
    </style>
    <style name="ShapeAppearanceOverlay.DifferentCornerSize" parent="@style/ShapeAppearanceOverlay">
        <item name="cornerSize">20.0px</item>
    </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.BottomSheet" parent="">
        <item name="cornerSizeBottomLeft">0.0dip</item>
        <item name="cornerSizeBottomRight">0.0dip</item>
    </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.Chip" parent="">
        <item name="cornerSize">50.0%</item>
    </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton" parent="">
        <item name="cornerSize">@null</item>
    </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton" parent="">
        <item name="cornerSize">50.0%</item>
    </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day" parent="">
        <item name="cornerSize">@dimen/mtrl_calendar_day_corner</item>
    </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen" parent="">
        <item name="cornerSize">0.0dip</item>
    </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year" parent="">
        <item name="cornerSize">@dimen/mtrl_calendar_year_corner</item>
    </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox" parent="">
        <item name="cornerSizeBottomLeft">@dimen/mtrl_textinput_box_corner_radius_small</item>
        <item name="cornerSizeBottomRight">@dimen/mtrl_textinput_box_corner_radius_small</item>
    </style>
    <style name="ShapeAppearanceOverlay.TopLeftCut" parent="@style/ShapeAppearanceOverlay">
        <item name="cornerFamilyTopLeft">cut</item>
    </style>
    <style name="ShapeAppearanceOverlay.TopRightDifferentCornerSize" parent="@style/ShapeAppearanceOverlay">
        <item name="cornerSizeTopRight">20.0px</item>
    </style>
    <style name="Test.ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day" parent="">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">@dimen/test_mtrl_calendar_day_cornerSize</item>
    </style>
    <style name="Test.Theme.MaterialComponents.MaterialCalendar" parent="@style/Theme.MaterialComponents.Light">
        <item name="materialCalendarStyle">@style/Test.Widget.MaterialComponents.MaterialCalendar</item>
    </style>
    <style name="Test.Widget.MaterialComponents.MaterialCalendar" parent="">
        <item name="dayInvalidStyle">@style/Test.Widget.MaterialComponents.MaterialCalendar.Day</item>
        <item name="daySelectedStyle">@style/Test.Widget.MaterialComponents.MaterialCalendar.Day.Selected</item>
        <item name="dayStyle">@style/Test.Widget.MaterialComponents.MaterialCalendar.Day</item>
        <item name="dayTodayStyle">@style/Test.Widget.MaterialComponents.MaterialCalendar.Day</item>
        <item name="rangeFillColor">@color/test_mtrl_calendar_day</item>
        <item name="yearSelectedStyle">@style/Test.Widget.MaterialComponents.MaterialCalendar.Day.Selected</item>
        <item name="yearStyle">@style/Test.Widget.MaterialComponents.MaterialCalendar.Day</item>
        <item name="yearTodayStyle">@style/Test.Widget.MaterialComponents.MaterialCalendar.Day</item>
    </style>
    <style name="Test.Widget.MaterialComponents.MaterialCalendar.Day" parent="">
        <item name="itemFillColor">@color/test_mtrl_calendar_day</item>
        <item name="itemShapeAppearance">@style/Test.ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day</item>
        <item name="itemShapeAppearanceOverlay">@style/Test.ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day</item>
        <item name="itemStrokeColor">@color/test_mtrl_calendar_day</item>
        <item name="itemStrokeWidth">0.0dip</item>
        <item name="itemTextColor">@color/test_mtrl_calendar_day</item>
    </style>
    <style name="Test.Widget.MaterialComponents.MaterialCalendar.Day.Selected" parent="">
        <item name="itemFillColor">@color/test_mtrl_calendar_day_selected</item>
        <item name="itemShapeAppearance">@style/Test.ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day</item>
        <item name="itemShapeAppearanceOverlay">@style/Test.ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day</item>
        <item name="itemStrokeColor">@color/test_mtrl_calendar_day</item>
        <item name="itemStrokeWidth">0.0dip</item>
        <item name="itemTextColor">@color/test_mtrl_calendar_day_selected</item>
    </style>
    <style name="TestStyleWithLineHeight" parent="@style/TestStyleWithoutLineHeight">
        <item name="lineHeight">@dimen/material_text_view_test_line_height</item>
    </style>
    <style name="TestStyleWithLineHeightAppearance">
        <item name="android:textAppearance">@style/TestStyleWithLineHeight</item>
    </style>
    <style name="TestStyleWithThemeLineHeightAttribute" parent="@style/TestStyleWithoutLineHeight">
        <item name="lineHeight">?themeLineHeight</item>
    </style>
    <style name="TestStyleWithoutLineHeight" parent="@style/TextAppearance.AppCompat.Title">
        <item name="android:textSize">20.0sp</item>
    </style>
    <style name="TestThemeWithLineHeight" parent="@style/Theme.AppCompat.Light">
        <item name="themeLineHeight">@dimen/material_text_view_test_line_height</item>
    </style>
    <style name="TestThemeWithLineHeightDisabled" parent="@style/TestThemeWithLineHeight">
        <item name="textAppearanceLineHeightEnabled">false</item>
    </style>
    <style name="TextAppearance.AppCompat" parent="@style/Base.TextAppearance.AppCompat" />
    <style name="TextAppearance.AppCompat.Body1" parent="@style/Base.TextAppearance.AppCompat.Body1" />
    <style name="TextAppearance.AppCompat.Body2" parent="@style/Base.TextAppearance.AppCompat.Body2" />
    <style name="TextAppearance.AppCompat.Button" parent="@style/Base.TextAppearance.AppCompat.Button" />
    <style name="TextAppearance.AppCompat.Caption" parent="@style/Base.TextAppearance.AppCompat.Caption" />
    <style name="TextAppearance.AppCompat.Display1" parent="@style/Base.TextAppearance.AppCompat.Display1" />
    <style name="TextAppearance.AppCompat.Display2" parent="@style/Base.TextAppearance.AppCompat.Display2" />
    <style name="TextAppearance.AppCompat.Display3" parent="@style/Base.TextAppearance.AppCompat.Display3" />
    <style name="TextAppearance.AppCompat.Display4" parent="@style/Base.TextAppearance.AppCompat.Display4" />
    <style name="TextAppearance.AppCompat.Headline" parent="@style/Base.TextAppearance.AppCompat.Headline" />
    <style name="TextAppearance.AppCompat.Inverse" parent="@style/Base.TextAppearance.AppCompat.Inverse" />
    <style name="TextAppearance.AppCompat.Large" parent="@style/Base.TextAppearance.AppCompat.Large" />
    <style name="TextAppearance.AppCompat.Large.Inverse" parent="@style/Base.TextAppearance.AppCompat.Large.Inverse" />
    <style name="TextAppearance.AppCompat.Light.SearchResult.Subtitle" parent="@style/TextAppearance.AppCompat.SearchResult.Subtitle" />
    <style name="TextAppearance.AppCompat.Light.SearchResult.Title" parent="@style/TextAppearance.AppCompat.SearchResult.Title" />
    <style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Large" parent="@style/TextAppearance.AppCompat.Widget.PopupMenu.Large" />
    <style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Small" parent="@style/TextAppearance.AppCompat.Widget.PopupMenu.Small" />
    <style name="TextAppearance.AppCompat.Medium" parent="@style/Base.TextAppearance.AppCompat.Medium" />
    <style name="TextAppearance.AppCompat.Medium.Inverse" parent="@style/Base.TextAppearance.AppCompat.Medium.Inverse" />
    <style name="TextAppearance.AppCompat.Menu" parent="@style/Base.TextAppearance.AppCompat.Menu" />
    <style name="TextAppearance.AppCompat.SearchResult.Subtitle" parent="@style/Base.TextAppearance.AppCompat.SearchResult.Subtitle" />
    <style name="TextAppearance.AppCompat.SearchResult.Title" parent="@style/Base.TextAppearance.AppCompat.SearchResult.Title" />
    <style name="TextAppearance.AppCompat.Small" parent="@style/Base.TextAppearance.AppCompat.Small" />
    <style name="TextAppearance.AppCompat.Small.Inverse" parent="@style/Base.TextAppearance.AppCompat.Small.Inverse" />
    <style name="TextAppearance.AppCompat.Subhead" parent="@style/Base.TextAppearance.AppCompat.Subhead" />
    <style name="TextAppearance.AppCompat.Subhead.Inverse" parent="@style/Base.TextAppearance.AppCompat.Subhead.Inverse" />
    <style name="TextAppearance.AppCompat.Title" parent="@style/Base.TextAppearance.AppCompat.Title" />
    <style name="TextAppearance.AppCompat.Title.Inverse" parent="@style/Base.TextAppearance.AppCompat.Title.Inverse" />
    <style name="TextAppearance.AppCompat.Tooltip" parent="@style/TextAppearance.AppCompat">
        <item name="android:textSize">14.0sp</item>
        <item name="android:fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Menu" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu" />
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle" />
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse" />
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Title" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title" />
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse" />
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle" />
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse" parent="@style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle" />
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Title" parent="@style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title" />
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse" parent="@style/TextAppearance.AppCompat.Widget.ActionMode.Title" />
    <style name="TextAppearance.AppCompat.Widget.Button" parent="@style/Base.TextAppearance.AppCompat.Widget.Button" />
    <style name="TextAppearance.AppCompat.Widget.Button.Borderless.Colored" parent="@style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored" />
    <style name="TextAppearance.AppCompat.Widget.Button.Colored" parent="@style/Base.TextAppearance.AppCompat.Widget.Button.Colored" />
    <style name="TextAppearance.AppCompat.Widget.Button.Inverse" parent="@style/Base.TextAppearance.AppCompat.Widget.Button.Inverse" />
    <style name="TextAppearance.AppCompat.Widget.DropDownItem" parent="@style/Base.TextAppearance.AppCompat.Widget.DropDownItem" />
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Header" parent="@style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header" />
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Large" parent="@style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large" />
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Small" parent="@style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small" />
    <style name="TextAppearance.AppCompat.Widget.Switch" parent="@style/Base.TextAppearance.AppCompat.Widget.Switch" />
    <style name="TextAppearance.AppCompat.Widget.TextView.SpinnerItem" parent="@style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem" />
    <style name="TextAppearance.Compat.Notification" parent="@android:style/TextAppearance.Material.Notification" />
    <style name="TextAppearance.Compat.Notification.Info" parent="@android:style/TextAppearance.Material.Notification.Info" />
    <style name="TextAppearance.Compat.Notification.Info.Media" parent="@style/TextAppearance.Compat.Notification.Info">
        <item name="android:textColor">@color/secondary_text_default_material_dark</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Line2" parent="@style/TextAppearance.Compat.Notification.Info" />
    <style name="TextAppearance.Compat.Notification.Line2.Media" parent="@style/TextAppearance.Compat.Notification.Info.Media" />
    <style name="TextAppearance.Compat.Notification.Media" parent="@style/TextAppearance.Compat.Notification">
        <item name="android:textColor">@color/secondary_text_default_material_dark</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Time" parent="@android:style/TextAppearance.Material.Notification.Time" />
    <style name="TextAppearance.Compat.Notification.Time.Media" parent="@style/TextAppearance.Compat.Notification.Time">
        <item name="android:textColor">@color/secondary_text_default_material_dark</item>
    </style>
    <style name="TextAppearance.Compat.Notification.Title" parent="@android:style/TextAppearance.Material.Notification.Title" />
    <style name="TextAppearance.Compat.Notification.Title.Media" parent="@style/TextAppearance.Compat.Notification.Title">
        <item name="android:textColor">@color/primary_text_default_material_dark</item>
    </style>
    <style name="TextAppearance.Design.CollapsingToolbar.Expanded" parent="@style/TextAppearance.AppCompat.Display1">
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="TextAppearance.Design.Counter" parent="@style/TextAppearance.AppCompat.Caption" />
    <style name="TextAppearance.Design.Counter.Overflow" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textColor">@color/design_error</item>
    </style>
    <style name="TextAppearance.Design.Error" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textColor">@color/design_error</item>
    </style>
    <style name="TextAppearance.Design.HelperText" parent="@style/TextAppearance.AppCompat.Caption" />
    <style name="TextAppearance.Design.Hint" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textColor">?colorControlActivated</item>
    </style>
    <style name="TextAppearance.Design.Snackbar.Message" parent="@android:style/TextAppearance">
        <item name="android:textSize">@dimen/design_snackbar_text_size</item>
        <item name="android:textColor">?android:textColorPrimary</item>
    </style>
    <style name="TextAppearance.Design.Tab" parent="@style/TextAppearance.AppCompat.Button">
        <item name="android:textSize">@dimen/design_tab_text_size</item>
        <item name="android:textColor">@color/mtrl_tabs_legacy_text_color_selector</item>
        <item name="textAllCaps">true</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Badge" parent="@style/Base.TextAppearance.MaterialComponents.Badge">
        <item name="android:textStyle">normal</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Body1" parent="@style/TextAppearance.AppCompat.Body2">
        <item name="android:textSize">16.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.03125</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Body2" parent="@style/TextAppearance.AppCompat.Body1">
        <item name="android:textSize">14.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.017857144</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Button" parent="@style/Base.TextAppearance.MaterialComponents.Button">
        <item name="android:textStyle">normal</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Caption" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textSize">12.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.033333335</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Chip" parent="@style/TextAppearance.AppCompat">
        <item name="android:textSize">@dimen/mtrl_chip_text_size</item>
        <item name="android:textColor">@color/mtrl_chip_text_color</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline1" parent="@style/TextAppearance.AppCompat.Display4">
        <item name="android:textSize">96.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:letterSpacing">-0.015625</item>
        <item name="fontFamily">sans-serif-light</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline2" parent="@style/TextAppearance.AppCompat.Display3">
        <item name="android:textSize">60.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:letterSpacing">-0.008333334</item>
        <item name="fontFamily">sans-serif-light</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline3" parent="@style/TextAppearance.AppCompat.Display2">
        <item name="android:textSize">48.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.0</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline4" parent="@style/TextAppearance.AppCompat.Display1">
        <item name="android:textSize">34.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.007352941</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline5" parent="@style/TextAppearance.AppCompat.Headline">
        <item name="android:textSize">24.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.0</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline6" parent="@style/Base.TextAppearance.MaterialComponents.Headline6">
        <item name="android:textStyle">normal</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Overline" parent="@style/TextAppearance.AppCompat">
        <item name="android:textSize">10.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.16666667</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Subtitle1" parent="@style/TextAppearance.AppCompat.Subhead">
        <item name="android:textSize">16.0sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.009375</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Subtitle2" parent="@style/Base.TextAppearance.MaterialComponents.Subtitle2">
        <item name="android:textStyle">normal</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.Widget.AppCompat.ExpandedMenu.Item" parent="@style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item" />
    <style name="TextAppearance.Widget.AppCompat.Toolbar.Subtitle" parent="@style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle" />
    <style name="TextAppearance.Widget.AppCompat.Toolbar.Title" parent="@style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title" />
    <style name="Theme.AppCompat" parent="@style/Base.Theme.AppCompat" />
    <style name="Theme.AppCompat.CompactMenu" parent="@style/Base.Theme.AppCompat.CompactMenu" />
    <style name="Theme.AppCompat.DayNight" parent="@style/Theme.AppCompat.Light" />
    <style name="Theme.AppCompat.DayNight.DarkActionBar" parent="@style/Theme.AppCompat.Light.DarkActionBar" />
    <style name="Theme.AppCompat.DayNight.Dialog" parent="@style/Theme.AppCompat.Light.Dialog" />
    <style name="Theme.AppCompat.DayNight.Dialog.Alert" parent="@style/Theme.AppCompat.Light.Dialog.Alert" />
    <style name="Theme.AppCompat.DayNight.Dialog.MinWidth" parent="@style/Theme.AppCompat.Light.Dialog.MinWidth" />
    <style name="Theme.AppCompat.DayNight.DialogWhenLarge" parent="@style/Theme.AppCompat.Light.DialogWhenLarge" />
    <style name="Theme.AppCompat.DayNight.NoActionBar" parent="@style/Theme.AppCompat.Light.NoActionBar" />
    <style name="Theme.AppCompat.Dialog" parent="@style/Base.Theme.AppCompat.Dialog" />
    <style name="Theme.AppCompat.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Dialog.Alert" />
    <style name="Theme.AppCompat.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Dialog.MinWidth" />
    <style name="Theme.AppCompat.DialogWhenLarge" parent="@style/Base.Theme.AppCompat.DialogWhenLarge" />
    <style name="Theme.AppCompat.Empty" parent="" />
    <style name="Theme.AppCompat.Light" parent="@style/Base.Theme.AppCompat.Light" />
    <style name="Theme.AppCompat.Light.DarkActionBar" parent="@style/Base.Theme.AppCompat.Light.DarkActionBar" />
    <style name="Theme.AppCompat.Light.Dialog" parent="@style/Base.Theme.AppCompat.Light.Dialog" />
    <style name="Theme.AppCompat.Light.Dialog.Alert" parent="@style/Base.Theme.AppCompat.Light.Dialog.Alert" />
    <style name="Theme.AppCompat.Light.Dialog.MinWidth" parent="@style/Base.Theme.AppCompat.Light.Dialog.MinWidth" />
    <style name="Theme.AppCompat.Light.DialogWhenLarge" parent="@style/Base.Theme.AppCompat.Light.DialogWhenLarge" />
    <style name="Theme.AppCompat.Light.NoActionBar" parent="@style/Theme.AppCompat.Light">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.AppCompat.NoActionBar" parent="@style/Theme.AppCompat">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.Design" parent="@style/Theme.AppCompat" />
    <style name="Theme.Design.BottomSheetDialog" parent="@style/Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation.Design.BottomSheetDialog</item>
        <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
    </style>
    <style name="Theme.Design.Light" parent="@style/Theme.AppCompat.Light" />
    <style name="Theme.Design.Light.BottomSheetDialog" parent="@style/Theme.AppCompat.Light.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation.Design.BottomSheetDialog</item>
        <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
    </style>
    <style name="Theme.Design.Light.NoActionBar" parent="@style/Theme.Design.Light">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.Design.NoActionBar" parent="@style/Theme.Design">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.MaterialComponents" parent="@style/Base.Theme.MaterialComponents" />
    <style name="Theme.MaterialComponents.BottomSheetDialog" parent="@style/Theme.MaterialComponents.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation.MaterialComponents.BottomSheetDialog</item>
        <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
    </style>
    <style name="Theme.MaterialComponents.Bridge" parent="@style/Base.Theme.MaterialComponents.Bridge" />
    <style name="Theme.MaterialComponents.CompactMenu" parent="@style/Base.Theme.MaterialComponents.CompactMenu" />
    <style name="Theme.MaterialComponents.DayNight" parent="@style/Theme.MaterialComponents.Light" />
    <style name="Theme.MaterialComponents.DayNight.BottomSheetDialog" parent="@style/Theme.MaterialComponents.Light.BottomSheetDialog" />
    <style name="Theme.MaterialComponents.DayNight.Bridge" parent="@style/Theme.MaterialComponents.Light.Bridge" />
    <style name="Theme.MaterialComponents.DayNight.DarkActionBar" parent="@style/Theme.MaterialComponents.Light.DarkActionBar" />
    <style name="Theme.MaterialComponents.DayNight.DarkActionBar.Bridge" parent="@style/Theme.MaterialComponents.Light.DarkActionBar.Bridge" />
    <style name="Theme.MaterialComponents.DayNight.Dialog" parent="@style/Theme.MaterialComponents.Light.Dialog" />
    <style name="Theme.MaterialComponents.DayNight.Dialog.Alert" parent="@style/Theme.MaterialComponents.Light.Dialog.Alert" />
    <style name="Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge" parent="@style/Theme.MaterialComponents.Light.Dialog.Alert.Bridge" />
    <style name="Theme.MaterialComponents.DayNight.Dialog.Bridge" parent="@style/Theme.MaterialComponents.Light.Dialog.Bridge" />
    <style name="Theme.MaterialComponents.DayNight.Dialog.FixedSize" parent="@style/Theme.MaterialComponents.Light.Dialog.FixedSize" />
    <style name="Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge" parent="@style/Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge" />
    <style name="Theme.MaterialComponents.DayNight.Dialog.MinWidth" parent="@style/Theme.MaterialComponents.Light.Dialog.MinWidth" />
    <style name="Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge" parent="@style/Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge" />
    <style name="Theme.MaterialComponents.DayNight.DialogWhenLarge" parent="@style/Theme.MaterialComponents.Light.DialogWhenLarge" />
    <style name="Theme.MaterialComponents.DayNight.NoActionBar" parent="@style/Theme.MaterialComponents.Light.NoActionBar" />
    <style name="Theme.MaterialComponents.DayNight.NoActionBar.Bridge" parent="@style/Theme.MaterialComponents.Light.NoActionBar.Bridge" />
    <style name="Theme.MaterialComponents.Dialog" parent="@style/Base.Theme.MaterialComponents.Dialog" />
    <style name="Theme.MaterialComponents.Dialog.Alert" parent="@style/Base.Theme.MaterialComponents.Dialog.Alert" />
    <style name="Theme.MaterialComponents.Dialog.Alert.Bridge" parent="@style/Base.Theme.MaterialComponents.Dialog.Bridge">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Theme.MaterialComponents.Dialog.Bridge" parent="@style/Base.Theme.MaterialComponents.Dialog.Bridge" />
    <style name="Theme.MaterialComponents.Dialog.FixedSize" parent="@style/Base.Theme.MaterialComponents.Dialog.FixedSize" />
    <style name="Theme.MaterialComponents.Dialog.FixedSize.Bridge" parent="@style/Base.Theme.MaterialComponents.Dialog.Bridge">
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    </style>
    <style name="Theme.MaterialComponents.Dialog.MinWidth" parent="@style/Base.Theme.MaterialComponents.Dialog.MinWidth" />
    <style name="Theme.MaterialComponents.Dialog.MinWidth.Bridge" parent="@style/Base.Theme.MaterialComponents.Dialog.Bridge">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Theme.MaterialComponents.DialogWhenLarge" parent="@style/Base.Theme.MaterialComponents.DialogWhenLarge" />
    <style name="Theme.MaterialComponents.Light" parent="@style/Base.Theme.MaterialComponents.Light" />
    <style name="Theme.MaterialComponents.Light.BarSize" parent="@style/Theme.MaterialComponents.Light">
        <item name="actionBarSize">@dimen/action_bar_size</item>
    </style>
    <style name="Theme.MaterialComponents.Light.BottomSheetDialog" parent="@style/Theme.MaterialComponents.Light.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation.MaterialComponents.BottomSheetDialog</item>
        <item name="bottomSheetStyle">@style/Widget.Design.BottomSheet.Modal</item>
    </style>
    <style name="Theme.MaterialComponents.Light.Bridge" parent="@style/Base.Theme.MaterialComponents.Light.Bridge" />
    <style name="Theme.MaterialComponents.Light.DarkActionBar" parent="@style/Base.Theme.MaterialComponents.Light.DarkActionBar" />
    <style name="Theme.MaterialComponents.Light.DarkActionBar.Bridge" parent="@style/Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge" />
    <style name="Theme.MaterialComponents.Light.Dialog" parent="@style/Base.Theme.MaterialComponents.Light.Dialog" />
    <style name="Theme.MaterialComponents.Light.Dialog.Alert" parent="@style/Base.Theme.MaterialComponents.Light.Dialog.Alert" />
    <style name="Theme.MaterialComponents.Light.Dialog.Alert.Bridge" parent="@style/Base.Theme.MaterialComponents.Light.Dialog.Bridge">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Theme.MaterialComponents.Light.Dialog.Bridge" parent="@style/Base.Theme.MaterialComponents.Light.Dialog.Bridge" />
    <style name="Theme.MaterialComponents.Light.Dialog.FixedSize" parent="@style/Base.Theme.MaterialComponents.Light.Dialog.FixedSize" />
    <style name="Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge" parent="@style/Base.Theme.MaterialComponents.Light.Dialog.Bridge">
        <item name="windowFixedHeightMajor">@dimen/abc_dialog_fixed_height_major</item>
        <item name="windowFixedHeightMinor">@dimen/abc_dialog_fixed_height_minor</item>
        <item name="windowFixedWidthMajor">@dimen/abc_dialog_fixed_width_major</item>
        <item name="windowFixedWidthMinor">@dimen/abc_dialog_fixed_width_minor</item>
    </style>
    <style name="Theme.MaterialComponents.Light.Dialog.MinWidth" parent="@style/Base.Theme.MaterialComponents.Light.Dialog.MinWidth" />
    <style name="Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge" parent="@style/Base.Theme.MaterialComponents.Light.Dialog.Bridge">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Theme.MaterialComponents.Light.DialogWhenLarge" parent="@style/Base.Theme.MaterialComponents.Light.DialogWhenLarge" />
    <style name="Theme.MaterialComponents.Light.LargeTouch" parent="@style/Theme.MaterialComponents.Light">
        <item name="minTouchTargetSize">@dimen/mtrl_large_touch_target</item>
    </style>
    <style name="Theme.MaterialComponents.Light.NoActionBar" parent="@style/Theme.MaterialComponents.Light">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.MaterialComponents.Light.NoActionBar.Bridge" parent="@style/Theme.MaterialComponents.Light.Bridge">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.MaterialComponents.NoActionBar" parent="@style/Theme.MaterialComponents">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.MaterialComponents.NoActionBar.Bridge" parent="@style/Theme.MaterialComponents.Bridge">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.MyApplication" parent="@style/Theme.MaterialComponents.Light.NoActionBar">
        <item name="android:textColorPrimary">@color/primaryTextColor</item>
        <item name="android:statusBarColor">@color/primaryDarkColor</item>
        <item name="colorAccent">@color/primaryColor</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorPrimary">@color/primaryColor</item>
        <item name="colorPrimaryDark">@color/primaryDarkColor</item>
        <item name="colorPrimaryVariant">@color/primaryLightColor</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="ThemeFullView" parent="@style/Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowNoTitle">true</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="ThemeOverlay.AppCompat" parent="@style/Base.ThemeOverlay.AppCompat" />
    <style name="ThemeOverlay.AppCompat.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat.ActionBar" />
    <style name="ThemeOverlay.AppCompat.Dark" parent="@style/Base.ThemeOverlay.AppCompat.Dark" />
    <style name="ThemeOverlay.AppCompat.Dark.ActionBar" parent="@style/Base.ThemeOverlay.AppCompat.Dark.ActionBar" />
    <style name="ThemeOverlay.AppCompat.DayNight" parent="@style/ThemeOverlay.AppCompat.Light" />
    <style name="ThemeOverlay.AppCompat.DayNight.ActionBar" parent="@style/ThemeOverlay.AppCompat.DayNight">
        <item name="colorControlNormal">?android:textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="ThemeOverlay.AppCompat.Dialog" parent="@style/Base.ThemeOverlay.AppCompat.Dialog" />
    <style name="ThemeOverlay.AppCompat.Dialog.Alert" parent="@style/Base.ThemeOverlay.AppCompat.Dialog.Alert" />
    <style name="ThemeOverlay.AppCompat.Light" parent="@style/Base.ThemeOverlay.AppCompat.Light" />
    <style name="ThemeOverlay.Design.TextInputEditText" parent="" />
    <style name="ThemeOverlay.MaterialComponents" parent="@style/ThemeOverlay.AppCompat" />
    <style name="ThemeOverlay.MaterialComponents.ActionBar" parent="@style/ThemeOverlay.AppCompat.ActionBar" />
    <style name="ThemeOverlay.MaterialComponents.ActionBar.Primary" parent="">
        <item name="android:colorBackground">?colorPrimary</item>
        <item name="android:textColorPrimary">?colorOnPrimary</item>
        <item name="android:textColorSecondary">@color/material_on_primary_emphasis_medium</item>
        <item name="actionMenuTextColor">?colorOnPrimary</item>
        <item name="colorControlNormal">?colorOnPrimary</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.ActionBar.Surface" parent="">
        <item name="android:colorBackground">?colorSurface</item>
        <item name="android:textColorPrimary">@color/material_on_surface_emphasis_high_type</item>
        <item name="android:textColorSecondary">@color/material_on_surface_emphasis_medium</item>
        <item name="actionMenuTextColor">@color/material_on_surface_emphasis_medium</item>
        <item name="colorControlNormal">@color/material_on_surface_emphasis_medium</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.AutoCompleteTextView" parent="">
        <item name="colorControlActivated">?colorPrimary</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox" parent="@style/ThemeOverlay.MaterialComponents.AutoCompleteTextView">
        <item name="autoCompleteTextViewStyle">@style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense" parent="@style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox">
        <item name="autoCompleteTextViewStyle">@style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox" parent="@style/ThemeOverlay.MaterialComponents.AutoCompleteTextView">
        <item name="autoCompleteTextViewStyle">@style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense" parent="@style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox">
        <item name="autoCompleteTextViewStyle">@style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.BottomAppBar.Primary" parent="">
        <item name="actionMenuTextColor">?colorOnPrimary</item>
        <item name="colorControlNormal">?colorOnPrimary</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.BottomAppBar.Surface" parent="">
        <item name="actionMenuTextColor">@color/material_on_surface_emphasis_medium</item>
        <item name="colorControlNormal">@color/material_on_surface_emphasis_medium</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.BottomSheetDialog" parent="@style/ThemeOverlay.MaterialComponents.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation.MaterialComponents.BottomSheetDialog</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="bottomSheetStyle">@style/Widget.MaterialComponents.BottomSheet.Modal</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.Dark" parent="@style/ThemeOverlay.AppCompat.Dark">
        <item name="android:colorBackground">@color/design_dark_default_color_background</item>
        <item name="colorError">@color/design_dark_default_color_error</item>
        <item name="colorOnBackground">@color/design_dark_default_color_on_background</item>
        <item name="colorOnError">@color/design_dark_default_color_on_error</item>
        <item name="colorOnSurface">@color/design_dark_default_color_on_surface</item>
        <item name="colorSurface">@color/design_dark_default_color_surface</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.Dark.ActionBar" parent="@style/ThemeOverlay.AppCompat.Dark.ActionBar" />
    <style name="ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog" parent="@style/ThemeOverlay.MaterialComponents.Light.BottomSheetDialog" />
    <style name="ThemeOverlay.MaterialComponents.Dialog" parent="@style/Base.ThemeOverlay.MaterialComponents.Dialog" />
    <style name="ThemeOverlay.MaterialComponents.Dialog.Alert" parent="@style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert" />
    <style name="ThemeOverlay.MaterialComponents.Light" parent="@style/ThemeOverlay.AppCompat.Light">
        <item name="android:colorBackground">@color/design_default_color_background</item>
        <item name="colorError">@color/design_default_color_error</item>
        <item name="colorOnBackground">@color/design_default_color_on_background</item>
        <item name="colorOnError">@color/design_default_color_on_error</item>
        <item name="colorOnSurface">@color/design_default_color_on_surface</item>
        <item name="colorSurface">@color/design_default_color_surface</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.Light.BottomSheetDialog" parent="@style/ThemeOverlay.MaterialComponents.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@style/Animation.MaterialComponents.BottomSheetDialog</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="bottomSheetStyle">@style/Widget.MaterialComponents.BottomSheet.Modal</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog" parent="@style/Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog">
        <item name="materialAlertDialogTitleIconStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Icon</item>
        <item name="materialAlertDialogTitlePanelStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Panel</item>
        <item name="materialAlertDialogTitleTextStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Text</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered" parent="@style/Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog">
        <item name="materialAlertDialogTitleIconStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked</item>
        <item name="materialAlertDialogTitlePanelStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked</item>
        <item name="materialAlertDialogTitleTextStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date" parent="@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog">
        <item name="android:buttonBarPositiveButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="android:buttonBarNegativeButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar" parent="@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date">
        <item name="android:datePickerStyle">@style/MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text" parent="@style/TextAppearance.MaterialComponents.Headline6">
        <item name="android:textColor">?colorOnPrimary</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day" parent="@style/TextAppearance.MaterialComponents.Headline1">
        <item name="android:textColor">?colorOnPrimary</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner" parent="@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date">
        <item name="android:datePickerStyle">@style/MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialCalendar" parent="@style/ThemeOverlay.MaterialComponents.Dialog">
        <item name="buttonBarNegativeButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="buttonBarPositiveButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="materialCalendarDay">@style/Widget.MaterialComponents.MaterialCalendar.DayTextView</item>
        <item name="materialCalendarHeaderConfirmButton">@style/Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton</item>
        <item name="materialCalendarHeaderDivider">@style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider</item>
        <item name="materialCalendarHeaderLayout">@style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout</item>
        <item name="materialCalendarHeaderSelection">@style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection</item>
        <item name="materialCalendarHeaderTitle">@style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle</item>
        <item name="materialCalendarHeaderToggleButton">@style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton</item>
        <item name="materialCalendarStyle">@style/Widget.MaterialComponents.MaterialCalendar</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen" parent="@style/ThemeOverlay.MaterialComponents.MaterialCalendar">
        <item name="materialCalendarHeaderSelection">@style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen</item>
        <item name="materialCalendarStyle">@style/Widget.MaterialComponents.MaterialCalendar.Fullscreen</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText" parent="@style/ThemeOverlay.Design.TextInputEditText">
        <item name="android:editTextBackground">@null</item>
        <item name="colorControlActivated">?colorPrimary</item>
        <item name="editTextBackground">@null</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox" parent="@style/ThemeOverlay.MaterialComponents.TextInputEditText">
        <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.FilledBox</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense" parent="@style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox">
        <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox" parent="@style/ThemeOverlay.MaterialComponents.TextInputEditText">
        <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense" parent="@style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox">
        <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.Toolbar.Primary" parent="">
        <item name="actionMenuTextColor">?colorOnPrimary</item>
        <item name="colorControlNormal">?colorOnPrimary</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.Toolbar.Surface" parent="">
        <item name="actionMenuTextColor">@color/material_on_surface_emphasis_medium</item>
        <item name="colorControlNormal">@color/material_on_surface_emphasis_medium</item>
    </style>
    <style name="Widget.AppCompat.ActionBar" parent="@style/Base.Widget.AppCompat.ActionBar" />
    <style name="Widget.AppCompat.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.ActionBar.Solid" />
    <style name="Widget.AppCompat.ActionBar.TabBar" parent="@style/Base.Widget.AppCompat.ActionBar.TabBar" />
    <style name="Widget.AppCompat.ActionBar.TabText" parent="@style/Base.Widget.AppCompat.ActionBar.TabText" />
    <style name="Widget.AppCompat.ActionBar.TabView" parent="@style/Base.Widget.AppCompat.ActionBar.TabView" />
    <style name="Widget.AppCompat.ActionButton" parent="@style/Base.Widget.AppCompat.ActionButton" />
    <style name="Widget.AppCompat.ActionButton.CloseMode" parent="@style/Base.Widget.AppCompat.ActionButton.CloseMode" />
    <style name="Widget.AppCompat.ActionButton.Overflow" parent="@style/Base.Widget.AppCompat.ActionButton.Overflow" />
    <style name="Widget.AppCompat.ActionMode" parent="@style/Base.Widget.AppCompat.ActionMode" />
    <style name="Widget.AppCompat.ActivityChooserView" parent="@style/Base.Widget.AppCompat.ActivityChooserView" />
    <style name="Widget.AppCompat.AutoCompleteTextView" parent="@style/Base.Widget.AppCompat.AutoCompleteTextView" />
    <style name="Widget.AppCompat.Button" parent="@style/Base.Widget.AppCompat.Button" />
    <style name="Widget.AppCompat.Button.Borderless" parent="@style/Base.Widget.AppCompat.Button.Borderless" />
    <style name="Widget.AppCompat.Button.Borderless.Colored" parent="@style/Base.Widget.AppCompat.Button.Borderless.Colored" />
    <style name="Widget.AppCompat.Button.ButtonBar.AlertDialog" parent="@style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog" />
    <style name="Widget.AppCompat.Button.Colored" parent="@style/Base.Widget.AppCompat.Button.Colored" />
    <style name="Widget.AppCompat.Button.Small" parent="@style/Base.Widget.AppCompat.Button.Small" />
    <style name="Widget.AppCompat.ButtonBar" parent="@style/Base.Widget.AppCompat.ButtonBar" />
    <style name="Widget.AppCompat.ButtonBar.AlertDialog" parent="@style/Base.Widget.AppCompat.ButtonBar.AlertDialog" />
    <style name="Widget.AppCompat.CompoundButton.CheckBox" parent="@style/Base.Widget.AppCompat.CompoundButton.CheckBox" />
    <style name="Widget.AppCompat.CompoundButton.RadioButton" parent="@style/Base.Widget.AppCompat.CompoundButton.RadioButton" />
    <style name="Widget.AppCompat.CompoundButton.Switch" parent="@style/Base.Widget.AppCompat.CompoundButton.Switch" />
    <style name="Widget.AppCompat.DrawerArrowToggle" parent="@style/Base.Widget.AppCompat.DrawerArrowToggle">
        <item name="color">?colorControlNormal</item>
    </style>
    <style name="Widget.AppCompat.DropDownItem.Spinner" parent="@style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text" />
    <style name="Widget.AppCompat.EditText" parent="@style/Base.Widget.AppCompat.EditText" />
    <style name="Widget.AppCompat.ImageButton" parent="@style/Base.Widget.AppCompat.ImageButton" />
    <style name="Widget.AppCompat.Light.ActionBar" parent="@style/Base.Widget.AppCompat.Light.ActionBar" />
    <style name="Widget.AppCompat.Light.ActionBar.Solid" parent="@style/Base.Widget.AppCompat.Light.ActionBar.Solid" />
    <style name="Widget.AppCompat.Light.ActionBar.Solid.Inverse" parent="@style/Widget.AppCompat.Light.ActionBar.Solid" />
    <style name="Widget.AppCompat.Light.ActionBar.TabBar" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabBar" />
    <style name="Widget.AppCompat.Light.ActionBar.TabBar.Inverse" parent="@style/Widget.AppCompat.Light.ActionBar.TabBar" />
    <style name="Widget.AppCompat.Light.ActionBar.TabText" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabText" />
    <style name="Widget.AppCompat.Light.ActionBar.TabText.Inverse" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse" />
    <style name="Widget.AppCompat.Light.ActionBar.TabView" parent="@style/Base.Widget.AppCompat.Light.ActionBar.TabView" />
    <style name="Widget.AppCompat.Light.ActionBar.TabView.Inverse" parent="@style/Widget.AppCompat.Light.ActionBar.TabView" />
    <style name="Widget.AppCompat.Light.ActionButton" parent="@style/Widget.AppCompat.ActionButton" />
    <style name="Widget.AppCompat.Light.ActionButton.CloseMode" parent="@style/Widget.AppCompat.ActionButton.CloseMode" />
    <style name="Widget.AppCompat.Light.ActionButton.Overflow" parent="@style/Widget.AppCompat.ActionButton.Overflow" />
    <style name="Widget.AppCompat.Light.ActionMode.Inverse" parent="@style/Widget.AppCompat.ActionMode" />
    <style name="Widget.AppCompat.Light.ActivityChooserView" parent="@style/Widget.AppCompat.ActivityChooserView" />
    <style name="Widget.AppCompat.Light.AutoCompleteTextView" parent="@style/Widget.AppCompat.AutoCompleteTextView" />
    <style name="Widget.AppCompat.Light.DropDownItem.Spinner" parent="@style/Widget.AppCompat.DropDownItem.Spinner" />
    <style name="Widget.AppCompat.Light.ListPopupWindow" parent="@style/Widget.AppCompat.ListPopupWindow" />
    <style name="Widget.AppCompat.Light.ListView.DropDown" parent="@style/Widget.AppCompat.ListView.DropDown" />
    <style name="Widget.AppCompat.Light.PopupMenu" parent="@style/Base.Widget.AppCompat.Light.PopupMenu" />
    <style name="Widget.AppCompat.Light.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.Light.PopupMenu.Overflow" />
    <style name="Widget.AppCompat.Light.SearchView" parent="@style/Widget.AppCompat.SearchView" />
    <style name="Widget.AppCompat.Light.Spinner.DropDown.ActionBar" parent="@style/Widget.AppCompat.Spinner.DropDown.ActionBar" />
    <style name="Widget.AppCompat.ListMenuView" parent="@style/Base.Widget.AppCompat.ListMenuView" />
    <style name="Widget.AppCompat.ListPopupWindow" parent="@style/Base.Widget.AppCompat.ListPopupWindow" />
    <style name="Widget.AppCompat.ListView" parent="@style/Base.Widget.AppCompat.ListView" />
    <style name="Widget.AppCompat.ListView.DropDown" parent="@style/Base.Widget.AppCompat.ListView.DropDown" />
    <style name="Widget.AppCompat.ListView.Menu" parent="@style/Base.Widget.AppCompat.ListView.Menu" />
    <style name="Widget.AppCompat.PopupMenu" parent="@style/Base.Widget.AppCompat.PopupMenu" />
    <style name="Widget.AppCompat.PopupMenu.Overflow" parent="@style/Base.Widget.AppCompat.PopupMenu.Overflow" />
    <style name="Widget.AppCompat.PopupWindow" parent="@style/Base.Widget.AppCompat.PopupWindow" />
    <style name="Widget.AppCompat.ProgressBar" parent="@style/Base.Widget.AppCompat.ProgressBar" />
    <style name="Widget.AppCompat.ProgressBar.Horizontal" parent="@style/Base.Widget.AppCompat.ProgressBar.Horizontal" />
    <style name="Widget.AppCompat.RatingBar" parent="@style/Base.Widget.AppCompat.RatingBar" />
    <style name="Widget.AppCompat.RatingBar.Indicator" parent="@style/Base.Widget.AppCompat.RatingBar.Indicator" />
    <style name="Widget.AppCompat.RatingBar.Small" parent="@style/Base.Widget.AppCompat.RatingBar.Small" />
    <style name="Widget.AppCompat.SearchView" parent="@style/Base.Widget.AppCompat.SearchView" />
    <style name="Widget.AppCompat.SearchView.ActionBar" parent="@style/Base.Widget.AppCompat.SearchView.ActionBar" />
    <style name="Widget.AppCompat.SeekBar" parent="@style/Base.Widget.AppCompat.SeekBar" />
    <style name="Widget.AppCompat.SeekBar.Discrete" parent="@style/Base.Widget.AppCompat.SeekBar.Discrete" />
    <style name="Widget.AppCompat.Spinner" parent="@style/Base.Widget.AppCompat.Spinner" />
    <style name="Widget.AppCompat.Spinner.DropDown" parent="@style/Widget.AppCompat.Spinner" />
    <style name="Widget.AppCompat.Spinner.DropDown.ActionBar" parent="@style/Widget.AppCompat.Spinner.DropDown" />
    <style name="Widget.AppCompat.Spinner.Underlined" parent="@style/Base.Widget.AppCompat.Spinner.Underlined" />
    <style name="Widget.AppCompat.TextView" parent="@style/Base.Widget.AppCompat.TextView" />
    <style name="Widget.AppCompat.TextView.SpinnerItem" parent="@style/Base.Widget.AppCompat.TextView.SpinnerItem" />
    <style name="Widget.AppCompat.Toolbar" parent="@style/Base.Widget.AppCompat.Toolbar" />
    <style name="Widget.AppCompat.Toolbar.Button.Navigation" parent="@style/Base.Widget.AppCompat.Toolbar.Button.Navigation" />
    <style name="Widget.Compat.NotificationActionContainer" parent="">
        <item name="android:background">@drawable/notification_action_background</item>
    </style>
    <style name="Widget.Compat.NotificationActionText" parent="">
        <item name="android:textAppearance">?android:textAppearanceButton</item>
        <item name="android:textSize">@dimen/notification_action_text_size</item>
        <item name="android:textColor">@color/androidx_core_secondary_text_default_material_light</item>
    </style>
    <style name="Widget.Design.AppBarLayout" parent="@android:style/Widget">
        <item name="android:background">?colorPrimary</item>
        <item name="android:stateListAnimator">@animator/design_appbar_state_list_animator</item>
        <item name="android:touchscreenBlocksFocus">true</item>
    </style>
    <style name="Widget.Design.BottomNavigationView" parent="">
        <item name="elevation">@dimen/design_bottom_navigation_elevation</item>
        <item name="enforceMaterialTheme">false</item>
        <item name="enforceTextAppearance">false</item>
        <item name="itemBackground">?selectableItemBackgroundBorderless</item>
        <item name="itemHorizontalTranslationEnabled">true</item>
        <item name="itemIconSize">@dimen/design_bottom_navigation_icon_size</item>
        <item name="labelVisibilityMode">auto</item>
    </style>
    <style name="Widget.Design.BottomSheet.Modal" parent="@android:style/Widget">
        <item name="android:background">?android:colorBackground</item>
        <item name="android:elevation">@dimen/design_bottom_sheet_modal_elevation</item>
        <item name="backgroundTint">?android:colorBackground</item>
        <item name="behavior_hideable">true</item>
        <item name="behavior_peekHeight">auto</item>
        <item name="behavior_skipCollapsed">false</item>
        <item name="enforceMaterialTheme">false</item>
        <item name="shapeAppearance">@null</item>
        <item name="shapeAppearanceOverlay">@null</item>
    </style>
    <style name="Widget.Design.CollapsingToolbar" parent="@android:style/Widget">
        <item name="expandedTitleMargin">32.0dip</item>
        <item name="statusBarScrim">?colorPrimaryDark</item>
    </style>
    <style name="Widget.Design.FloatingActionButton" parent="@android:style/Widget">
        <item name="android:background">@drawable/design_fab_background</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="backgroundTint">?colorAccent</item>
        <item name="borderWidth">@dimen/design_fab_border_width</item>
        <item name="elevation">@dimen/design_fab_elevation</item>
        <item name="fabSize">auto</item>
        <item name="hideMotionSpec">@animator/design_fab_hide_motion_spec</item>
        <item name="hoveredFocusedTranslationZ">@dimen/design_fab_translation_z_hovered_focused</item>
        <item name="maxImageSize">@dimen/design_fab_image_size</item>
        <item name="pressedTranslationZ">@dimen/design_fab_translation_z_pressed</item>
        <item name="rippleColor">?colorControlHighlight</item>
        <item name="showMotionSpec">@animator/design_fab_show_motion_spec</item>
    </style>
    <style name="Widget.Design.NavigationView" parent="@style/Widget.Design.ScrimInsetsFrameLayout">
        <item name="android:background">?android:windowBackground</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:maxWidth">@dimen/design_navigation_max_width</item>
        <item name="elevation">@dimen/design_navigation_elevation</item>
        <item name="itemHorizontalPadding">@dimen/design_navigation_item_horizontal_padding</item>
        <item name="itemIconPadding">@dimen/design_navigation_item_icon_padding</item>
    </style>
    <style name="Widget.Design.ScrimInsetsFrameLayout" parent="">
        <item name="insetForeground">#44000000</item>
    </style>
    <style name="Widget.Design.Snackbar" parent="@android:style/Widget">
        <item name="android:background">@drawable/design_snackbar_background</item>
        <item name="android:paddingLeft">@dimen/design_snackbar_padding_horizontal</item>
        <item name="android:paddingRight">@dimen/design_snackbar_padding_horizontal</item>
        <item name="android:maxWidth">@dimen/design_snackbar_max_width</item>
        <item name="android:minWidth">@dimen/design_snackbar_min_width</item>
        <item name="actionTextColorAlpha">@dimen/design_snackbar_action_text_color_alpha</item>
        <item name="animationMode">slide</item>
        <item name="elevation">@dimen/design_snackbar_elevation</item>
        <item name="maxActionInlineWidth">@dimen/design_snackbar_action_inline_max_width</item>
    </style>
    <style name="Widget.Design.TabLayout" parent="@style/Base.Widget.Design.TabLayout">
        <item name="tabGravity">fill</item>
        <item name="tabIndicatorFullWidth">true</item>
        <item name="tabMode">fixed</item>
    </style>
    <style name="Widget.Design.TextInputLayout" parent="@android:style/Widget">
        <item name="boxBackgroundMode">none</item>
        <item name="boxStrokeColor">@color/design_box_stroke_color</item>
        <item name="counterOverflowTextAppearance">@style/TextAppearance.Design.Counter.Overflow</item>
        <item name="counterOverflowTextColor">@null</item>
        <item name="counterTextAppearance">@style/TextAppearance.Design.Counter</item>
        <item name="counterTextColor">@null</item>
        <item name="endIconTint">@color/design_icon_tint</item>
        <item name="enforceMaterialTheme">false</item>
        <item name="enforceTextAppearance">false</item>
        <item name="errorIconDrawable">@null</item>
        <item name="errorTextAppearance">@style/TextAppearance.Design.Error</item>
        <item name="errorTextColor">@null</item>
        <item name="helperTextTextAppearance">@style/TextAppearance.Design.HelperText</item>
        <item name="helperTextTextColor">@null</item>
        <item name="hintTextAppearance">@style/TextAppearance.Design.Hint</item>
        <item name="hintTextColor">@null</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Design.TextInputEditText</item>
        <item name="passwordToggleContentDescription">@string/password_toggle_content_description</item>
        <item name="passwordToggleDrawable">@drawable/design_password_eye</item>
        <item name="passwordToggleTint">@color/design_icon_tint</item>
        <item name="shapeAppearance">@null</item>
        <item name="shapeAppearanceOverlay">@null</item>
        <item name="startIconTint">@color/design_icon_tint</item>
    </style>
    <style name="Widget.MaterialComponents.ActionBar.Primary" parent="@style/Widget.AppCompat.ActionBar.Solid">
        <item name="background">?colorPrimary</item>
        <item name="elevation">@dimen/design_appbar_elevation</item>
    </style>
    <style name="Widget.MaterialComponents.ActionBar.PrimarySurface" parent="@style/Widget.MaterialComponents.ActionBar.Primary" />
    <style name="Widget.MaterialComponents.ActionBar.Solid" parent="@style/Widget.AppCompat.ActionBar.Solid">
        <item name="android:minHeight">@dimen/mtrl_toolbar_default_height</item>
        <item name="maxButtonHeight">@dimen/mtrl_toolbar_default_height</item>
        <item name="subtitleTextStyle">?textAppearanceSubtitle1</item>
        <item name="titleTextStyle">?textAppearanceHeadline6</item>
    </style>
    <style name="Widget.MaterialComponents.ActionBar.Surface" parent="@style/Widget.AppCompat.Light.ActionBar.Solid">
        <item name="background">?colorSurface</item>
        <item name="elevation">0.0dip</item>
    </style>
    <style name="Widget.MaterialComponents.AppBarLayout.Primary" parent="@style/Widget.Design.AppBarLayout" />
    <style name="Widget.MaterialComponents.AppBarLayout.PrimarySurface" parent="@style/Widget.MaterialComponents.AppBarLayout.Primary" />
    <style name="Widget.MaterialComponents.AppBarLayout.Surface" parent="@style/Widget.Design.AppBarLayout">
        <item name="android:background">?colorSurface</item>
    </style>
    <style name="Widget.MaterialComponents.AutoCompleteTextView.FilledBox" parent="@style/Base.Widget.MaterialComponents.AutoCompleteTextView">
        <item name="android:paddingTop">28.0dip</item>
        <item name="android:paddingBottom">12.0dip</item>
    </style>
    <style name="Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense" parent="@style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox">
        <item name="android:paddingTop">24.0dip</item>
        <item name="android:paddingBottom">8.0dip</item>
    </style>
    <style name="Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox" parent="@style/Base.Widget.MaterialComponents.AutoCompleteTextView" />
    <style name="Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense" parent="@style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox">
        <item name="android:paddingTop">13.0dip</item>
        <item name="android:paddingBottom">13.0dip</item>
    </style>
    <style name="Widget.MaterialComponents.Badge" parent="@android:style/Widget">
        <item name="backgroundColor">?colorError</item>
        <item name="badgeGravity">TOP_END</item>
        <item name="maxCharacterCount">@integer/mtrl_badge_max_character_count</item>
    </style>
    <style name="Widget.MaterialComponents.BottomAppBar" parent="@style/Widget.AppCompat.Toolbar">
        <item name="android:minHeight">@dimen/mtrl_bottomappbar_height</item>
        <item name="backgroundTint">?colorSurface</item>
        <item name="elevation">8.0dip</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="fabCradleMargin">@dimen/mtrl_bottomappbar_fab_cradle_margin</item>
        <item name="fabCradleRoundedCornerRadius">@dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius</item>
        <item name="fabCradleVerticalOffset">@dimen/mtrl_bottomappbar_fab_cradle_vertical_offset</item>
        <item name="maxButtonHeight">@dimen/mtrl_bottomappbar_height</item>
    </style>
    <style name="Widget.MaterialComponents.BottomAppBar.Colored" parent="@style/Widget.MaterialComponents.BottomAppBar">
        <item name="backgroundTint">?colorPrimary</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.BottomAppBar.Primary</item>
    </style>
    <style name="Widget.MaterialComponents.BottomAppBar.PrimarySurface" parent="@style/Widget.MaterialComponents.BottomAppBar.Colored" />
    <style name="Widget.MaterialComponents.BottomNavigationView" parent="@style/Widget.Design.BottomNavigationView">
        <item name="android:background">?colorSurface</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="itemBackground">@null</item>
        <item name="itemHorizontalTranslationEnabled">false</item>
        <item name="itemIconTint">@color/mtrl_bottom_nav_item_tint</item>
        <item name="itemRippleColor">@color/mtrl_bottom_nav_ripple_color</item>
        <item name="itemTextAppearanceActive">?textAppearanceCaption</item>
        <item name="itemTextAppearanceInactive">?textAppearanceCaption</item>
        <item name="itemTextColor">@color/mtrl_bottom_nav_item_tint</item>
    </style>
    <style name="Widget.MaterialComponents.BottomNavigationView.Colored" parent="@style/Widget.MaterialComponents.BottomNavigationView">
        <item name="android:background">?colorPrimary</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="itemIconTint">@color/mtrl_bottom_nav_colored_item_tint</item>
        <item name="itemRippleColor">@color/mtrl_bottom_nav_colored_ripple_color</item>
        <item name="itemTextAppearanceActive">?textAppearanceCaption</item>
        <item name="itemTextAppearanceInactive">?textAppearanceCaption</item>
        <item name="itemTextColor">@color/mtrl_bottom_nav_colored_item_tint</item>
    </style>
    <style name="Widget.MaterialComponents.BottomNavigationView.PrimarySurface" parent="@style/Widget.MaterialComponents.BottomNavigationView.Colored" />
    <style name="Widget.MaterialComponents.BottomSheet" parent="@style/Widget.Design.BottomSheet.Modal">
        <item name="android:background">@null</item>
        <item name="android:elevation">@dimen/design_bottom_sheet_elevation</item>
        <item name="backgroundTint">?colorSurface</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="shapeAppearance">?shapeAppearanceLargeComponent</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.BottomSheet</item>
    </style>
    <style name="Widget.MaterialComponents.BottomSheet.Modal" parent="@style/Widget.MaterialComponents.BottomSheet">
        <item name="android:elevation">@dimen/design_bottom_sheet_modal_elevation</item>
    </style>
    <style name="Widget.MaterialComponents.Button" parent="@style/Widget.AppCompat.Button">
        <item name="android:textAppearance">?textAppearanceButton</item>
        <item name="android:textColor">@color/mtrl_btn_text_color_selector</item>
        <item name="android:paddingLeft">@dimen/mtrl_btn_padding_left</item>
        <item name="android:paddingTop">@dimen/mtrl_btn_padding_top</item>
        <item name="android:paddingRight">@dimen/mtrl_btn_padding_right</item>
        <item name="android:paddingBottom">@dimen/mtrl_btn_padding_bottom</item>
        <item name="android:insetLeft">0.0dip</item>
        <item name="android:insetRight">0.0dip</item>
        <item name="android:insetTop">@dimen/mtrl_btn_inset</item>
        <item name="android:insetBottom">@dimen/mtrl_btn_inset</item>
        <item name="android:stateListAnimator">@animator/mtrl_btn_state_list_anim</item>
        <item name="backgroundTint">@color/mtrl_btn_bg_color_selector</item>
        <item name="cornerRadius">@null</item>
        <item name="elevation">@dimen/mtrl_btn_elevation</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="iconPadding">@dimen/mtrl_btn_icon_padding</item>
        <item name="iconTint">@color/mtrl_btn_text_color_selector</item>
        <item name="rippleColor">@color/mtrl_btn_ripple_color</item>
        <item name="shapeAppearance">?shapeAppearanceSmallComponent</item>
    </style>
    <style name="Widget.MaterialComponents.Button.Icon" parent="@style/Widget.MaterialComponents.Button">
        <item name="android:paddingLeft">@dimen/mtrl_btn_icon_btn_padding_left</item>
    </style>
    <style name="Widget.MaterialComponents.Button.OutlinedButton" parent="@style/Widget.MaterialComponents.Button.TextButton">
        <item name="android:paddingLeft">@dimen/mtrl_btn_padding_left</item>
        <item name="android:paddingRight">@dimen/mtrl_btn_padding_right</item>
        <item name="strokeColor">@color/mtrl_btn_stroke_color_selector</item>
        <item name="strokeWidth">@dimen/mtrl_btn_stroke_size</item>
    </style>
    <style name="Widget.MaterialComponents.Button.OutlinedButton.Icon" parent="@style/Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:paddingLeft">@dimen/mtrl_btn_icon_btn_padding_left</item>
    </style>
    <style name="Widget.MaterialComponents.Button.TextButton" parent="@style/Widget.MaterialComponents.Button.UnelevatedButton">
        <item name="android:textColor">@color/mtrl_text_btn_text_color_selector</item>
        <item name="android:paddingLeft">@dimen/mtrl_btn_text_btn_padding_left</item>
        <item name="android:paddingRight">@dimen/mtrl_btn_text_btn_padding_right</item>
        <item name="backgroundTint">@color/mtrl_btn_text_btn_bg_color_selector</item>
        <item name="iconPadding">@dimen/mtrl_btn_text_btn_icon_padding</item>
        <item name="iconTint">@color/mtrl_text_btn_text_color_selector</item>
        <item name="rippleColor">@color/mtrl_btn_text_btn_ripple_color</item>
    </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Dialog" parent="@style/Widget.MaterialComponents.Button.TextButton">
        <item name="android:ellipsize">end</item>
        <item name="android:layout_marginLeft">@dimen/mtrl_btn_text_btn_padding_left</item>
        <item name="android:minWidth">@dimen/mtrl_btn_dialog_btn_min_width</item>
        <item name="android:lines">1</item>
        <item name="android:singleLine">true</item>
        <item name="android:layout_marginStart">@dimen/mtrl_btn_text_btn_padding_left</item>
    </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Dialog.Flush" parent="@style/Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:layout_marginLeft">0.0dip</item>
        <item name="android:layout_marginStart">0.0dip</item>
    </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Dialog.Icon" parent="@style/Widget.MaterialComponents.Button.TextButton.Dialog" />
    <style name="Widget.MaterialComponents.Button.TextButton.Icon" parent="@style/Widget.MaterialComponents.Button.TextButton" />
    <style name="Widget.MaterialComponents.Button.TextButton.Snackbar" parent="@style/Widget.MaterialComponents.Button.TextButton">
        <item name="android:textColor">?colorPrimary</item>
    </style>
    <style name="Widget.MaterialComponents.Button.UnelevatedButton" parent="@style/Widget.MaterialComponents.Button">
        <item name="android:stateListAnimator">@animator/mtrl_btn_unelevated_state_list_anim</item>
        <item name="elevation">0.0dip</item>
    </style>
    <style name="Widget.MaterialComponents.Button.UnelevatedButton.Icon" parent="@style/Widget.MaterialComponents.Button.UnelevatedButton">
        <item name="android:paddingLeft">@dimen/mtrl_btn_icon_btn_padding_left</item>
    </style>
    <style name="Widget.MaterialComponents.CardView" parent="@style/CardView">
        <item name="android:stateListAnimator">@animator/mtrl_card_state_list_anim</item>
        <item name="cardBackgroundColor">?colorSurface</item>
        <item name="cardCornerRadius">@null</item>
        <item name="cardElevation">@dimen/mtrl_card_elevation</item>
        <item name="cardForegroundColor">@color/mtrl_card_view_foreground</item>
        <item name="checkedIcon">@drawable/ic_mtrl_checked_circle</item>
        <item name="checkedIconTint">?colorPrimary</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="rippleColor">@color/mtrl_card_view_ripple</item>
        <item name="shapeAppearance">?shapeAppearanceMediumComponent</item>
    </style>
    <style name="Widget.MaterialComponents.CheckedTextView" parent="@style/Base.Widget.MaterialComponents.CheckedTextView">
        <item name="android:textAppearance">?textAppearanceBody1</item>
        <item name="android:textColor">@color/material_on_surface_emphasis_medium</item>
    </style>
    <style name="Widget.MaterialComponents.Chip.Action" parent="@style/Base.Widget.MaterialComponents.Chip">
        <item name="closeIconVisible">false</item>
    </style>
    <style name="Widget.MaterialComponents.Chip.Choice" parent="@style/Base.Widget.MaterialComponents.Chip">
        <item name="android:textColor">@color/mtrl_choice_chip_text_color</item>
        <item name="android:checkable">true</item>
        <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_black</item>
        <item name="checkedIconVisible">false</item>
        <item name="chipBackgroundColor">@color/mtrl_choice_chip_background_color</item>
        <item name="chipIconVisible">false</item>
        <item name="closeIconVisible">false</item>
        <item name="rippleColor">@color/mtrl_choice_chip_ripple_color</item>
    </style>
    <style name="Widget.MaterialComponents.Chip.Entry" parent="@style/Base.Widget.MaterialComponents.Chip">
        <item name="android:checkable">true</item>
    </style>
    <style name="Widget.MaterialComponents.Chip.Filter" parent="@style/Base.Widget.MaterialComponents.Chip">
        <item name="android:checkable">true</item>
        <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_black</item>
        <item name="chipIconVisible">false</item>
        <item name="closeIconVisible">false</item>
    </style>
    <style name="Widget.MaterialComponents.ChipGroup" parent="@android:style/Widget">
        <item name="chipSpacingHorizontal">8.0dip</item>
        <item name="singleLine">false</item>
        <item name="singleSelection">false</item>
    </style>
    <style name="Widget.MaterialComponents.CompoundButton.CheckBox" parent="@style/Widget.AppCompat.CompoundButton.CheckBox">
        <item name="android:minWidth">?minTouchTargetSize</item>
        <item name="android:minHeight">?minTouchTargetSize</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="useMaterialThemeColors">true</item>
    </style>
    <style name="Widget.MaterialComponents.CompoundButton.RadioButton" parent="@style/Widget.AppCompat.CompoundButton.RadioButton">
        <item name="android:minWidth">?minTouchTargetSize</item>
        <item name="android:minHeight">?minTouchTargetSize</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="useMaterialThemeColors">true</item>
    </style>
    <style name="Widget.MaterialComponents.CompoundButton.Switch" parent="@style/Widget.AppCompat.CompoundButton.Switch">
        <item name="android:minWidth">?minTouchTargetSize</item>
        <item name="android:minHeight">?minTouchTargetSize</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="useMaterialThemeColors">true</item>
    </style>
    <style name="Widget.MaterialComponents.ExtendedFloatingActionButton" parent="@style/Widget.MaterialComponents.Button">
        <item name="android:textColor">@color/mtrl_extended_fab_text_color_selector</item>
        <item name="android:paddingLeft">@dimen/mtrl_extended_fab_start_padding</item>
        <item name="android:paddingTop">@dimen/mtrl_extended_fab_top_padding</item>
        <item name="android:paddingRight">@dimen/mtrl_extended_fab_end_padding</item>
        <item name="android:paddingBottom">@dimen/mtrl_extended_fab_bottom_padding</item>
        <item name="android:minWidth">@dimen/mtrl_extended_fab_min_width</item>
        <item name="android:minHeight">@dimen/mtrl_extended_fab_min_height</item>
        <item name="android:maxLines">1</item>
        <item name="android:insetTop">0.0dip</item>
        <item name="android:insetBottom">0.0dip</item>
        <item name="android:paddingStart">@dimen/mtrl_extended_fab_start_padding</item>
        <item name="android:paddingEnd">@dimen/mtrl_extended_fab_end_padding</item>
        <item name="android:stateListAnimator">@animator/mtrl_extended_fab_state_list_animator</item>
        <item name="backgroundTint">@color/mtrl_extended_fab_bg_color_selector</item>
        <item name="elevation">@dimen/mtrl_extended_fab_elevation</item>
        <item name="iconPadding">@dimen/mtrl_extended_fab_icon_text_spacing</item>
        <item name="iconSize">@dimen/mtrl_extended_fab_icon_size</item>
        <item name="iconTint">@color/mtrl_extended_fab_text_color_selector</item>
        <item name="rippleColor">@color/mtrl_extended_fab_ripple_color</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton</item>
    </style>
    <style name="Widget.MaterialComponents.ExtendedFloatingActionButton.Icon" parent="@style/Widget.MaterialComponents.ExtendedFloatingActionButton">
        <item name="android:gravity">start|center</item>
        <item name="android:paddingLeft">@dimen/mtrl_extended_fab_start_padding_icon</item>
        <item name="android:paddingRight">@dimen/mtrl_extended_fab_end_padding_icon</item>
        <item name="android:paddingStart">@dimen/mtrl_extended_fab_start_padding_icon</item>
        <item name="android:paddingEnd">@dimen/mtrl_extended_fab_end_padding_icon</item>
    </style>
    <style name="Widget.MaterialComponents.FloatingActionButton" parent="@style/Widget.Design.FloatingActionButton">
        <item name="android:background">@null</item>
        <item name="backgroundTint">?colorSecondary</item>
        <item name="elevation">@dimen/mtrl_fab_elevation</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="ensureMinTouchTargetSize">true</item>
        <item name="hideMotionSpec">@animator/mtrl_fab_hide_motion_spec</item>
        <item name="hoveredFocusedTranslationZ">@dimen/mtrl_fab_translation_z_hovered_focused</item>
        <item name="pressedTranslationZ">@dimen/mtrl_fab_translation_z_pressed</item>
        <item name="rippleColor">@color/mtrl_fab_ripple_color</item>
        <item name="shapeAppearance">?shapeAppearanceSmallComponent</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton</item>
        <item name="showMotionSpec">@animator/mtrl_fab_show_motion_spec</item>
        <item name="tint">?colorOnSecondary</item>
    </style>
    <style name="Widget.MaterialComponents.Light.ActionBar.Solid" parent="@style/Widget.AppCompat.Light.ActionBar.Solid">
        <item name="android:minHeight">@dimen/mtrl_toolbar_default_height</item>
        <item name="maxButtonHeight">@dimen/mtrl_toolbar_default_height</item>
        <item name="subtitleTextStyle">?textAppearanceSubtitle1</item>
        <item name="titleTextStyle">?textAppearanceHeadline6</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialButtonToggleGroup" parent="@android:style/Widget">
        <item name="singleSelection">false</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar" parent="@android:style/Widget">
        <item name="android:windowFullscreen">false</item>
        <item name="dayInvalidStyle">@style/Widget.MaterialComponents.MaterialCalendar.Day.Invalid</item>
        <item name="daySelectedStyle">@style/Widget.MaterialComponents.MaterialCalendar.Day.Selected</item>
        <item name="dayStyle">@style/Widget.MaterialComponents.MaterialCalendar.Day</item>
        <item name="dayTodayStyle">@style/Widget.MaterialComponents.MaterialCalendar.Day.Today</item>
        <item name="rangeFillColor">@color/mtrl_calendar_selected_range</item>
        <item name="shapeAppearance">?shapeAppearanceMediumComponent</item>
        <item name="yearSelectedStyle">@style/Widget.MaterialComponents.MaterialCalendar.Year.Selected</item>
        <item name="yearStyle">@style/Widget.MaterialComponents.MaterialCalendar.Year</item>
        <item name="yearTodayStyle">@style/Widget.MaterialComponents.MaterialCalendar.Year.Today</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Day" parent="@style/Widget.MaterialComponents.MaterialCalendar.Item">
        <item name="android:height">@dimen/mtrl_calendar_day_height</item>
        <item name="android:width">@dimen/mtrl_calendar_day_width</item>
        <item name="android:insetLeft">@dimen/mtrl_calendar_day_horizontal_padding</item>
        <item name="android:insetRight">@dimen/mtrl_calendar_day_horizontal_padding</item>
        <item name="android:insetTop">@dimen/mtrl_calendar_day_vertical_padding</item>
        <item name="android:insetBottom">@dimen/mtrl_calendar_day_vertical_padding</item>
        <item name="itemShapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Day.Invalid" parent="@style/Widget.MaterialComponents.MaterialCalendar.Day">
        <item name="itemStrokeWidth">0.0dip</item>
        <item name="itemTextColor">@color/material_on_surface_disabled</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Day.Selected" parent="@style/Widget.MaterialComponents.MaterialCalendar.Day">
        <item name="itemFillColor">?colorPrimary</item>
        <item name="itemStrokeWidth">0.0dip</item>
        <item name="itemTextColor">?colorOnPrimary</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Day.Today" parent="@style/Widget.MaterialComponents.MaterialCalendar.Day">
        <item name="itemStrokeColor">@color/material_on_surface_emphasis_high_type</item>
        <item name="itemStrokeWidth">@dimen/mtrl_calendar_day_today_stroke</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.DayTextView" parent="@style/Widget.AppCompat.TextView">
        <item name="android:textAppearance">?textAppearanceCaption</item>
        <item name="android:gravity">center</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Fullscreen" parent="@style/Widget.MaterialComponents.MaterialCalendar">
        <item name="android:windowFullscreen">true</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton" parent="@style/Widget.MaterialComponents.Button.TextButton">
        <item name="android:textColor">@color/mtrl_on_primary_text_btn_text_color_selector</item>
        <item name="rippleColor">@color/mtrl_btn_ripple_color</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderDivider" parent="@android:style/Widget">
        <item name="android:background">?colorOnPrimary</item>
        <item name="android:visibility">gone</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderLayout" parent="@android:style/Widget">
        <item name="android:background">?colorPrimary</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderSelection" parent="@style/Widget.AppCompat.TextView">
        <item name="android:textAppearance">?textAppearanceHeadline4</item>
        <item name="android:textColor">?colorOnPrimary</item>
        <item name="android:ellipsize">end</item>
        <item name="android:maxLines">@integer/mtrl_calendar_selection_text_lines</item>
        <item name="autoSizeMaxTextSize">34.0sp</item>
        <item name="autoSizeMinTextSize">2.0sp</item>
        <item name="autoSizeTextType">uniform</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen" parent="@style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection">
        <item name="android:textAppearance">?textAppearanceHeadline6</item>
        <item name="android:maxLines">1</item>
        <item name="autoSizeMaxTextSize">20.0sp</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderTitle" parent="@style/Widget.AppCompat.TextView">
        <item name="android:textAppearance">?textAppearanceOverline</item>
        <item name="android:textColor">?colorOnPrimary</item>
        <item name="android:ellipsize">end</item>
        <item name="android:maxLines">1</item>
        <item name="autoSizeMaxTextSize">10.0sp</item>
        <item name="autoSizeMinTextSize">2.0sp</item>
        <item name="autoSizeTextType">uniform</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton" parent="@style/Widget.AppCompat.ImageButton">
        <item name="android:background">?actionBarItemBackground</item>
        <item name="android:tint">?colorOnPrimary</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Item" parent="">
        <item name="itemFillColor">@android:color/transparent</item>
        <item name="itemShapeAppearance">?shapeAppearanceSmallComponent</item>
        <item name="itemStrokeColor">@color/mtrl_calendar_item_stroke_color</item>
        <item name="itemStrokeWidth">1.0dip</item>
        <item name="itemTextColor">@color/material_on_surface_emphasis_high_type</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Year" parent="@style/Widget.MaterialComponents.MaterialCalendar.Item">
        <item name="android:height">@dimen/mtrl_calendar_year_height</item>
        <item name="android:width">@dimen/mtrl_calendar_year_width</item>
        <item name="android:insetLeft">@dimen/mtrl_calendar_year_horizontal_padding</item>
        <item name="android:insetRight">@dimen/mtrl_calendar_year_horizontal_padding</item>
        <item name="android:insetTop">@dimen/mtrl_calendar_year_vertical_padding</item>
        <item name="android:insetBottom">@dimen/mtrl_calendar_year_vertical_padding</item>
        <item name="itemShapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Year.Selected" parent="@style/Widget.MaterialComponents.MaterialCalendar.Year">
        <item name="itemFillColor">?colorPrimary</item>
        <item name="itemStrokeColor">?colorOnPrimary</item>
        <item name="itemStrokeWidth">0.0dip</item>
        <item name="itemTextColor">?colorOnPrimary</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Year.Today" parent="@style/Widget.MaterialComponents.MaterialCalendar.Year">
        <item name="itemStrokeColor">@color/material_on_surface_emphasis_high_type</item>
        <item name="itemStrokeWidth">@dimen/mtrl_calendar_day_today_stroke</item>
    </style>
    <style name="Widget.MaterialComponents.NavigationView" parent="@style/Widget.Design.NavigationView">
        <item name="android:background">?colorSurface</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="itemBackground">@null</item>
        <item name="itemHorizontalPadding">@dimen/mtrl_navigation_item_horizontal_padding</item>
        <item name="itemIconPadding">@dimen/mtrl_navigation_item_icon_padding</item>
        <item name="itemIconSize">@dimen/mtrl_navigation_item_icon_size</item>
        <item name="itemIconTint">@color/mtrl_navigation_item_icon_tint</item>
        <item name="itemShapeAppearance">?shapeAppearanceSmallComponent</item>
        <item name="itemShapeFillColor">@color/mtrl_navigation_item_background_color</item>
        <item name="itemShapeInsetBottom">@dimen/mtrl_navigation_item_shape_vertical_margin</item>
        <item name="itemShapeInsetEnd">@dimen/mtrl_navigation_item_shape_horizontal_margin</item>
        <item name="itemShapeInsetStart">@dimen/mtrl_navigation_item_shape_horizontal_margin</item>
        <item name="itemShapeInsetTop">@dimen/mtrl_navigation_item_shape_vertical_margin</item>
        <item name="itemTextAppearance">?textAppearanceSubtitle2</item>
        <item name="itemTextColor">@color/mtrl_navigation_item_text_color</item>
    </style>
    <style name="Widget.MaterialComponents.PopupMenu" parent="@style/Base.Widget.MaterialComponents.PopupMenu">
        <item name="android:popupBackground">?popupMenuBackground</item>
        <item name="android:popupElevation">8.0dip</item>
    </style>
    <style name="Widget.MaterialComponents.PopupMenu.ContextMenu" parent="@style/Base.Widget.MaterialComponents.PopupMenu.ContextMenu" />
    <style name="Widget.MaterialComponents.PopupMenu.ListPopupWindow" parent="@style/Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow" />
    <style name="Widget.MaterialComponents.PopupMenu.Overflow" parent="@style/Base.Widget.MaterialComponents.PopupMenu.Overflow">
        <item name="android:popupBackground">?popupMenuBackground</item>
        <item name="android:popupElevation">8.0dip</item>
    </style>
    <style name="Widget.MaterialComponents.Snackbar" parent="@style/Widget.Design.Snackbar">
        <item name="android:background">@null</item>
        <item name="android:layout_margin">@dimen/mtrl_snackbar_margin</item>
        <item name="actionTextColorAlpha">@dimen/mtrl_snackbar_action_text_color_alpha</item>
        <item name="animationMode">fade</item>
        <item name="backgroundOverlayColorAlpha">@dimen/mtrl_snackbar_background_overlay_color_alpha</item>
    </style>
    <style name="Widget.MaterialComponents.Snackbar.FullWidth" parent="@style/Widget.Design.Snackbar">
        <item name="actionTextColorAlpha">@dimen/mtrl_snackbar_action_text_color_alpha</item>
        <item name="backgroundOverlayColorAlpha">@dimen/mtrl_snackbar_background_overlay_color_alpha</item>
    </style>
    <style name="Widget.MaterialComponents.TabLayout" parent="@style/Widget.Design.TabLayout">
        <item name="android:background">?colorSurface</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="tabIconTint">@color/mtrl_tabs_icon_color_selector</item>
        <item name="tabIndicatorAnimationDuration">@integer/mtrl_tab_indicator_anim_duration_ms</item>
        <item name="tabIndicatorColor">?colorPrimary</item>
        <item name="tabRippleColor">@color/mtrl_tabs_ripple_color</item>
        <item name="tabTextAppearance">?textAppearanceButton</item>
        <item name="tabTextColor">@color/mtrl_tabs_icon_color_selector</item>
        <item name="tabUnboundedRipple">true</item>
    </style>
    <style name="Widget.MaterialComponents.TabLayout.Colored" parent="@style/Widget.MaterialComponents.TabLayout">
        <item name="android:background">?colorPrimary</item>
        <item name="tabIconTint">@color/mtrl_tabs_icon_color_selector_colored</item>
        <item name="tabIndicatorColor">?colorOnPrimary</item>
        <item name="tabRippleColor">@color/mtrl_tabs_colored_ripple_color</item>
        <item name="tabTextColor">@color/mtrl_tabs_icon_color_selector_colored</item>
    </style>
    <style name="Widget.MaterialComponents.TabLayout.PrimarySurface" parent="@style/Widget.MaterialComponents.TabLayout.Colored" />
    <style name="Widget.MaterialComponents.TextInputEditText.FilledBox" parent="@style/Base.Widget.MaterialComponents.TextInputEditText">
        <item name="android:paddingTop">28.0dip</item>
        <item name="android:paddingBottom">12.0dip</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputEditText.FilledBox.Dense" parent="@style/Widget.MaterialComponents.TextInputEditText.FilledBox">
        <item name="android:paddingTop">24.0dip</item>
        <item name="android:paddingBottom">8.0dip</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputEditText.OutlinedBox" parent="@style/Base.Widget.MaterialComponents.TextInputEditText" />
    <style name="Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense" parent="@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox">
        <item name="android:paddingTop">13.0dip</item>
        <item name="android:paddingBottom">13.0dip</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox" parent="@style/Base.Widget.MaterialComponents.TextInputLayout">
        <item name="boxBackgroundColor">@color/mtrl_filled_background_color</item>
        <item name="boxBackgroundMode">filled</item>
        <item name="boxCollapsedPaddingTop">12.0dip</item>
        <item name="boxStrokeColor">@color/mtrl_filled_stroke_color</item>
        <item name="endIconTint">@color/mtrl_filled_icon_tint</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox</item>
        <item name="startIconTint">@color/mtrl_filled_icon_tint</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense" parent="@style/Widget.MaterialComponents.TextInputLayout.FilledBox">
        <item name="boxCollapsedPaddingTop">8.0dip</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu" parent="@style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense">
        <item name="endIconMode">dropdown_menu</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu" parent="@style/Widget.MaterialComponents.TextInputLayout.FilledBox">
        <item name="endIconMode">dropdown_menu</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox" parent="@style/Base.Widget.MaterialComponents.TextInputLayout">
        <item name="boxCollapsedPaddingTop">0.0dip</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense" parent="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu" parent="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense">
        <item name="endIconMode">dropdown_menu</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu" parent="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="endIconMode">dropdown_menu</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox</item>
    </style>
    <style name="Widget.MaterialComponents.TextView" parent="@style/Base.Widget.MaterialComponents.TextView" />
    <style name="Widget.MaterialComponents.Toolbar" parent="@style/Widget.AppCompat.Toolbar">
        <item name="android:minHeight">@dimen/mtrl_toolbar_default_height</item>
        <item name="maxButtonHeight">@dimen/mtrl_toolbar_default_height</item>
        <item name="subtitleTextAppearance">?textAppearanceSubtitle1</item>
        <item name="subtitleTextColor">?android:textColorSecondary</item>
        <item name="titleTextAppearance">?textAppearanceHeadline6</item>
        <item name="titleTextColor">?android:textColorPrimary</item>
    </style>
    <style name="Widget.MaterialComponents.Toolbar.Primary" parent="@style/Widget.MaterialComponents.Toolbar">
        <item name="android:theme">@style/ThemeOverlay.MaterialComponents.Toolbar.Primary</item>
        <item name="android:background">?colorPrimary</item>
        <item name="android:elevation">@dimen/design_appbar_elevation</item>
        <item name="subtitleTextColor">@color/material_on_primary_emphasis_medium</item>
        <item name="titleTextColor">?colorOnPrimary</item>
    </style>
    <style name="Widget.MaterialComponents.Toolbar.PrimarySurface" parent="@style/Widget.MaterialComponents.Toolbar.Primary" />
    <style name="Widget.MaterialComponents.Toolbar.Surface" parent="@style/Widget.MaterialComponents.Toolbar">
        <item name="android:theme">@style/ThemeOverlay.MaterialComponents.Toolbar.Surface</item>
        <item name="android:background">?colorSurface</item>
        <item name="subtitleTextColor">@color/material_on_surface_emphasis_medium</item>
        <item name="titleTextColor">@color/material_on_surface_emphasis_high_type</item>
    </style>
    <style name="Widget.Support.CoordinatorLayout" parent="@android:style/Widget">
        <item name="statusBarBackground">#ff000000</item>
    </style>
    <style name="black_text_style">
        <item name="android:textSize">@dimen/sp_15</item>
        <item name="android:textColor">@color/textColorBlack</item>
    </style>
    <style name="dialog_style" parent="@style/Theme.AppCompat.Light.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:background">@null</item>
        <item name="android:title">@null</item>
        <item name="android:dialogTitle">@null</item>
        <item name="android:windowCloseOnTouchOutside">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="dialogstyleTheme" parent="@style/Theme.AppCompat.Light.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:background">#00000000</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowCloseOnTouchOutside">false</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="dividing_line_style">
        <item name="android:background">@color/colorLightGrayDivider</item>
        <item name="android:layout_width">fill_parent</item>
        <item name="android:layout_height">@dimen/dp_10</item>
    </style>
    <style name="grey_text_style" parent="@style/black_text_style">
        <item name="android:textColor">@color/textColorGray</item>
    </style>
    <style name="light_grey_text_style" parent="@style/black_text_style">
        <item name="android:textColor">@color/textColorGrayA6</item>
    </style>
    <style name="middle_black_text_style" parent="@style/black_text_style">
        <item name="android:textSize">@dimen/sp_14</item>
    </style>
    <style name="middle_grey_text_style" parent="@style/grey_text_style">
        <item name="android:textSize">@dimen/sp_14</item>
    </style>
    <style name="middle_light_grey_text_style" parent="@style/light_grey_text_style">
        <item name="android:textSize">@dimen/sp_14</item>
    </style>
    <style name="middle_theme_color_text_style" parent="@style/theme_color_text_style">
        <item name="android:textSize">@dimen/sp_14</item>
        <item name="android:gravity">center</item>
    </style>
    <style name="middle_white_text_style" parent="@style/white_text_style">
        <item name="android:textSize">@dimen/sp_14</item>
    </style>
    <style name="myCheckBox" parent="@android:style/Widget.Material.CompoundButton.CheckBox">
        <item name="android:textSize">@dimen/sp_14</item>
        <item name="android:textColor">@color/black</item>
        <item name="android:colorControlNormal">@color/gray</item>
        <item name="android:colorControlActivated">@color/primaryColor</item>
        <item name="colorAccent">@color/primaryColor</item>
        <item name="colorControlNormal">@color/gray</item>
    </style>
    <style name="small_grey_text_style" parent="@style/grey_text_style">
        <item name="android:textSize">@dimen/sp_12</item>
    </style>
    <style name="small_light_grey_text_style" parent="@style/light_grey_text_style">
        <item name="android:textSize">@dimen/sp_12</item>
    </style>
    <style name="small_theme_color_text_style" parent="@style/theme_color_text_style">
        <item name="android:textSize">@dimen/sp_12</item>
    </style>
    <style name="style_radio_button">
        <item name="android:textColor">@color/colorThemBlue</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/selector_btn_border2_bg</item>
        <item name="android:visibility">gone</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">fill_parent</item>
        <item name="android:checked">false</item>
        <item name="android:button">@null</item>
    </style>
    <style name="theme_color_text_style" parent="@style/black_text_style">
        <item name="android:textColor">@color/primaryColor</item>
    </style>
    <style name="thin_dividing_line_style" parent="@style/dividing_line_style">
        <item name="android:layout_height">1.0px</item>
    </style>
    <style name="title_black_text_style" parent="@style/title_white_text_style">
        <item name="android:textColor">@color/textColorBlack</item>
    </style>
    <style name="title_white_text_style" parent="@style/white_text_style">
        <item name="android:textSize">@dimen/sp_17</item>
    </style>
    <style name="transparentBgDialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowBackground">@color/transparent</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:background">@color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>
    <style name="video_popup_toast_anim" parent="@android:style/Animation">
        <item name="android:windowEnterAnimation">@android:anim/fade_in</item>
        <item name="android:windowExitAnimation">@android:anim/fade_out</item>
    </style>
    <style name="video_style_dialog_progress" parent="@style/Theme.AppCompat.Light.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowAnimationStyle">@style/video_popup_toast_anim</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>
    <style name="video_vertical_progressBar">
        <item name="android:maxWidth">12.0dip</item>
        <item name="android:indeterminateOnly">false</item>
        <item name="android:indeterminateDrawable">@android:drawable/progress_indeterminate_horizontal</item>
        <item name="android:progressDrawable">@drawable/video_volume_progress_bg</item>
        <item name="android:indeterminateDuration">3500</item>
        <item name="android:indeterminateBehavior">repeat</item>
        <item name="android:minWidth">1.0dip</item>
    </style>
    <style name="white_text_style" parent="@style/black_text_style">
        <item name="android:textColor">@color/white</item>
    </style>
</resources>
