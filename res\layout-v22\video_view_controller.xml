<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center_horizontal" android:orientation="vertical" android:id="@id/layout_bottom" android:background="@drawable/media_gradient_background" android:paddingTop="@dimen/dp_6" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_alignParentBottom="true" app:layout_constraintBottom_toBottomOf="parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:gravity="center" android:orientation="horizontal" android:id="@id/layoutLoop" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="@dimen/dp_45" app:layout_constraintBottom_toTopOf="@id/layout_bottom" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintHorizontal_bias="0.0" app:layout_constraintStart_toStartOf="parent">
        <FrameLayout android:id="@id/flLoopA" android:paddingLeft="@dimen/dp_6" android:paddingTop="@dimen/dp_6" android:paddingRight="@dimen/dp_6" android:paddingBottom="@dimen/dp_6" android:focusable="true" android:clickable="true" android:layout_width="wrap_content" android:layout_height="wrap_content" android:paddingHorizontal="@dimen/dp_6" android:paddingVertical="@dimen/dp_6" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/flFiller" app:layout_constraintTop_toTopOf="parent">
            <TextView android:textSize="@dimen/sp_14" android:textColor="@color/white" android:gravity="center" android:layout_gravity="center" android:id="@id/tvLoopA" android:background="@drawable/shape_black_round_tran" android:paddingLeft="@dimen/dp_10" android:paddingTop="@dimen/dp_6" android:paddingRight="@dimen/dp_10" android:paddingBottom="@dimen/dp_6" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/text_loop_a" android:paddingHorizontal="@dimen/dp_10" android:paddingVertical="@dimen/dp_6" />
        </FrameLayout>
        <FrameLayout android:id="@id/flFiller" android:layout_width="0.0dip" android:layout_height="0.0dip" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" app:layout_constraintWidth_percent="0.01" />
        <FrameLayout android:id="@id/flLoopB" android:paddingLeft="@dimen/dp_6" android:paddingTop="@dimen/dp_6" android:paddingRight="@dimen/dp_6" android:paddingBottom="@dimen/dp_6" android:focusable="true" android:clickable="true" android:layout_width="wrap_content" android:layout_height="wrap_content" android:paddingHorizontal="@dimen/dp_6" android:paddingVertical="@dimen/dp_6" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toEndOf="@id/flFiller" app:layout_constraintTop_toTopOf="parent">
            <TextView android:textSize="@dimen/sp_14" android:textColor="@color/white" android:gravity="center" android:layout_gravity="center" android:id="@id/tvLoopB" android:background="@drawable/shape_black_round_tran" android:paddingLeft="@dimen/dp_10" android:paddingTop="@dimen/dp_6" android:paddingRight="@dimen/dp_10" android:paddingBottom="@dimen/dp_6" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/text_loop_b" android:paddingHorizontal="@dimen/dp_10" android:paddingVertical="@dimen/dp_6" />
        </FrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
    <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/land_progress_line" android:paddingLeft="@dimen/dp_6" android:paddingRight="@dimen/dp_6" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="@dimen/dp_40" android:layout_marginLeft="@dimen/dp_15" android:layout_marginRight="@dimen/dp_15" android:layout_marginBottom="@dimen/dp_6" android:layout_marginHorizontal="@dimen/dp_15" android:paddingHorizontal="@dimen/dp_6">
        <TextView android:textSize="@dimen/sp_14" android:textStyle="bold" android:textColor="@color/white" android:id="@id/time_current" android:layout_width="wrap_content" android:layout_height="wrap_content" android:minWidth="@dimen/dp_40" android:text="00:00" />
        <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="0.0dip" android:layout_height="fill_parent" android:layout_weight="1.0">
            <com.example.myapplication.videoplayer.ABLoopSeekBar android:id="@id/seekbar" android:background="@null" android:layout_width="fill_parent" android:layout_height="fill_parent" android:max="1000" android:progress="300" android:progressDrawable="@drawable/player_setting_bright_progressbar" android:thumb="@drawable/seek_thumb" android:splitTrack="false" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <TextView android:textSize="@dimen/sp_14" android:textStyle="bold" android:textColor="@color/white" android:id="@id/time" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="00:00" />
    </LinearLayout>
    <androidx.appcompat.widget.LinearLayoutCompat android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/port_controller_area" android:paddingLeft="@dimen/dp_15" android:paddingRight="@dimen/dp_15" android:paddingBottom="@dimen/dp_11" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="@dimen/dp_40" android:paddingHorizontal="@dimen/dp_15">
        <ImageView android:id="@id/help_start2" android:paddingLeft="@dimen/dp_6" android:paddingRight="@dimen/dp_6" android:layout_width="@dimen/dp_35" android:layout_height="fill_parent" android:src="@drawable/vvc_ic_media_play" android:scaleType="fitCenter" android:paddingHorizontal="@dimen/dp_6" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
        <ImageView android:id="@id/help_next" android:paddingLeft="@dimen/dp_6" android:paddingRight="@dimen/dp_6" android:visibility="gone" android:layout_width="@dimen/dp_35" android:layout_height="fill_parent" android:src="@drawable/vvc_ic_media_next" android:scaleType="fitCenter" android:layout_marginStart="@dimen/dp_15" android:paddingHorizontal="@dimen/dp_6" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toEndOf="@id/help_start2" app:layout_constraintTop_toTopOf="parent" />
        <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:id="@id/portrait_progress_line" android:layout_width="0.0dip" android:layout_height="fill_parent" android:layout_weight="1.0" android:layout_marginStart="@dimen/dp_6" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/help_start2" app:layout_constraintTop_toTopOf="parent">
            <TextView android:textSize="@dimen/sp_14" android:textColor="#ffffffff" android:id="@id/help_current" android:layout_width="wrap_content" android:layout_height="wrap_content" android:minWidth="@dimen/dp_40" android:text="00:00" android:layout_marginStart="@dimen/dp_6" />
            <androidx.constraintlayout.widget.ConstraintLayout android:layout_width="0.0dip" android:layout_height="fill_parent" android:layout_weight="1.0">
                <com.example.myapplication.videoplayer.ABLoopSeekBar android:id="@id/help_seekbar" android:background="@null" android:layout_width="fill_parent" android:layout_height="fill_parent" android:max="1000" android:progress="300" android:progressDrawable="@drawable/player_setting_bright_progressbar" android:thumb="@drawable/seek_thumb" android:splitTrack="false" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
            <TextView android:textSize="@dimen/sp_14" android:textColor="#ffffffff" android:id="@id/help_total" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="00:00" android:layout_marginEnd="@dimen/dp_2" />
        </LinearLayout>
        <HorizontalScrollView android:id="@id/port_controller_scrollView" android:layout_width="@dimen/dp_125" android:layout_height="fill_parent" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/help_start2" app:layout_constraintTop_toTopOf="parent">
            <androidx.appcompat.widget.LinearLayoutCompat android:gravity="end|center" android:layout_gravity="end|center" android:orientation="horizontal" android:layout_width="wrap_content" android:layout_height="wrap_content">
                <LinearLayout android:id="@id/dirLayout" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="fill_parent">
                    <TextView android:textSize="@dimen/sp_14" android:textColor="#ffffffff" android:gravity="center" android:id="@id/directoryTv" android:paddingLeft="@dimen/dp_6" android:paddingRight="@dimen/dp_6" android:focusable="true" android:clickable="true" android:layout_width="wrap_content" android:layout_height="fill_parent" android:text="@string/directory" android:layout_marginEnd="@dimen/dp_2" android:paddingHorizontal="@dimen/dp_6" />
                </LinearLayout>
                <LinearLayout android:id="@id/ll_subtitle" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="fill_parent">
                    <TextView android:textSize="@dimen/sp_14" android:textColor="#ffffffff" android:gravity="center" android:id="@id/subtitle_switch" android:paddingLeft="@dimen/dp_6" android:paddingRight="@dimen/dp_6" android:focusable="true" android:clickable="true" android:layout_width="wrap_content" android:layout_height="fill_parent" android:minWidth="@dimen/dp_40" android:text="@string/subtitle_off" android:layout_marginEnd="@dimen/dp_2" android:paddingHorizontal="@dimen/dp_6" />
                </LinearLayout>
                <TextView android:textSize="@dimen/sp_14" android:textColor="#ffffffff" android:gravity="center" android:id="@id/speedTv" android:paddingLeft="@dimen/dp_6" android:paddingRight="@dimen/dp_6" android:focusable="true" android:clickable="true" android:layout_width="wrap_content" android:layout_height="fill_parent" android:minWidth="@dimen/dp_50" android:text="1.0X" android:layout_marginEnd="@dimen/dp_2" android:paddingHorizontal="@dimen/dp_6" />
                <ImageView android:id="@id/iv_shortcut" android:paddingLeft="@dimen/dp_6" android:paddingRight="@dimen/dp_6" android:visibility="gone" android:layout_width="@dimen/dp_35" android:layout_height="fill_parent" android:src="@drawable/shortcut" android:scaleType="center" android:layout_marginEnd="@dimen/dp_2" android:paddingHorizontal="@dimen/dp_6" />
                <ImageView android:id="@id/help_fullscreen" android:paddingLeft="@dimen/dp_6" android:paddingRight="@dimen/dp_6" android:layout_width="@dimen/dp_35" android:layout_height="fill_parent" android:src="@drawable/video_switch_open" android:scaleType="center" android:layout_marginEnd="@dimen/dp_2" android:paddingHorizontal="@dimen/dp_6" />
                <ImageView android:id="@id/imgRotate" android:paddingLeft="@dimen/dp_6" android:paddingRight="@dimen/dp_6" android:layout_width="@dimen/dp_35" android:layout_height="fill_parent" android:src="@drawable/icon_ori_ver" android:scaleType="center" android:paddingHorizontal="@dimen/dp_6" />
            </androidx.appcompat.widget.LinearLayoutCompat>
        </HorizontalScrollView>
    </androidx.appcompat.widget.LinearLayoutCompat>
    <LinearLayout android:orientation="vertical" android:id="@id/layoutFloatController" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="@dimen/dp_56">
        <androidx.constraintlayout.widget.ConstraintLayout android:gravity="center" android:paddingTop="@dimen/dp_3" android:paddingBottom="@dimen/dp_3" android:layout_width="fill_parent" android:layout_height="0.0dip" android:layout_weight="1.0" android:paddingVertical="@dimen/dp_3">
            <ImageView android:id="@id/imgPlayLast" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:layout_width="wrap_content" android:layout_height="fill_parent" android:src="@drawable/vvc_ic_media_next" android:rotation="180.0" android:paddingHorizontal="@dimen/dp_10" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/imgPauseStart" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
            <ImageView android:id="@id/imgPauseStart" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:layout_width="wrap_content" android:layout_height="0.0dip" android:src="@drawable/vvc_ic_media_play" android:paddingHorizontal="@dimen/dp_10" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" />
            <ImageView android:id="@id/imgPlayNext" android:paddingLeft="@dimen/dp_10" android:paddingRight="@dimen/dp_10" android:layout_width="wrap_content" android:layout_height="0.0dip" android:src="@drawable/vvc_ic_media_next" android:paddingHorizontal="@dimen/dp_10" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/imgPauseStart" app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
        <ProgressBar android:id="@id/progressFloat" android:layout_width="fill_parent" android:layout_height="wrap_content" android:max="1000" android:progress="250" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" style="@style/Widget.AppCompat.ProgressBar.Horizontal" />
    </LinearLayout>
</LinearLayout>
