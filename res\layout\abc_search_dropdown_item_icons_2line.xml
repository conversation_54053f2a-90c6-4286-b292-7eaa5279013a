<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:layout_width="fill_parent" android:layout_height="58.0dip" style="@style/RtlOverlay.Widget.AppCompat.Search.DropDown"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <ImageView android:id="@android:id/icon1" android:visibility="invisible" android:layout_width="@dimen/abc_dropdownitem_icon_width" android:layout_height="48.0dip" android:scaleType="centerInside" android:layout_alignParentTop="true" android:layout_alignParentBottom="true" style="@style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1" />
    <ImageView android:id="@id/edit_query" android:background="?selectableItemBackground" android:visibility="gone" android:layout_width="48.0dip" android:layout_height="48.0dip" android:scaleType="centerInside" android:layout_alignParentTop="true" android:layout_alignParentBottom="true" style="@style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query" />
    <ImageView android:id="@android:id/icon2" android:visibility="gone" android:layout_width="48.0dip" android:layout_height="48.0dip" android:scaleType="centerInside" android:layout_alignParentTop="true" android:layout_alignParentBottom="true" android:layout_alignWithParentIfMissing="true" style="@style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2" />
    <TextView android:textAppearance="?textAppearanceSearchResultSubtitle" android:gravity="top" android:id="@android:id/text2" android:paddingBottom="4.0dip" android:visibility="gone" android:layout_width="fill_parent" android:layout_height="29.0dip" android:singleLine="true" android:layout_alignParentBottom="true" android:layout_alignWithParentIfMissing="true" style="?android:dropDownItemStyle" />
    <TextView android:textAppearance="?textAppearanceSearchResultTitle" android:id="@android:id/text1" android:layout_width="fill_parent" android:layout_height="wrap_content" android:singleLine="true" android:layout_above="@android:id/text2" android:layout_centerVertical="true" style="?android:dropDownItemStyle" />
</RelativeLayout>
