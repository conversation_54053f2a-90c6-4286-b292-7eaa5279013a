<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:gravity="center_vertical" android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <RelativeLayout android:gravity="center" android:layout_width="@dimen/dp_90" android:layout_height="@dimen/dp_160">
        <LinearLayout android:orientation="vertical" android:id="@id/content" android:background="@drawable/video_dialog_progress_bg" android:layout_width="@dimen/dp_40" android:layout_height="wrap_content">
            <ProgressBar android:layout_gravity="center_horizontal" android:id="@id/volume_progressbar" android:layout_width="@dimen/dp_4" android:layout_height="@dimen/dp_80" android:layout_marginTop="@dimen/dp_16" android:max="100" style="@style/video_vertical_progressBar" />
            <ImageView android:layout_gravity="center_horizontal" android:layout_width="@dimen/dp_24" android:layout_height="@dimen/dp_24" android:layout_marginTop="@dimen/dp_16" android:layout_marginBottom="@dimen/dp_16" android:src="@drawable/video_volumn_bg" />
        </LinearLayout>
    </RelativeLayout>
</LinearLayout>
