<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:gravity="center_horizontal" android:layout_gravity="bottom" android:orientation="vertical" android:background="#cc000000" android:paddingTop="@dimen/dp_6" android:paddingBottom="@dimen/dp_11" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_alignParentBottom="true"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <LinearLayout android:gravity="center_horizontal" android:orientation="vertical" android:id="@id/subtitle_layout" android:layout_width="wrap_content" android:layout_height="fill_parent" android:layout_marginLeft="@dimen/dp_6" android:layout_marginRight="@dimen/dp_6" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
            <androidx.appcompat.widget.AppCompatImageView android:id="@id/iv_subtitle" android:paddingTop="@dimen/dp_12" android:paddingBottom="@dimen/dp_12" android:layout_width="@dimen/dp_60" android:layout_height="@dimen/dp_48" android:src="@drawable/ic_subtitle_off" android:scaleType="fitCenter" />
            <TextView android:textSize="@dimen/sp_14" android:textColor="#ffffffff" android:gravity="center" android:id="@id/tv_subtitle" android:paddingLeft="@dimen/dp_6" android:paddingRight="@dimen/dp_6" android:layout_width="wrap_content" android:layout_height="fill_parent" android:minWidth="@dimen/dp_40" android:text="@string/subtitle_off" />
        </LinearLayout>
        <LinearLayout android:gravity="center_horizontal" android:orientation="vertical" android:id="@id/layoutFloatView" android:layout_width="wrap_content" android:layout_height="fill_parent" android:layout_marginLeft="@dimen/dp_6" android:layout_marginRight="@dimen/dp_6" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
            <androidx.appcompat.widget.AppCompatImageView android:id="@id/ivFloat" android:paddingTop="@dimen/dp_12" android:paddingBottom="@dimen/dp_12" android:layout_width="@dimen/dp_60" android:layout_height="@dimen/dp_48" android:src="@mipmap/icon_float_close" android:scaleType="fitCenter" />
            <TextView android:textSize="@dimen/sp_14" android:textColor="#ffffffff" android:gravity="center" android:id="@id/tvFloat" android:paddingLeft="@dimen/dp_6" android:paddingRight="@dimen/dp_6" android:layout_width="wrap_content" android:layout_height="fill_parent" android:minWidth="@dimen/dp_40" android:text="@string/text_float_view" />
        </LinearLayout>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
