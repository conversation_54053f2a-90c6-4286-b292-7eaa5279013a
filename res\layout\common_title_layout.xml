<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout android:gravity="center_vertical" android:orientation="horizontal" android:background="@color/primaryColor" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingEnd="@dimen/dp_6"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@id/layoutTitle" android:layout_width="fill_parent" android:layout_height="?actionBarSize" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
        <FrameLayout android:id="@id/frameLayout" android:layout_width="wrap_content" android:layout_height="@dimen/dp_35" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent">
            <TextView android:gravity="center_vertical" android:layout_gravity="center_vertical" android:id="@id/backTv" android:visibility="invisible" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/app_name" android:lines="1" android:paddingStart="@dimen/dp_15" style="@style/white_text_style" />
            <ImageView android:layout_gravity="center_vertical" android:id="@id/backIV" android:layout_width="@dimen/dp_50" android:layout_height="@dimen/dp_35" android:src="@drawable/menu_back_bg" android:paddingStart="@dimen/dp_15" android:paddingEnd="@dimen/dp_10" />
        </FrameLayout>
        <TextView android:ellipsize="end" android:gravity="center" android:id="@id/titleTv" android:layout_width="0.0dip" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/dp_50" android:layout_marginRight="@dimen/dp_50" android:lines="1" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/title_white_text_style" />
        <FrameLayout android:id="@id/frameLayout2" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintTop_toTopOf="parent">
            <TextView android:layout_gravity="center|right" android:id="@id/menuTv" android:visibility="invisible" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/setting_title_name" android:lines="1" android:paddingStart="@dimen/dp_10" android:paddingEnd="@dimen/dp_15" style="@style/white_text_style" />
            <ImageView android:layout_gravity="center|right" android:id="@id/menuIV" android:visibility="gone" android:layout_width="@dimen/dp_50" android:layout_height="@dimen/dp_35" android:src="@drawable/menu_set_bg" android:paddingStart="@dimen/dp_15" android:paddingEnd="@dimen/dp_6" />
        </FrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
