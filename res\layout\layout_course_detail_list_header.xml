<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <LinearLayout android:orientation="vertical" android:padding="@dimen/dp_15" android:layout_width="fill_parent" android:layout_height="wrap_content">
        <LinearLayout android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="fill_parent">
            <TextView android:layout_gravity="top" android:id="@id/iv_point" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" app:drawableLeftCompat="@drawable/shape_blue_point" style="@style/middle_black_text_style" />
            <TextView android:layout_gravity="top" android:id="@id/nameTv" android:layout_width="fill_parent" android:layout_height="wrap_content" android:text="" style="@style/middle_black_text_style" />
        </LinearLayout>
        <TextView android:textColor="@color/textColorGray8" android:id="@id/discribleTv" android:paddingTop="@dimen/dp_8" android:paddingBottom="@dimen/dp_8" android:layout_width="fill_parent" android:layout_height="wrap_content" style="@style/middle_grey_text_style" />
        <LinearLayout android:layout_width="fill_parent" android:layout_height="wrap_content">
            <TextView android:id="@id/gkfsTv" android:visibility="visible" android:layout_width="0.0dip" android:layout_height="fill_parent" android:text="@string/gkfs_text" android:maxLines="1" android:layout_weight="1.0" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toStartOf="@id/tvEndTime" app:layout_constraintStart_toStartOf="parent" app:layout_constraintTop_toTopOf="parent" style="@style/small_light_grey_text_style" />
            <TextView android:gravity="start" android:id="@id/tvEndTime" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="fill_parent" android:text="@string/end_time" android:lines="1" app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintEnd_toEndOf="parent" app:layout_constraintStart_toEndOf="@id/gkfsTv" app:layout_constraintTop_toTopOf="parent" style="@style/small_light_grey_text_style" />
        </LinearLayout>
    </LinearLayout>
    <View style="@style/dividing_line_style" />
</LinearLayout>
