<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout android:background="#ff161c22" android:layout_width="fill_parent" android:layout_height="fill_parent"
  xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto">
    <FrameLayout android:background="#ff000000" android:layout_width="fill_parent" android:layout_height="fill_parent">
        <tv.danmaku.ijk.media.player.evplayer.widget.media.IjkVideoView android:layout_gravity="center" android:id="@id/video_view" android:layout_width="fill_parent" android:layout_height="fill_parent">
            <FrameLayout android:layout_gravity="center" android:id="@id/loading_view" android:background="@color/color_light_black" android:visibility="invisible" android:layout_width="fill_parent" android:layout_height="fill_parent">
                <ImageView android:layout_gravity="center" android:id="@id/loading_imageview" android:layout_width="wrap_content" android:layout_height="wrap_content" android:scaleType="fitCenter" />
            </FrameLayout>
        </tv.danmaku.ijk.media.player.evplayer.widget.media.IjkVideoView>
        <tv.danmaku.ijk.media.player.evplayer.utils.TextMarkView android:id="@id/text_mark" android:visibility="visible" android:layout_width="fill_parent" android:layout_height="fill_parent" />
        <ImageView android:layout_gravity="center|right" android:id="@id/btn_shortcut" android:padding="@dimen/dp_8" android:visibility="gone" android:layout_width="@dimen/dp_35" android:layout_height="@dimen/dp_40" android:src="@drawable/shortcut" android:text="@string/screenshot" />
        <TableLayout android:layout_gravity="center|right" android:id="@id/hud_view" android:background="@color/ijk_transparent_dark" android:padding="@dimen/dp_8" android:layout_width="wrap_content" android:layout_height="wrap_content" />
        <tv.danmaku.ijk.media.player.evplayer.widget.media.PlayerToolBar android:id="@id/player_toolbar" android:background="@drawable/toolbar_gradient_background" android:visibility="invisible" android:fitsSystemWindows="true" android:layout_width="fill_parent" android:layout_height="wrap_content" android:minHeight="@dimen/dp_35" app:contentInsetLeft="0.0dip" app:contentInsetStart="0.0dip" app:title="">
            <TextView android:textSize="@dimen/sp_16" android:textColor="#ffffffff" android:gravity="center" android:id="@id/txt_left_title" android:visibility="visible" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/dp_10" android:text="" android:singleLine="true" android:drawableLeft="@drawable/playerbar_back_icon" />
            <TextView android:textSize="@dimen/sp_15" android:textColor="@android:color/white" android:layout_gravity="center" android:id="@id/txt_main_title" android:visibility="visible" android:layout_width="wrap_content" android:layout_height="wrap_content" android:minWidth="@dimen/dp_100" android:text="标题" android:singleLine="true" android:textAlignment="center" />
            <TextView android:textSize="@dimen/sp_16" android:textColor="#ffffffff" android:gravity="center" android:layout_gravity="right" android:id="@id/txt_right_title" android:visibility="invisible" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginRight="@dimen/dp_10" android:drawableRight="@drawable/playerbar_add_icon" />
        </tv.danmaku.ijk.media.player.evplayer.widget.media.PlayerToolBar>
    </FrameLayout>
</RelativeLayout>
