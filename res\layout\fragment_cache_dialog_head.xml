<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <TextView android:textAppearance="@style/TextAppearance.AppCompat.Large" android:textSize="@dimen/sp_14" android:textColor="@color/textColorBlack" android:gravity="center" android:id="@id/text" android:background="@drawable/selector_item_click_bg3" android:paddingTop="@dimen/dp_15" android:paddingBottom="@dimen/dp_5" android:layout_width="fill_parent" android:layout_height="wrap_content"
      xmlns:android="http://schemas.android.com/apk/res/android" />
    <TextView android:textAppearance="@style/TextAppearance.AppCompat.Large" android:textSize="@dimen/sp_12" android:textColor="@color/textColorGray9" android:gravity="center" android:id="@id/desc" android:background="@drawable/selector_item_click_bg3" android:paddingBottom="@dimen/dp_15" android:layout_width="fill_parent" android:layout_height="wrap_content"
      xmlns:android="http://schemas.android.com/apk/res/android" />
    <View android:background="@color/colorLightGrayDivider" android:layout_width="fill_parent" android:layout_height="@dimen/dp_2" />
</androidx.appcompat.widget.LinearLayoutCompat>
