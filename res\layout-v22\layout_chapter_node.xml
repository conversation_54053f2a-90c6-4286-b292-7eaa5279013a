<?xml version="1.0" encoding="utf-8"?>
<LinearLayout android:orientation="vertical" android:layout_width="fill_parent" android:layout_height="wrap_content"
  xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout android:orientation="vertical" android:id="@id/container" android:background="@drawable/selector_item_click_bg" android:paddingTop="@dimen/dp_2" android:paddingBottom="@dimen/dp_2" android:layout_width="fill_parent" android:layout_height="wrap_content" android:paddingVertical="@dimen/dp_2">
        <LinearLayout android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="fill_parent" android:layout_height="wrap_content" android:layout_marginLeft="@dimen/margin_parent" android:layout_marginRight="@dimen/margin_parent" android:minHeight="@dimen/dp_55" android:layout_marginHorizontal="@dimen/margin_parent">
            <ImageView android:id="@id/iv" android:layout_width="@dimen/dp_18" android:layout_height="@dimen/dp_18" android:src="@mipmap/chapter" android:scaleType="fitXY" android:layout_marginEnd="@dimen/dp_10" />
            <TextView android:ellipsize="middle" android:id="@id/nameTv" android:layout_width="0.0dip" android:layout_height="wrap_content" android:singleLine="true" android:layout_weight="1.0" style="@style/middle_grey_text_style" />
            <ImageView android:id="@id/arrowIv" android:layout_width="@dimen/arrow_folder_size" android:layout_height="@dimen/arrow_folder_size" android:layout_margin="@dimen/margin" android:src="@mipmap/retract" android:scaleType="fitXY" />
        </LinearLayout>
        <View android:paddingEnd="@dimen/dp_15" style="@style/thin_dividing_line_style" />
    </LinearLayout>
</LinearLayout>
